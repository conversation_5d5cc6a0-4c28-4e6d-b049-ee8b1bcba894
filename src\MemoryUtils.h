#pragma once
#include <Windows.h>
#include <vector>
#include <string>

class MemoryUtils {
public:
    // Memory protection
    static bool IsMemoryReadable(uintptr_t address, size_t size);
    static bool IsMemoryWritable(uintptr_t address, size_t size);
    static bool IsMemoryExecutable(uintptr_t address, size_t size);
    
    // Memory reading
    template<typename T>
    static bool ReadMemory(uintptr_t address, T& value) {
        if (!IsMemoryReadable(address, sizeof(T))) {
            return false;
        }
        
        __try {
            value = *reinterpret_cast<T*>(address);
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    static bool ReadMemoryBytes(uintptr_t address, void* buffer, size_t size);
    static std::vector<uint8_t> ReadMemoryBytes(uintptr_t address, size_t size);
    
    // Memory writing
    template<typename T>
    static bool WriteMemory(uintptr_t address, const T& value) {
        if (!IsMemoryWritable(address, sizeof(T))) {
            return false;
        }
        
        __try {
            *reinterpret_cast<T*>(address) = value;
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    static bool WriteMemoryBytes(uintptr_t address, const void* buffer, size_t size);
    
    // Module utilities
    static HMODULE GetModuleByName(const std::string& moduleName);
    static uintptr_t GetModuleBase(HMODULE hModule);
    static size_t GetModuleSize(HMODULE hModule);
    static std::string GetModulePath(HMODULE hModule);
    
    // Address utilities
    static bool IsValidAddress(uintptr_t address);
    static uintptr_t ResolveRelativeAddress(uintptr_t instructionAddress, int offset = 0);
    static std::string FormatAddress(uintptr_t address);
    
    // Process utilities
    static DWORD GetCurrentProcessId();
    static std::string GetProcessName();
    static std::vector<HMODULE> GetLoadedModules();
    
private:
    MemoryUtils() = delete;
};
