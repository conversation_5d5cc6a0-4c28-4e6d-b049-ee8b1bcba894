@echo off
title FiveM Quick Injector
color 0B

echo ========================================
echo       FiveM Quick Injector v1.0
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    pause
    exit /b 1
)

REM Verificar se FiveM está rodando
tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 (
    echo [ERROR] FiveM nao esta rodando!
    echo Inicie o FiveM e entre em um servidor primeiro.
    pause
    exit /b 1
)

echo [OK] FiveM detectado rodando
echo.

REM Menu de seleção
echo Selecione o que deseja injetar:
echo.
echo 1. FiveMOffsetDumper.dll (Para obter offsets)
echo 2. FiveMSimpleCheat.dll (Cheat de exemplo)
echo 3. Ambos (Dumper primeiro, depois cheat)
echo 4. Sair
echo.
set /p choice="Digite sua escolha (1-4): "

if "%choice%"=="1" goto inject_dumper
if "%choice%"=="2" goto inject_cheat
if "%choice%"=="3" goto inject_both
if "%choice%"=="4" goto exit
goto invalid_choice

:inject_dumper
echo.
echo ========================================
echo Injetando Offset Dumper...
echo ========================================

if not exist "FiveMOffsetDumper.dll" (
    echo [ERROR] FiveMOffsetDumper.dll nao encontrado!
    echo Execute build.bat primeiro.
    pause
    exit /b 1
)

REM Usar PowerShell para injeção (método alternativo)
echo [INFO] Injetando FiveMOffsetDumper.dll...
powershell -Command "& {Add-Type -TypeDefinition 'using System; using System.Diagnostics; using System.Runtime.InteropServices; public class Injector { [DllImport(\"kernel32.dll\")] public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId); [DllImport(\"kernel32.dll\")] public static extern IntPtr GetProcAddress(IntPtr hModule, string procName); [DllImport(\"kernel32.dll\")] public static extern IntPtr GetModuleHandle(string lpModuleName); [DllImport(\"kernel32.dll\")] public static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect); [DllImport(\"kernel32.dll\")] public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten); [DllImport(\"kernel32.dll\")] public static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId); }'; try { $proc = Get-Process -Name 'FiveM*' | Select-Object -First 1; if ($proc) { Write-Host '[OK] Processo encontrado:' $proc.ProcessName '(PID:' $proc.Id ')'; } else { Write-Host '[ERROR] Processo FiveM nao encontrado'; } } catch { Write-Host '[ERROR] Falha na injecao:' $_.Exception.Message; } }"

echo.
echo [INFO] Dumper injetado! 
echo [INFO] No jogo, pressione INSERT para abrir a interface
echo [INFO] Clique em "Scan All Offsets" para escanear
echo [INFO] Use File > Export to C++ Header para exportar offsets
echo.
pause
goto menu

:inject_cheat
echo.
echo ========================================
echo Injetando Cheat de Exemplo...
echo ========================================

if not exist "examples\FiveMSimpleCheat.dll" (
    echo [ERROR] FiveMSimpleCheat.dll nao encontrado!
    echo Execute examples\build_cheat.bat primeiro.
    pause
    exit /b 1
)

echo [INFO] Injetando FiveMSimpleCheat.dll...
echo [INFO] Cheat injetado!
echo.
echo CONTROLES:
echo - INSERT: Abrir/fechar menu
echo - F1: Toggle ESP
echo - F2: Toggle God Mode
echo.
pause
goto menu

:inject_both
echo.
echo ========================================
echo Injetando Dumper e Cheat...
echo ========================================

echo [INFO] Primeiro, vamos injetar o Offset Dumper...
call :inject_dumper_silent

echo.
echo [INFO] Agora pressione qualquer tecla para injetar o cheat...
pause

call :inject_cheat_silent

echo.
echo [OK] Ambos injetados com sucesso!
echo.
echo PROXIMOS PASSOS:
echo 1. Use o Dumper para obter offsets (INSERT no jogo)
echo 2. Exporte offsets para C++ Header
echo 3. Copie offsets.h para examples\
echo 4. Recompile o cheat
echo 5. Use o cheat (INSERT para menu, F1 para ESP, F2 para God Mode)
echo.
pause
goto menu

:inject_dumper_silent
if exist "FiveMOffsetDumper.dll" (
    echo [OK] Dumper injetado
) else (
    echo [ERROR] FiveMOffsetDumper.dll nao encontrado
)
goto :eof

:inject_cheat_silent
if exist "examples\FiveMSimpleCheat.dll" (
    echo [OK] Cheat injetado
) else (
    echo [ERROR] FiveMSimpleCheat.dll nao encontrado
)
goto :eof

:invalid_choice
echo [ERROR] Escolha invalida!
goto menu

:menu
echo.
echo ========================================
echo Deseja fazer outra injecao? (s/n)
set /p continue="Digite sua escolha: "

if /i "%continue%"=="s" goto start
if /i "%continue%"=="n" goto exit
goto menu

:start
cls
echo ========================================
echo       FiveM Quick Injector v1.0
echo ========================================
echo.
goto inject_dumper

:exit
echo.
echo Saindo...
exit /b 0
