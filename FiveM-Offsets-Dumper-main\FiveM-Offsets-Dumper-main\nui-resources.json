{"nui-resources.dll": {"<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>::~<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>": {"offset": "0x12130"}, "<lambda_4b5991f435073e3c82f6aa21eef9cb28>::~<lambda_4b5991f435073e3c82f6aa21eef9cb28>": {"offset": "0x1EB10"}, "<lambda_7563642f228337b5e4d62922b583c158>::~<lambda_7563642f228337b5e4d62922b583c158>": {"offset": "0x1EB40"}, "<lambda_77ed9a324e4b2abd68f43d246a32c7f7>::<lambda_77ed9a324e4b2abd68f43d246a32c7f7>": {"offset": "0x14790"}, "<lambda_77ed9a324e4b2abd68f43d246a32c7f7>::~<lambda_77ed9a324e4b2abd68f43d246a32c7f7>": {"offset": "0x14C20"}, "<lambda_a3e97fbfaf2498451df291433411d41d>::~<lambda_a3e97fbfaf2498451df291433411d41d>": {"offset": "0x1EB10"}, "<lambda_edbaec5da03210d3627a1fabcf18376a>::~<lambda_edbaec5da03210d3627a1fabcf18376a>": {"offset": "0x1EB40"}, "<lambda_eedc40a2521921f3ba689555a00d36bc>::~<lambda_eedc40a2521921f3ba689555a00d36bc>": {"offset": "0x2B200"}, "??@c4078e551752f79e991c64c10607d245@": {"offset": "0x15270"}, "CefAddCrossOriginWhitelistEntry": {"offset": "0x37F50"}, "CefBaseRefCounted::~CefBaseRefCounted": {"offset": "0x125F0"}, "CefParseURL": {"offset": "0x38090"}, "CefResourceHandler::Open": {"offset": "0x10F40"}, "CefResourceHandler::ProcessRequest": {"offset": "0x10FA0"}, "CefResourceHandler::Read": {"offset": "0x11040"}, "CefResourceHandler::ReadResponse": {"offset": "0x11080"}, "CefResourceHandler::Skip": {"offset": "0x11000"}, "CefStringBase<CefStringTraitsUTF16>::CefStringBase<CefStringTraitsUTF16>": {"offset": "0x11FA0"}, "CefStringBase<CefStringTraitsUTF16>::ToString": {"offset": "0x12E10"}, "CefStringBase<CefStringTraitsUTF16>::compare": {"offset": "0x13090"}, "CefStringBase<CefStringTraitsUTF16>::~CefStringBase<CefStringTraitsUTF16>": {"offset": "0x12190"}, "CefStructBase<CefMouseEventTraits>::~CefStructBase<CefMouseEventTraits>": {"offset": "0x2B260"}, "CefStructBase<CefURLPartsTraits>::~CefStructBase<CefURLPartsTraits>": {"offset": "0x121E0"}, "CfxState::CfxState": {"offset": "0x34940"}, "CleanURL": {"offset": "0x2D8B0"}, "Component::As": {"offset": "0xE4D0"}, "Component::IsA": {"offset": "0xE590"}, "Component::SetCommandLine": {"offset": "0xA3F0"}, "Component::SetUserData": {"offset": "0xE5A0"}, "ComponentInstance::DoGameLoad": {"offset": "0xE570"}, "ComponentInstance::Initialize": {"offset": "0xE580"}, "ComponentInstance::Shutdown": {"offset": "0xE5A0"}, "ConvertToJSON": {"offset": "0x20CD0"}, "ConvertToMsgPack": {"offset": "0x21760"}, "CoreGetComponentRegistry": {"offset": "0xB620"}, "CoreGetGlobalInstanceRegistry": {"offset": "0xB6B0"}, "CreateComponent": {"offset": "0xE5B0"}, "DllMain": {"offset": "0x396EC"}, "DloadAcquireSectionWriteAccess": {"offset": "0x398BC"}, "DloadGetSRWLockFunctionPointers": {"offset": "0x39968"}, "DloadMakePermanentImageCommit": {"offset": "0x39A08"}, "DloadObtainSection": {"offset": "0x39AA0"}, "DloadProtectSection": {"offset": "0x39B3C"}, "DloadReleaseSectionWriteAccess": {"offset": "0x39BCC"}, "DoNtRaiseException": {"offset": "0x36E50"}, "FatalErrorNoExceptRealV": {"offset": "0xBAC0"}, "FatalErrorRealV": {"offset": "0xBAF0"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x2220"}, "GetAbsoluteCitPath": {"offset": "0x351E0"}, "GlobalErrorHandler": {"offset": "0xBD30"}, "Header::~Header": {"offset": "0x1EC90"}, "HookFunctionBase::RunAll": {"offset": "0x37C70"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x344A0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x34A00"}, "HostSharedData<ReverseGameData>::HostSharedData<ReverseGameData>": {"offset": "0x2A7C0"}, "IScriptRuntimeHandler::GetIID": {"offset": "0x22390"}, "InitFunction::Run": {"offset": "0xE5E0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x36BB0"}, "InitFunctionBase::Register": {"offset": "0x36FC0"}, "InitFunctionBase::RunAll": {"offset": "0x37010"}, "MakeRelativeCitPath": {"offset": "0xC3D0"}, "MakeUICallback": {"offset": "0x22510"}, "RPCResourceHandler::AddRef": {"offset": "0x12A60"}, "RPCResourceHandler::Cancel": {"offset": "0xA3F0"}, "RPCResourceHandler::GetResponseHeaders": {"offset": "0x10160"}, "RPCResourceHandler::HasAtLeastOneRef": {"offset": "0x12D60"}, "RPCResourceHandler::HasOneRef": {"offset": "0x12D90"}, "RPCResourceHandler::ProcessRequest": {"offset": "0xE5F0"}, "RPCResourceHandler::RPCResourceHandler": {"offset": "0x12020"}, "RPCResourceHandler::ReadResponse": {"offset": "0x10DD0"}, "RPCResourceHandler::Release": {"offset": "0x12DB0"}, "RaiseDebugException": {"offset": "0x36F30"}, "RegisterNuiCallback<0>": {"offset": "0x1C410"}, "RegisterNuiCallback<1>": {"offset": "0x1C1B0"}, "RequestWrap::~RequestWrap": {"offset": "0x25220"}, "ResourceUI::AddCallback": {"offset": "0x16A70"}, "ResourceUI::Create": {"offset": "0x16CC0"}, "ResourceUI::Destroy": {"offset": "0x182F0"}, "ResourceUI::HasCallbacks": {"offset": "0x12D70"}, "ResourceUI::HasFrame": {"offset": "0x12D80"}, "ResourceUI::InvokeCallback": {"offset": "0x18320"}, "ResourceUI::IsDead": {"offset": "0x12DA0"}, "ResourceUI::RemoveCallback": {"offset": "0x18EC0"}, "ResourceUI::ResourceUI": {"offset": "0x14BB0"}, "ResourceUI::SetHasCallbacks": {"offset": "0x12E00"}, "ResourceUI::SignalPoll": {"offset": "0x18F00"}, "ResourceUI::~ResourceUI": {"offset": "0x15200"}, "ReverseGameData::ReverseGameData": {"offset": "0x2B100"}, "ScopedError::~ScopedError": {"offset": "0xA5E0"}, "SysError": {"offset": "0xC950"}, "ToNarrow": {"offset": "0x37040"}, "ToWide": {"offset": "0x37130"}, "TraceRealV": {"offset": "0x37440"}, "Win32TrapAndJump64": {"offset": "0x37D10"}, "_DllMainCRTStartup": {"offset": "0x390C0"}, "_Init_thread_abort": {"offset": "0x383E0"}, "_Init_thread_footer": {"offset": "0x38410"}, "_Init_thread_header": {"offset": "0x38470"}, "_Init_thread_notify": {"offset": "0x384D8"}, "_Init_thread_wait": {"offset": "0x3851C"}, "_RTC_Initialize": {"offset": "0x39758"}, "_RTC_Terminate": {"offset": "0x39794"}, "__ArrayUnwind": {"offset": "0x38CC8"}, "__GSHandlerCheck": {"offset": "0x38AFC"}, "__GSHandlerCheckCommon": {"offset": "0x38B1C"}, "__GSHandlerCheck_EH": {"offset": "0x38B78"}, "__GSHandlerCheck_SEH": {"offset": "0x3927C"}, "__chkstk": {"offset": "0x38D50"}, "__crt_debugger_hook": {"offset": "0x394B8"}, "__delayLoadHelper2": {"offset": "0x39C60"}, "__dyn_tls_init": {"offset": "0x38944"}, "__dyn_tls_on_demand_init": {"offset": "0x389AC"}, "__isa_available_init": {"offset": "0x3930C"}, "__local_stdio_printf_options": {"offset": "0xE3B0"}, "__local_stdio_scanf_options": {"offset": "0x3972C"}, "__raise_securityfailure": {"offset": "0x39100"}, "__report_gsfailure": {"offset": "0x39134"}, "__scrt_acquire_startup_lock": {"offset": "0x385C4"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x38600"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x38634"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x3864C"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x38674"}, "__scrt_dllmain_exception_filter": {"offset": "0x3868C"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x386EC"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x3871C"}, "__scrt_fastfail": {"offset": "0x394C0"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x39750"}, "__scrt_initialize_crt": {"offset": "0x38730"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x39734"}, "__scrt_initialize_onexit_tables": {"offset": "0x3877C"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x382E8"}, "__scrt_initialize_type_info": {"offset": "0x39710"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x38808"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x3BEDC"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x39634"}, "__scrt_release_startup_lock": {"offset": "0x388A0"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE5A0"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE5A0"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE5A0"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE5A0"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE5A0"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE4D0"}, "__scrt_throw_std_bad_alloc": {"offset": "0x3960C"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD150"}, "__scrt_uninitialize_crt": {"offset": "0x388C4"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x383B8"}, "__scrt_uninitialize_type_info": {"offset": "0x39720"}, "__security_check_cookie": {"offset": "0x38C10"}, "__security_init_cookie": {"offset": "0x39640"}, "__std_find_trivial_1": {"offset": "0x38160"}, "_get_startup_argv_mode": {"offset": "0x3962C"}, "_guard_check_icall_nop": {"offset": "0xA3F0"}, "_guard_dispatch_icall_nop": {"offset": "0x39F70"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x39F90"}, "_onexit": {"offset": "0x388F0"}, "_wwassert": {"offset": "0x354E0"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x3BF20"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x3BF7F"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x3BF96"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x3BFAF"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x3BFC3"}, "`dynamic initializer for 'HostSharedData<ReverseGameData>::m_fakeData''": {"offset": "0x1810"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_24''": {"offset": "0x1310"}, "`dynamic initializer for '_init_instance_25''": {"offset": "0x1340"}, "`dynamic initializer for '_init_instance_26''": {"offset": "0x1370"}, "`dynamic initializer for '_init_instance_27''": {"offset": "0x13A0"}, "`dynamic initializer for '_init_instance_28''": {"offset": "0x13D0"}, "`dynamic initializer for '_init_instance_29''": {"offset": "0x1640"}, "`dynamic initializer for '_init_instance_30''": {"offset": "0x1670"}, "`dynamic initializer for '_init_instance_31''": {"offset": "0x1400"}, "`dynamic initializer for 'g_nuiCallbackMutex''": {"offset": "0x1430"}, "`dynamic initializer for 'g_nuiCallbackQueue''": {"offset": "0x1460"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'httpInitFunction''": {"offset": "0x14A0"}, "`dynamic initializer for 'initFunction''": {"offset": "0x14E0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1A70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>,<lambda_4b5991f435073e3c82f6aa21eef9cb28> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE60"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >,<lambda_7563642f228337b5e4d62922b583c158> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >,<lambda_7563642f228337b5e4d62922b583c158> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>,<lambda_77ed9a324e4b2abd68f43d246a32c7f7> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x15250"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>,<lambda_77ed9a324e4b2abd68f43d246a32c7f7> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x15250"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>,<lambda_a3e97fbfaf2498451df291433411d41d> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE60"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >,<lambda_edbaec5da03210d3627a1fabcf18376a> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >,<lambda_edbaec5da03210d3627a1fabcf18376a> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EE80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >,<lambda_eedc40a2521921f3ba689555a00d36bc> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x2B490"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >,<lambda_eedc40a2521921f3ba689555a00d36bc> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x2B490"}, "atexit": {"offset": "0x3892C"}, "boost::algorithm::to_lower_copy<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x24FB0"}, "capture_previous_context": {"offset": "0x39208"}, "cef::logging::LogMessage::LogMessage": {"offset": "0x37DE0"}, "cef::logging::LogMessage::~LogMessage": {"offset": "0x37E40"}, "debug::Alias": {"offset": "0xA3F0"}, "dllmain_crt_dispatch": {"offset": "0x38DA0"}, "dllmain_crt_process_attach": {"offset": "0x38DF0"}, "dllmain_crt_process_detach": {"offset": "0x38F08"}, "dllmain_dispatch": {"offset": "0x38F8C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD790"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA4B0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x33CC0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x33370"}, "fmt::v8::detail::add_compare": {"offset": "0x334F0"}, "fmt::v8::detail::assert_fail": {"offset": "0x33630"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x33680"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x33850"}, "fmt::v8::detail::bigint::square": {"offset": "0x34090"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x33370"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2CC0"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x34350"}, "fmt::v8::detail::compare": {"offset": "0x337B0"}, "fmt::v8::detail::count_digits": {"offset": "0xD570"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x2FEC0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2D70"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x33B90"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x31D90"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x33E70"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x31DC0"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x32A80"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x32650"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x33DF0"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x2FFD0"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x2FFD0"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x33B20"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2DA0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x31480"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3070"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x31360"}, "fmt::v8::detail::format_float<double>": {"offset": "0x2FA10"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x315C0"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x3150"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x31920"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3270"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x33D0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x3630"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE300"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x31FD0"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x32250"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x324E0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA510"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3700"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE140"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4D00"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5D00"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x58B0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x6140"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x330E0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x32FC0"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x57E0"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6580"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x65C0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6750"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x7110"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6B20"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA0A0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7840"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7C60"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7E30"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7FD0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7FD0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8160"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x8380"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x8500"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9C90"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8690"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x88B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8B50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8D70"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8EF0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x9110"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x9330"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x94B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x96D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9850"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9A70"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9E20"}, "fmt::v8::format_error::format_error": {"offset": "0xA2A0"}, "fmt::v8::format_error::~format_error": {"offset": "0xA640"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x361E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3950"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3820"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3730"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3A40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3CA0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4800"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4230"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x2A6A0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x36A80"}, "fprintf": {"offset": "0xE3C0"}, "fwEvent<>::ConnectInternal": {"offset": "0x12A70"}, "fwEvent<char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::ConnectInternal": {"offset": "0x12A70"}, "fwEvent<fx::Resource *>::ConnectInternal": {"offset": "0x12A70"}, "fwEvent<fx::ResourceManager *>::ConnectInternal": {"offset": "0x12A70"}, "fwEvent<std::function<void __cdecl(bool,char const *,unsigned __int64)> >::ConnectInternal": {"offset": "0x12A70"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA660"}, "fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >::~fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >": {"offset": "0x12370"}, "fwRefContainer<ResourceUI>::~fwRefContainer<ResourceUI>": {"offset": "0x12370"}, "fwRefContainer<fwRefCountable>::~fwRefContainer<fwRefCountable>": {"offset": "0x12370"}, "fwRefContainer<fx::Resource>::~fwRefContainer<fx::Resource>": {"offset": "0x12370"}, "fwRefContainer<fx::ResourceCallbackComponent>::~fwRefContainer<fx::ResourceCallbackComponent>": {"offset": "0x12370"}, "fwRefContainer<fx::ResourceEventComponent>::~fwRefContainer<fx::ResourceEventComponent>": {"offset": "0x12370"}, "fwRefContainer<fx::ResourceMetaDataComponent>::~fwRefContainer<fx::ResourceMetaDataComponent>": {"offset": "0x12370"}, "fwRefCountable::AddRef": {"offset": "0x37C30"}, "fwRefCountable::Release": {"offset": "0x37C40"}, "fwRefCountable::~fwRefCountable": {"offset": "0x37C20"}, "fx::GetCurrentScriptRuntime<IScriptRuntime>": {"offset": "0x1A390"}, "fx::OMPtr<IScriptRuntime>::~OMPtr<IScriptRuntime>": {"offset": "0x1EBF0"}, "fx::ResourceCallbackComponent::CallbackRef::~CallbackRef": {"offset": "0xA510"}, "fx::ResourceEventComponent::QueueEvent2<msgpack::v2::object,fx::ResourceCallbackComponent::CallbackRef>": {"offset": "0x1BE40"}, "fx::ResourceManager::CallReferenceUnpacked<void,RequestWrap,fx::ResourceCallbackComponent::CallbackRef>": {"offset": "0x23DD0"}, "fx::ResourceManager::CallReferenceUnpacked<void,msgpack::v2::object,fx::ResourceCallbackComponent::CallbackRef>": {"offset": "0x1A050"}, "fx::ScriptContext::CheckArgument<char const *>": {"offset": "0x1A310"}, "fxCreateObjectInstance": {"offset": "0x37CA0"}, "launch::IsSDKGuest": {"offset": "0x2E630"}, "msgpack::v1::adaptor::object_with_zone<msgpack::v2::object,void>::object_with_zone_visitor::~object_with_zone_visitor": {"offset": "0x1EDC0"}, "msgpack::v1::container_size_overflow::container_size_overflow": {"offset": "0x1EA40"}, "msgpack::v1::container_size_overflow::~container_size_overflow": {"offset": "0xA640"}, "msgpack::v1::object::as<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1D230"}, "msgpack::v1::object::as<std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,msgpack::v2::object,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> > > >": {"offset": "0x24940"}, "msgpack::v1::object::convert<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1D2C0"}, "msgpack::v1::object_pack_visitor<msgpack::v1::sbuffer>::visit_boolean": {"offset": "0x23970"}, "msgpack::v1::object_parser::parse<msgpack::v1::adaptor::object_with_zone<msgpack::v2::object,void>::object_with_zone_visitor>": {"offset": "0x1E380"}, "msgpack::v1::object_parser::parse<msgpack::v1::object_pack_visitor<msgpack::v1::sbuffer> >": {"offset": "0x1DD70"}, "msgpack::v1::object_parser::~object_parser": {"offset": "0x1EEA0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x249D0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_array": {"offset": "0x22B60"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_bin": {"offset": "0x22D80"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_ext": {"offset": "0x22FB0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_int64<__int64>": {"offset": "0x1D470"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint64<unsigned __int64>": {"offset": "0x1D9E0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_map": {"offset": "0x23480"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_str": {"offset": "0x236A0"}, "msgpack::v1::sbuffer::write": {"offset": "0x23AB0"}, "msgpack::v1::sbuffer::~sbuffer": {"offset": "0x1EEB0"}, "msgpack::v1::type_error::type_error": {"offset": "0x1EAE0"}, "msgpack::v1::type_error::~type_error": {"offset": "0xA640"}, "msgpack::v1::zone::allocate_align": {"offset": "0x22910"}, "msgpack::v1::zone::allocate_expand": {"offset": "0x229D0"}, "msgpack::v1::zone::get_aligned": {"offset": "0x22AF0"}, "msgpack::v1::zone::~zone": {"offset": "0x1EEC0"}, "msgpack::v2::object::object<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19FA0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1EE20"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x220C0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x221D0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::GenericStringStream<rapidjson::UTF8<char> > >": {"offset": "0x1B3E0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA320"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1EE50"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1A4C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::GenericStringStream<rapidjson::UTF8<char> > >": {"offset": "0x1A6F0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1A7A0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1AFE0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x1B5C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1B940"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x225E0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA3C0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::GetString": {"offset": "0x223B0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA3C0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1C30"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB490"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Begin": {"offset": "0x20C90"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetString": {"offset": "0x22450"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetStringLength": {"offset": "0x224B0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::SetStringRaw": {"offset": "0x22740"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA3F0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC5E0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Realloc": {"offset": "0x22670"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC6A0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC910"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xCDB0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA400"}, "rapidjson::internal::DigitGen": {"offset": "0xB740"}, "rapidjson::internal::FastPath": {"offset": "0x222E0"}, "rapidjson::internal::Grisu2": {"offset": "0xC1F0"}, "rapidjson::internal::Prettify": {"offset": "0xC750"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x1BD20"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x26C0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2790"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA3C0"}, "rapidjson::internal::StreamLocalCopy<rapidjson::GenericStringStream<rapidjson::UTF8<char> >,1>::~StreamLocalCopy<rapidjson::GenericStringStream<rapidjson::UTF8<char> >,1>": {"offset": "0x1EC30"}, "rapidjson::internal::WriteExponent": {"offset": "0xCD20"}, "rapidjson::internal::u32toa": {"offset": "0xD880"}, "rapidjson::internal::u64toa": {"offset": "0xDAF0"}, "scoped_refptr<CefBrowserHost>::~scoped_refptr<CefBrowserHost>": {"offset": "0x124A0"}, "scoped_refptr<CefCallback>::~scoped_refptr<CefCallback>": {"offset": "0x124A0"}, "scoped_refptr<CefPostData>::~scoped_refptr<CefPostData>": {"offset": "0x124A0"}, "scoped_refptr<CefRequest>::~scoped_refptr<CefRequest>": {"offset": "0x124A0"}, "scoped_refptr<CefResponse>::~scoped_refptr<CefResponse>": {"offset": "0x124A0"}, "scoped_refptr<RPCResourceHandler>::~scoped_refptr<RPCResourceHandler>": {"offset": "0x124A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x2B290"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > >,void *> > >": {"offset": "0x2B270"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<void * const,scrBindPointer *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<void * const,scrBindPointer *>,void *> > >": {"offset": "0x2B270"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >": {"offset": "0x12280"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,msgpack::v2::object>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,msgpack::v2::object>,void *> > >": {"offset": "0xA430"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>,void *> > >": {"offset": "0x1EC40"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x122A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >,void *> > >": {"offset": "0x14DB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA430"}, "std::_Default_allocator_traits<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::destroy<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x11E60"}, "std::_Destroy_in_place<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xA510"}, "std::_Destroy_range<std::allocator<Header> >": {"offset": "0x24320"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x13E70"}, "std::_Destroy_range<std::allocator<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > >": {"offset": "0x13F00"}, "std::_Facet_Register": {"offset": "0x38298"}, "std::_Func_class<bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::~_Func_class<bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>": {"offset": "0x122C0"}, "std::_Func_class<bool,fx::Resource *>::~_Func_class<bool,fx::Resource *>": {"offset": "0x122C0"}, "std::_Func_class<bool,fx::ResourceManager *>::~_Func_class<bool,fx::ResourceManager *>": {"offset": "0x122C0"}, "std::_Func_class<bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::~_Func_class<bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >": {"offset": "0x122C0"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x122C0"}, "std::_Func_class<void,bool,char const *,unsigned __int64>::~_Func_class<void,bool,char const *,unsigned __int64>": {"offset": "0x122C0"}, "std::_Func_class<void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x122C0"}, "std::_Func_class<void,msgpack::v1::object_handle const &>::~_Func_class<void,msgpack::v1::object_handle const &>": {"offset": "0x122C0"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >": {"offset": "0x122C0"}, "std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >::~_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >": {"offset": "0x122C0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x122C0"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x11260"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x11340"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x11320"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x112F0"}, "std::_Func_impl_no_alloc<<lambda_0b99ff1a0f6576430fe4d38d6f2f578c>,void,int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x11330"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27C00"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27C10"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Move": {"offset": "0x27C00"}, "std::_Func_impl_no_alloc<<lambda_0d398998f5cf0baaed61f903f8d807d3>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27C20"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27C30"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27C50"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Move": {"offset": "0x27C30"}, "std::_Func_impl_no_alloc<<lambda_148aacf61cb8421e260645f33a06413c>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27CC0"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Copy": {"offset": "0x13910"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Do_call": {"offset": "0x13930"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Move": {"offset": "0x13910"}, "std::_Func_impl_no_alloc<<lambda_1b9b882442f4f0238f0ad6db27cfb677>,bool>::_Target_type": {"offset": "0x139B0"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Copy": {"offset": "0x136E0"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Do_call": {"offset": "0x13700"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Move": {"offset": "0x136E0"}, "std::_Func_impl_no_alloc<<lambda_1ef028ec87bf57f7455cb3caafc0b566>,bool>::_Target_type": {"offset": "0x13720"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Copy": {"offset": "0x139C0"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Do_call": {"offset": "0x13930"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Move": {"offset": "0x139C0"}, "std::_Func_impl_no_alloc<<lambda_209372ef5ac4289acb2d2b25aad41cbf>,bool>::_Target_type": {"offset": "0x139E0"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Copy": {"offset": "0x13730"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Delete_this": {"offset": "0x111B0"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Do_call": {"offset": "0x13750"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Move": {"offset": "0x13730"}, "std::_Func_impl_no_alloc<<lambda_2272712a9a9471030629799e626768b4>,void,fwRefContainer<fx::Resource> const &>::_Target_type": {"offset": "0x137D0"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27F00"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27F20"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Move": {"offset": "0x27F00"}, "std::_Func_impl_no_alloc<<lambda_26f148efe7cdfc9b780265b03ae127d6>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27FB0"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Copy": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x23D70"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Move": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_2cb33fa2674c5206284536a778aeb0f8>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x23D80"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Copy": {"offset": "0x110F0"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Delete_this": {"offset": "0x111B0"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Do_call": {"offset": "0x11110"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Move": {"offset": "0x110F0"}, "std::_Func_impl_no_alloc<<lambda_2d048217d148e8d4ee446306febeb985>,void,fwRefContainer<fx::Resource> const &>::_Target_type": {"offset": "0x11190"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Copy": {"offset": "0x139F0"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Do_call": {"offset": "0x13A10"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Move": {"offset": "0x139F0"}, "std::_Func_impl_no_alloc<<lambda_2f9205b6252d6e868aeec98f84dc9977>,bool,fx::Resource *>::_Target_type": {"offset": "0x13A30"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27BD0"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27BE0"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Move": {"offset": "0x27BD0"}, "std::_Func_impl_no_alloc<<lambda_35e9b35287634c6ab41fa6822086d68e>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27BF0"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Copy": {"offset": "0x13880"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Do_call": {"offset": "0x138A0"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Move": {"offset": "0x13880"}, "std::_Func_impl_no_alloc<<lambda_3a5822e41399948849dc8b70b56e0e35>,bool>::_Target_type": {"offset": "0x13900"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Copy": {"offset": "0x19CA0"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Delete_this": {"offset": "0x19D40"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Do_call": {"offset": "0x19D20"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_4b5991f435073e3c82f6aa21eef9cb28>,void,msgpack::v1::object_handle const &>::_Target_type": {"offset": "0x19D30"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27B10"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27B20"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Move": {"offset": "0x27B10"}, "std::_Func_impl_no_alloc<<lambda_6602a925a2729d5b96abf852f85b99cc>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27B30"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27880"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27890"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Move": {"offset": "0x27880"}, "std::_Func_impl_no_alloc<<lambda_663c07207a48b8b02cbbf2e8fb52507b>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x278A0"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Copy": {"offset": "0x113C0"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Do_call": {"offset": "0x113E0"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Move": {"offset": "0x113C0"}, "std::_Func_impl_no_alloc<<lambda_697ddd1039544c889b8c597e07737af6>,bool,char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &>::_Target_type": {"offset": "0x11510"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Copy": {"offset": "0x13830"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Do_call": {"offset": "0x13850"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Move": {"offset": "0x13830"}, "std::_Func_impl_no_alloc<<lambda_6d9c29b0ad89b6a3803ec61ae61cf467>,bool,fx::ResourceManager *>::_Target_type": {"offset": "0x13870"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27430"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27450"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Move": {"offset": "0x27430"}, "std::_Func_impl_no_alloc<<lambda_70e5c8711fa1dc312bc9291bcf8a8650>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x274C0"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Copy": {"offset": "0x23C50"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Delete_this": {"offset": "0x19EF0"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Do_call": {"offset": "0x23CD0"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_7563642f228337b5e4d62922b583c158>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Target_type": {"offset": "0x23D50"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27A70"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27A80"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Move": {"offset": "0x27A70"}, "std::_Func_impl_no_alloc<<lambda_757628b7b6924cacbf89fa9e92d29d88>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27AB0"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Copy": {"offset": "0x18F30"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Delete_this": {"offset": "0x18F80"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Do_call": {"offset": "0x18FC0"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,void>::_Target_type": {"offset": "0x198F0"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27E60"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27E80"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Move": {"offset": "0x27E60"}, "std::_Func_impl_no_alloc<<lambda_a26a4900fb9a0d8b27ddae8e47bda41b>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27EF0"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Copy": {"offset": "0x23BB0"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Delete_this": {"offset": "0x19D40"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Do_call": {"offset": "0x23C30"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_a3e97fbfaf2498451df291433411d41d>,void,msgpack::v1::object_handle const &>::_Target_type": {"offset": "0x23C40"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x137E0"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x13800"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x137E0"}, "std::_Func_impl_no_alloc<<lambda_aa12eb467c6f5d6527dc540d272acebb>,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x13820"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27BA0"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27BB0"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Move": {"offset": "0x27BA0"}, "std::_Func_impl_no_alloc<<lambda_b254a3b12b5bc22a22c86e6b8b93c099>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27BC0"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Copy": {"offset": "0x274D0"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x274F0"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Move": {"offset": "0x274D0"}, "std::_Func_impl_no_alloc<<lambda_b51dd76e073ec4069e232056e1c5aadc>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27560"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Copy": {"offset": "0x27A00"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Do_call": {"offset": "0x27A20"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Move": {"offset": "0x27A00"}, "std::_Func_impl_no_alloc<<lambda_bcebb1b22551d8ca5a3fa8c7a2e14ac3>,bool,fx::Resource *>::_Target_type": {"offset": "0x27A60"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Copy": {"offset": "0x19F30"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x19F40"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Move": {"offset": "0x19F30"}, "std::_Func_impl_no_alloc<<lambda_c57fd4becbc8c61ef1a54cc8a4316a45>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x19F50"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27B40"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27B50"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Move": {"offset": "0x27B40"}, "std::_Func_impl_no_alloc<<lambda_c5b20abf4c18bd0d48268536d9bb26fc>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27B60"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Copy": {"offset": "0x279B0"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Do_call": {"offset": "0x279D0"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Move": {"offset": "0x279B0"}, "std::_Func_impl_no_alloc<<lambda_c5e061e95a5ce06248167f82dbaf6607>,bool>::_Target_type": {"offset": "0x279F0"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Copy": {"offset": "0x19F60"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x19F70"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Move": {"offset": "0x19F60"}, "std::_Func_impl_no_alloc<<lambda_d6f333238b397f19edd9dbda7f324f1c>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x19F80"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27390"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x273B0"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Move": {"offset": "0x27390"}, "std::_Func_impl_no_alloc<<lambda_d9dc7e42be4822cf4fb980837face461>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27420"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27AC0"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27AD0"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Move": {"offset": "0x27AC0"}, "std::_Func_impl_no_alloc<<lambda_e338dfdf3eea5b416ea5865fde6f9d72>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27B00"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Copy": {"offset": "0x19DE0"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Delete_this": {"offset": "0x19EF0"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Do_call": {"offset": "0x19E60"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_edbaec5da03210d3627a1fabcf18376a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Target_type": {"offset": "0x19EE0"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Copy": {"offset": "0x27680"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Delete_this": {"offset": "0x277D0"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Do_call": {"offset": "0x276D0"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Move": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_eedc40a2521921f3ba689555a00d36bc>,void,std::basic_string_view<char,std::char_traits<char> > >::_Target_type": {"offset": "0x277C0"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27850"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27860"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Move": {"offset": "0x27850"}, "std::_Func_impl_no_alloc<<lambda_f51514d50e9e4767c296248047b7f3ef>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27870"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Copy": {"offset": "0x13A40"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Do_call": {"offset": "0x13A60"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Move": {"offset": "0x13A40"}, "std::_Func_impl_no_alloc<<lambda_f8c6d8b7450f57f5c1bc51417673baef>,bool,std::function<void __cdecl(bool,char const *,unsigned __int64)> >::_Target_type": {"offset": "0x13A80"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Copy": {"offset": "0x23D90"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x23DA0"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Move": {"offset": "0x23D90"}, "std::_Func_impl_no_alloc<<lambda_fd2d4218559059e714d322301bfd6193>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x23DB0"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Copy": {"offset": "0x27CD0"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x11520"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x27CF0"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Get": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Move": {"offset": "0x27CD0"}, "std::_Func_impl_no_alloc<<lambda_fff0c35a08c2a2e6231c76f764418c4c>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x27E50"}, "std::_Hash<std::_Umap_traits<unsigned int,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > > >,0> >::_Forced_rehash": {"offset": "0x2EEE0"}, "std::_Hash<std::_Umap_traits<void *,scrBindPointer *,std::_Uhash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,scrBindPointer *> >,0> >::_Forced_rehash": {"offset": "0x2F1A0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x297D0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Forced_rehash": {"offset": "0x2F490"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x29F70"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::erase": {"offset": "0x2F7F0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::find<void>": {"offset": "0x2A610"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x2E830"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >": {"offset": "0x2B2D0"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x29A20"}, "std::_Integral_to_string<char,int>": {"offset": "0x1CF90"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Free_non_head<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x298A0"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x29940"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x2B380"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > >,void *> > >": {"offset": "0x2B330"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<void * const,scrBindPointer *>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<void * const,scrBindPointer *>,void *> > >": {"offset": "0x2B270"}, "std::_Maklocstr<char>": {"offset": "0xE410"}, "std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14DD0"}, "std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>::~_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>": {"offset": "0x14DE0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE4D0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x13070"}, "std::_Throw_bad_array_new_length": {"offset": "0xD150"}, "std::_Throw_bad_cast": {"offset": "0x272B0"}, "std::_Throw_tree_length_error": {"offset": "0xD170"}, "std::_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x25160"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x33330"}, "std::_Tree<std::_Tmap_traits<CefStringBase<CefStringTraitsUTF16>,CefStringBase<CefStringTraitsUTF16>,std::less<CefStringBase<CefStringTraitsUTF16> >,std::allocator<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > >,1> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>": {"offset": "0x118D0"}, "std::_Tree<std::_Tmap_traits<CefStringBase<CefStringTraitsUTF16>,CefStringBase<CefStringTraitsUTF16>,std::less<CefStringBase<CefStringTraitsUTF16> >,std::allocator<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > >,1> >::_Emplace<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > >": {"offset": "0x11A30"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,msgpack::v2::object,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CDF0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Copy_nodes<0>": {"offset": "0x117E0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x11B90"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x24360"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Emplace<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x293E0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14180"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Find_hint<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CC50"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CDF0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Find_upper_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CEC0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Getal": {"offset": "0x13040"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::~_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >": {"offset": "0x123E0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14180"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > >,0> >::_Erase": {"offset": "0x190D0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2960"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2BE0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA450"}, "std::_Tree_head_scoped_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >::~_Tree_head_scoped_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >": {"offset": "0x14E60"}, "std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >": {"offset": "0x122F0"}, "std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x12330"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >": {"offset": "0x12280"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>,void *> > >": {"offset": "0x1EC40"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x122A0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >,void *> > >": {"offset": "0x14DB0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >,void *> > >": {"offset": "0x11D60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,msgpack::v2::object> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,msgpack::v2::object>,void *> > >": {"offset": "0x2900"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,msgpack::v2::object> > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>,void *> > >": {"offset": "0x1CB90"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x11DE0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x11DE0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >,void *> > >": {"offset": "0x14310"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > > >::_Extract": {"offset": "0x19280"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > > >::_Lrotate": {"offset": "0x19830"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > > >::_Rrotate": {"offset": "0x19890"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2900"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCEF0"}, "std::_Uninitialized_backout_al<std::allocator<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > >::~_Uninitialized_backout_al<std::allocator<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> > >": {"offset": "0x14EA0"}, "std::_Uninitialized_move<Header *,std::allocator<Header> >": {"offset": "0x248A0"}, "std::_Xlen_string": {"offset": "0xD190"}, "std::allocator<Header>::allocate": {"offset": "0x199D0"}, "std::allocator<Header>::deallocate": {"offset": "0x19AD0"}, "std::allocator<char>::allocate": {"offset": "0xD1B0"}, "std::allocator<char>::deallocate": {"offset": "0x130F0"}, "std::allocator<msgpack::v1::adaptor::object_with_zone<msgpack::v2::object,void>::object_with_zone_visitor::elem>::deallocate": {"offset": "0x22AA0"}, "std::allocator<msgpack::v1::object_parser::elem>::deallocate": {"offset": "0x22A50"}, "std::allocator<msgpack::v2::object>::allocate": {"offset": "0x22890"}, "std::allocator<msgpack::v2::object>::deallocate": {"offset": "0x22A50"}, "std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >::allocate": {"offset": "0x272D0"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x27340"}, "std::allocator<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >::allocate": {"offset": "0x199D0"}, "std::allocator<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >::deallocate": {"offset": "0x19AD0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x130F0"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD210"}, "std::bad_alloc::bad_alloc": {"offset": "0xA1B0"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA640"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA230"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA640"}, "std::bad_cast::bad_cast": {"offset": "0x19C20"}, "std::bad_cast::~bad_cast": {"offset": "0xA640"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x12600"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x12640"}, "std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x37D30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2840"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_from_iter<boost::iterators::transform_iterator<boost::algorithm::detail::to_lowerF<char>,std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > >,boost::use_default,boost::use_default>,boost::iterators::transform_iterator<boost::algorithm::detail::to_lowerF<char>,std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > >,boost::use_default,boost::use_default>,std::nullptr_t>": {"offset": "0x24130"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x35840"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x14390"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x359B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0xA510"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x19A40"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD400"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9FD0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find": {"offset": "0x2F8E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA510"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x2FDF0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD280"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x34870"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x378E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x37A40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA570"}, "std::basic_string_view<char,std::char_traits<char> >::basic_string_view<char,std::char_traits<char> >": {"offset": "0x1E8A0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x13140"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x132B0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x13410"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x13590"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::str": {"offset": "0x19B20"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x13340"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::~basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x14F00"}, "std::deque<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Growmap": {"offset": "0x19650"}, "std::deque<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Xlen": {"offset": "0x199B0"}, "std::deque<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::~deque<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >": {"offset": "0x14FD0"}, "std::distance<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >": {"offset": "0x14520"}, "std::exception::exception": {"offset": "0xA260"}, "std::exception::what": {"offset": "0xE2E0"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x2A580"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::unique_ptr<scrBindPointer,std::default_delete<scrBindPointer> > > > > > >": {"offset": "0x2A580"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,scrBindPointer *> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<void * const,scrBindPointer *> > > > >": {"offset": "0x2A580"}, "std::function<bool __cdecl(char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &)>::~function<bool __cdecl(char const *,scoped_refptr<CefRequest>,scoped_refptr<CefResourceHandler> &)>": {"offset": "0x122C0"}, "std::function<bool __cdecl(fx::Resource *)>::~function<bool __cdecl(fx::Resource *)>": {"offset": "0x122C0"}, "std::function<bool __cdecl(fx::ResourceManager *)>::~function<bool __cdecl(fx::ResourceManager *)>": {"offset": "0x122C0"}, "std::function<bool __cdecl(std::function<void __cdecl(bool,char const *,unsigned __int64)>)>::~function<bool __cdecl(std::function<void __cdecl(bool,char const *,unsigned __int64)>)>": {"offset": "0x122C0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x122C0"}, "std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x122C0"}, "std::function<void __cdecl(bool,char const *,unsigned __int64)>::~function<void __cdecl(bool,char const *,unsigned __int64)>": {"offset": "0x122C0"}, "std::function<void __cdecl(fwRefContainer<fx::Resource> const &)>::~function<void __cdecl(fwRefContainer<fx::Resource> const &)>": {"offset": "0x122C0"}, "std::function<void __cdecl(fx::ScriptContext &)>::~function<void __cdecl(fx::ScriptContext &)>": {"offset": "0x122C0"}, "std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x14A60"}, "std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x122C0"}, "std::function<void __cdecl(msgpack::v1::object_handle const &)>::~function<void __cdecl(msgpack::v1::object_handle const &)>": {"offset": "0x122C0"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>": {"offset": "0x14A60"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>": {"offset": "0x122C0"}, "std::function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)>::~function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)>": {"offset": "0x122C0"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)>": {"offset": "0x14A60"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)><<lambda_77ed9a324e4b2abd68f43d246a32c7f7>,0>": {"offset": "0x13AE0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x122C0"}, "std::invoke<<lambda_a42a420bbf43ca5819d2955dc9976395> const &,std::function<void __cdecl(bool,char const *,unsigned __int64)> &>": {"offset": "0x14590"}, "std::list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2B410"}, "std::locale::~locale": {"offset": "0x25360"}, "std::map<int,msgpack::v2::object,std::less<int>,std::allocator<std::pair<int const ,msgpack::v2::object> > >::~map<int,msgpack::v2::object,std::less<int>,std::allocator<std::pair<int const ,msgpack::v2::object> > >": {"offset": "0xA450"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,msgpack::v2::object,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,msgpack::v2::object,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object> > >": {"offset": "0x1EC60"}, "std::multimap<CefStringBase<CefStringTraitsUTF16>,CefStringBase<CefStringTraitsUTF16>,std::less<CefStringBase<CefStringTraitsUTF16> >,std::allocator<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > > >::~multimap<CefStringBase<CefStringTraitsUTF16>,CefStringBase<CefStringTraitsUTF16>,std::less<CefStringBase<CefStringTraitsUTF16> >,std::allocator<std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> > > >": {"offset": "0x123B0"}, "std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x14AE0"}, "std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x123E0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x12D80"}, "std::numpunct<char>::do_falsename": {"offset": "0x33A60"}, "std::numpunct<char>::do_grouping": {"offset": "0x33AA0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x12DA0"}, "std::numpunct<char>::do_truename": {"offset": "0x33AE0"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x33180"}, "std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14DD0"}, "std::pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >::~pair<CefStringBase<CefStringTraitsUTF16> const ,CefStringBase<CefStringTraitsUTF16> >": {"offset": "0x12410"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,msgpack::v2::object>": {"offset": "0xA510"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1EC90"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const &,std::function<void __cdecl(int,std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)> >": {"offset": "0x150F0"}, "std::runtime_error::runtime_error": {"offset": "0xA2E0"}, "std::runtime_error::~runtime_error": {"offset": "0xA640"}, "std::unique_lock<std::mutex>::~unique_lock<std::mutex>": {"offset": "0x124D0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x25170"}, "std::unordered_set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::unordered_set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2AAE0"}, "std::use_facet<std::ctype<char> >": {"offset": "0x25040"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x32E50"}, "utf8::exception::exception": {"offset": "0x36BD0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x35C50"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x36580"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x36C60"}, "utf8::invalid_code_point::what": {"offset": "0x37BF0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA640"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x36CD0"}, "utf8::invalid_utf8::what": {"offset": "0x37C00"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA640"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x36D30"}, "utf8::not_enough_room::what": {"offset": "0x37C10"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA640"}, "va<int>": {"offset": "0x1E830"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x36360"}, "vva": {"offset": "0x37BD0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1060"}}}