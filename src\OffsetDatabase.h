#pragma once
#include <vector>
#include <string>
#include <unordered_map>

struct OffsetPattern {
    std::string name;
    std::string description;
    std::string pattern;
    std::string category;
    int offset; // Additional offset to add to found address
    bool isRelative; // If true, read the value at found address + offset
};

class OffsetDatabase {
public:
    OffsetDatabase();
    ~OffsetDatabase();
    
    void Initialize();
    const std::vector<OffsetPattern>& GetPatterns() const { return m_patterns; }
    const OffsetPattern* GetPattern(const std::string& name) const;
    
    // Add custom patterns
    void AddPattern(const OffsetPattern& pattern);
    void RemovePattern(const std::string& name);
    
    // Save/Load custom patterns
    bool SaveCustomPatterns(const std::string& filename) const;
    bool LoadCustomPatterns(const std::string& filename);
    
private:
    void AddDefaultPatterns();
    void AddPlayerPatterns();
    void AddVehiclePatterns();
    void AddWorldPatterns();
    void AddWeaponPatterns();
    void AddNetworkPatterns();
    void AddMiscPatterns();
    
    std::vector<OffsetPattern> m_patterns;
    std::unordered_map<std::string, size_t> m_patternMap;
};
