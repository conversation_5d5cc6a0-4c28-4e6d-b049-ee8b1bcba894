{"citizen-resources-gta.dll": {"<lambda_020dcbb3c423cd868b84c6b8909d7e1c>::~<lambda_020dcbb3c423cd868b84c6b8909d7e1c>": {"offset": "0x183F0"}, "<lambda_0b0babe1a19363ec7be56191fabd5442>::~<lambda_0b0babe1a19363ec7be56191fabd5442>": {"offset": "0x183F0"}, "<lambda_37cd154843ef59c257675230155a41d2>::~<lambda_37cd154843ef59c257675230155a41d2>": {"offset": "0x183F0"}, "<lambda_45e2cfddfcee57d1e64402cf9e392e33>::~<lambda_45e2cfddfcee57d1e64402cf9e392e33>": {"offset": "0x183F0"}, "<lambda_58bb27b514c2276ce46c759771cd7375>::~<lambda_58bb27b514c2276ce46c759771cd7375>": {"offset": "0x183F0"}, "<lambda_59b50da243c6950994f38d3aa8b2b6ed>::~<lambda_59b50da243c6950994f38d3aa8b2b6ed>": {"offset": "0x183F0"}, "<lambda_69436dab28cb5ae82ac6793ca27bb42a>::~<lambda_69436dab28cb5ae82ac6793ca27bb42a>": {"offset": "0x183F0"}, "<lambda_798dd9cb8a85d121325c388b20aa99ca>::~<lambda_798dd9cb8a85d121325c388b20aa99ca>": {"offset": "0x183F0"}, "<lambda_95a8eba6b3f8df8e8dda9b27a07964cc>::~<lambda_95a8eba6b3f8df8e8dda9b27a07964cc>": {"offset": "0x183F0"}, "<lambda_979693caa8c6c75830e3b4a8f464bd99>::~<lambda_979693caa8c6c75830e3b4a8f464bd99>": {"offset": "0x183F0"}, "<lambda_f07fa65509661d169603ff7c382e5ad8>::~<lambda_f07fa65509661d169603ff7c382e5ad8>": {"offset": "0x183F0"}, "<unnamed-type-g_globalLeftoverThread>::DoRun": {"offset": "0xB560"}, "AddCrashometry": {"offset": "0x28950"}, "AddTextEntryForResource": {"offset": "0x22770"}, "CEntityLocalStateBagExtension::GetExtensionId": {"offset": "0x11390"}, "CEntityLocalStateBagExtension::Update": {"offset": "0x11A30"}, "CfxState::CfxState": {"offset": "0x27F90"}, "Component::As": {"offset": "0x11280"}, "Component::IsA": {"offset": "0x11340"}, "Component::SetCommandLine": {"offset": "0xB560"}, "Component::SetUserData": {"offset": "0x11350"}, "ComponentInstance::DoGameLoad": {"offset": "0x11320"}, "ComponentInstance::Initialize": {"offset": "0x11330"}, "ComponentInstance::Shutdown": {"offset": "0x11350"}, "CoreGetComponentRegistry": {"offset": "0xC790"}, "CoreGetGlobalInstanceRegistry": {"offset": "0xC820"}, "CreateComponent": {"offset": "0x11360"}, "DeleteDummyThread": {"offset": "0x197E0"}, "DllMain": {"offset": "0x2D15C"}, "DoGetServerId": {"offset": "0x153A0"}, "DoNtRaiseException": {"offset": "0x2A780"}, "DummyThread::DoRun": {"offset": "0xB560"}, "DummyThread::DummyThread": {"offset": "0x18270"}, "FatalErrorNoExceptRealV": {"offset": "0xCC30"}, "FatalErrorRealV": {"offset": "0xCC60"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x3390"}, "GetAbsoluteCitPath": {"offset": "0x28AD0"}, "GlobalErrorHandler": {"offset": "0xCEA0"}, "GlobalErrorRealV": {"offset": "0xD360"}, "HookFunctionBase::RunAll": {"offset": "0x2B610"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x27AF0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x28050"}, "IScriptRuntimeHandler::GetIID": {"offset": "0x10D90"}, "InitFunction::Run": {"offset": "0xF840"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x2A520"}, "InitFunctionBase::Register": {"offset": "0x2A8F0"}, "InitFunctionBase::RunAll": {"offset": "0x2A940"}, "Instance<ICoreGameInit>::Get": {"offset": "0x10D30"}, "Instance<fx::ResourceManager>::Get": {"offset": "0x1F330"}, "MakeRelativeCitPath": {"offset": "0xD780"}, "RaiseDebugException": {"offset": "0x2A860"}, "ScopedError::~ScopedError": {"offset": "0xB750"}, "SysError": {"offset": "0xDD00"}, "TestScriptThread::DoRun": {"offset": "0x20030"}, "ToNarrow": {"offset": "0x2A970"}, "ToWide": {"offset": "0x2AA60"}, "TraceRealV": {"offset": "0x2AD70"}, "Win32TrapAndJump64": {"offset": "0x2BBE0"}, "_DllMainCRTStartup": {"offset": "0x2CA60"}, "_Init_thread_abort": {"offset": "0x2BE64"}, "_Init_thread_footer": {"offset": "0x2BE94"}, "_Init_thread_header": {"offset": "0x2BEF4"}, "_Init_thread_notify": {"offset": "0x2BF5C"}, "_Init_thread_wait": {"offset": "0x2BFA0"}, "_RTC_Initialize": {"offset": "0x2D1C8"}, "_RTC_Terminate": {"offset": "0x2D204"}, "__ArrayUnwind": {"offset": "0x2CB0C"}, "__GSHandlerCheck": {"offset": "0x2C580"}, "__GSHandlerCheckCommon": {"offset": "0x2C5A0"}, "__GSHandlerCheck_EH": {"offset": "0x2C5FC"}, "__GSHandlerCheck_SEH": {"offset": "0x2CCEC"}, "__chkstk": {"offset": "0x2C6F0"}, "__crt_debugger_hook": {"offset": "0x2CF20"}, "__dyn_tls_init": {"offset": "0x2C3C8"}, "__dyn_tls_on_demand_init": {"offset": "0x2C430"}, "__isa_available_init": {"offset": "0x2CD74"}, "__local_stdio_printf_options": {"offset": "0xF760"}, "__local_stdio_scanf_options": {"offset": "0x2D19C"}, "__raise_securityfailure": {"offset": "0x2CB70"}, "__report_gsfailure": {"offset": "0x2CBA4"}, "__scrt_acquire_startup_lock": {"offset": "0x2C048"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x2C084"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x2C0B8"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x2C0D0"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x2C0F8"}, "__scrt_dllmain_exception_filter": {"offset": "0x2C110"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x2C170"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x2C1A0"}, "__scrt_fastfail": {"offset": "0x2CF28"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x2D1C0"}, "__scrt_initialize_crt": {"offset": "0x2C1B4"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x2D1A4"}, "__scrt_initialize_onexit_tables": {"offset": "0x2C200"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x2BD6C"}, "__scrt_initialize_type_info": {"offset": "0x2D180"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x2C28C"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x2E24C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x2D0A4"}, "__scrt_release_startup_lock": {"offset": "0x2C324"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x11350"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x11350"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x11350"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x11350"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x11350"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0x11280"}, "__scrt_throw_std_bad_alloc": {"offset": "0x2D074"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xE500"}, "__scrt_uninitialize_crt": {"offset": "0x2C348"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x2BE3C"}, "__scrt_uninitialize_type_info": {"offset": "0x2D190"}, "__security_check_cookie": {"offset": "0x2C690"}, "__security_init_cookie": {"offset": "0x2D0B0"}, "__std_find_trivial_1": {"offset": "0x2BC30"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x2BD10"}, "_get_startup_argv_mode": {"offset": "0x2D09C"}, "_guard_check_icall_nop": {"offset": "0xB560"}, "_guard_dispatch_icall_nop": {"offset": "0x2D340"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x2D360"}, "_onexit": {"offset": "0x2C374"}, "_wwassert": {"offset": "0x28E50"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x2E30A"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x2E264"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x2E27B"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x2E294"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x2E2A8"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x2020"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x2050"}, "`dynamic initializer for '_init_instance_24''": {"offset": "0x1430"}, "`dynamic initializer for '_init_instance_25''": {"offset": "0x1460"}, "`dynamic initializer for '_init_instance_33''": {"offset": "0x1810"}, "`dynamic initializer for '_init_instance_41''": {"offset": "0x2410"}, "`dynamic initializer for '_init_instance_42''": {"offset": "0x2440"}, "`dynamic initializer for '_init_instance_43''": {"offset": "0x2470"}, "`dynamic initializer for '_init_instance_44''": {"offset": "0x24A0"}, "`dynamic initializer for '_init_instance_45''": {"offset": "0x24D0"}, "`dynamic initializer for '_init_instance_46''": {"offset": "0x2500"}, "`dynamic initializer for '_init_instance_47''": {"offset": "0x2530"}, "`dynamic initializer for '_init_instance_48''": {"offset": "0x2560"}, "`dynamic initializer for '_init_instance_57''": {"offset": "0x1840"}, "`dynamic initializer for '_init_instance_58''": {"offset": "0x1870"}, "`dynamic initializer for '_init_instance_59''": {"offset": "0x18A0"}, "`dynamic initializer for '_init_instance_60''": {"offset": "0x18D0"}, "`dynamic initializer for '_init_instance_75''": {"offset": "0x2590"}, "`dynamic initializer for 'g_globalLeftoverThread''": {"offset": "0x2080"}, "`dynamic initializer for 'g_lastThreads''": {"offset": "0x20B0"}, "`dynamic initializer for 'g_resourceManager''": {"offset": "0x25C0"}, "`dynamic initializer for 'g_scopes''": {"offset": "0x20F0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initFunction''": {"offset": "0x1900"}, "`dynamic initializer for 'initFunctionRglt''": {"offset": "0x2170"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x2A50"}, "`dynamic initializer for 'thread''": {"offset": "0x29E0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,0,6>::ms_initFunction''": {"offset": "0x1B00"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x1690"}, "`dynamic initializer for 'xbr::virt::Base<15,2802,0,6>::ms_initFunction''": {"offset": "0x17D0"}, "`dynamic initializer for 'xbr::virt::Base<18,2802,0,6>::ms_initFunction''": {"offset": "0x16D0"}, "`dynamic initializer for 'xbr::virt::Base<23,2802,0,6>::ms_initFunction''": {"offset": "0x1710"}, "`dynamic initializer for 'xbr::virt::Base<26,2802,0,6>::ms_initFunction''": {"offset": "0x1750"}, "`dynamic initializer for 'xbr::virt::Base<26,2802,2,6>::ms_initFunction''": {"offset": "0x2330"}, "`dynamic initializer for 'xbr::virt::Base<27,2802,2,6>::ms_initFunction''": {"offset": "0x1280"}, "`dynamic initializer for 'xbr::virt::Base<29,2802,0,6>::ms_initFunction''": {"offset": "0x1790"}, "`dynamic initializer for 'xbr::virt::Base<4,2802,2,6>::ms_initFunction''": {"offset": "0x1FB0"}, "`dynamic initializer for 'xbr::virt::Base<41,2802,0,6>::ms_initFunction''": {"offset": "0x12C0"}, "`dynamic initializer for 'xbr::virt::Base<44,2802,0,6>::ms_initFunction''": {"offset": "0x1300"}, "`dynamic initializer for 'xbr::virt::Base<49,2802,0,6>::ms_initFunction''": {"offset": "0x1340"}, "`dynamic initializer for 'xbr::virt::Base<50,2802,0,6>::ms_initFunction''": {"offset": "0x2370"}, "`dynamic initializer for 'xbr::virt::Base<52,2802,0,6>::ms_initFunction''": {"offset": "0x1380"}, "`dynamic initializer for 'xbr::virt::Base<55,2802,0,6>::ms_initFunction''": {"offset": "0x13C0"}, "atexit": {"offset": "0x2C3B0"}, "capture_previous_context": {"offset": "0x2CC78"}, "debug::Alias": {"offset": "0xB560"}, "dllmain_crt_dispatch": {"offset": "0x2C740"}, "dllmain_crt_process_attach": {"offset": "0x2C790"}, "dllmain_crt_process_detach": {"offset": "0x2C8A8"}, "dllmain_dispatch": {"offset": "0x2C92C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xEB40"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xB620"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x27310"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x26950"}, "fmt::v8::detail::add_compare": {"offset": "0x26B20"}, "fmt::v8::detail::assert_fail": {"offset": "0x26C60"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x26CB0"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x26E80"}, "fmt::v8::detail::bigint::square": {"offset": "0x276E0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x26950"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x3E30"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x279A0"}, "fmt::v8::detail::compare": {"offset": "0x26DE0"}, "fmt::v8::detail::count_digits": {"offset": "0xE920"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x234A0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x3EE0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x271E0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x25370"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x274C0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x253A0"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x26060"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x25C30"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x27440"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x235B0"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x235B0"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x27170"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x3F10"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x24A60"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x41E0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x24940"}, "fmt::v8::detail::format_float<double>": {"offset": "0x22FF0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x24BA0"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x42C0"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x24F00"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x43E0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x4540"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x47A0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xF6B0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x255B0"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x25830"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x25AC0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xB680"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x4870"}, "fmt::v8::detail::utf8_decode": {"offset": "0xF4F0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5E70"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x6E70"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x6A20"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x72B0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x266C0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x265A0"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x6950"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x76F0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x7730"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x78C0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x8280"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x7C90"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xB210"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x89B0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x8DD0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x8FA0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x9140"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x9140"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x92D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x94F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x9670"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0xAE00"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x9800"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x9A20"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x9CC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x9EE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0xA060"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0xA280"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0xA4A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0xA620"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0xA840"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0xA9C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0xABE0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xAF90"}, "fmt::v8::format_error::format_error": {"offset": "0xB410"}, "fmt::v8::format_error::~format_error": {"offset": "0xB7B0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x29B50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4CE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4AC0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4990"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x48A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4CE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4BB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4E10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5970"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x53A0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x11590"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x2A3F0"}, "fprintf": {"offset": "0xF770"}, "fwEvent<>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<>::callback::~callback": {"offset": "0xFDA0"}, "fwEvent<>::~fwEvent<>": {"offset": "0x18630"}, "fwEvent<DamageEventMetaData const &>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<GameEventData const &>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<GameEventMetaData const &>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<PopulationCreationState *>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<char const *>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<fx::Resource *>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<fx::ResourceManager *>::ConnectInternal": {"offset": "0x10A40"}, "fwEvent<fx::StreamingEntryData const &>::ConnectInternal": {"offset": "0x10A40"}, "fwPlatformString::~fwPlatformString": {"offset": "0xB7D0"}, "fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >::~fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >": {"offset": "0x116B0"}, "fwRefContainer<ResourceEntryListComponent>::~fwRefContainer<ResourceEntryListComponent>": {"offset": "0x116B0"}, "fwRefContainer<console::Context>::~fwRefContainer<console::Context>": {"offset": "0x116B0"}, "fwRefContainer<fwRefCountable>::~fwRefContainer<fwRefCountable>": {"offset": "0x116B0"}, "fwRefContainer<fx::Resource>::~fwRefContainer<fx::Resource>": {"offset": "0x116B0"}, "fwRefContainer<fx::ResourceCallbackComponent>::~fwRefContainer<fx::ResourceCallbackComponent>": {"offset": "0x116B0"}, "fwRefContainer<fx::ResourceEventManagerComponent>::~fwRefContainer<fx::ResourceEventManagerComponent>": {"offset": "0x116B0"}, "fwRefContainer<fx::ResourceGameLifetimeEvents>::~fwRefContainer<fx::ResourceGameLifetimeEvents>": {"offset": "0x116B0"}, "fwRefContainer<fx::ResourceManager>::~fwRefContainer<fx::ResourceManager>": {"offset": "0x116B0"}, "fwRefContainer<fx::ResourceMetaDataComponent>::~fwRefContainer<fx::ResourceMetaDataComponent>": {"offset": "0x116B0"}, "fwRefContainer<fx::StateBagComponent>::~fwRefContainer<fx::StateBagComponent>": {"offset": "0x116B0"}, "fwRefCountable::AddRef": {"offset": "0x2B5D0"}, "fwRefCountable::Release": {"offset": "0x2B5E0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x2B5C0"}, "fx::ComponentHolder<fx::Resource>::GetComponent<ResourceEntryListComponent>": {"offset": "0x1A170"}, "fx::GetCurrentScriptRuntime<IScriptRuntime>": {"offset": "0x14850"}, "fx::OMPtr<IScriptRuntime>::~OMPtr<IScriptRuntime>": {"offset": "0xFD20"}, "fx::ResourceCallbackComponent::CallbackRef::~CallbackRef": {"offset": "0xB680"}, "fx::ResourceEventManagerComponent::QueueEvent2<int,int,__int64,float>": {"offset": "0x12010"}, "fx::ResourceEventManagerComponent::TriggerEvent2<float,float,float,unsigned int,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::ResourceCallbackComponent::CallbackRef,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef> > > >": {"offset": "0x156C0"}, "fx::ScriptContext::CheckArgument<char const *>": {"offset": "0x147D0"}, "fxCreateObjectInstance": {"offset": "0x2B550"}, "launch::IsSDKGuest": {"offset": "0x28DD0"}, "msgpack::v1::container_size_overflow::container_size_overflow": {"offset": "0x136E0"}, "msgpack::v1::container_size_overflow::~container_size_overflow": {"offset": "0xB7B0"}, "msgpack::v1::object::as<float>": {"offset": "0x15D80"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack<float>": {"offset": "0x127E0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_array": {"offset": "0x14040"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_ext": {"offset": "0x16FB0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_int32<int>": {"offset": "0x12990"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_int64<__int64>": {"offset": "0x12DB0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint32<unsigned int>": {"offset": "0x15F10"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint64<unsigned __int64>": {"offset": "0x13320"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_map": {"offset": "0x17480"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_str": {"offset": "0x14260"}, "msgpack::v1::sbuffer::write": {"offset": "0x14530"}, "msgpack::v1::sbuffer::~sbuffer": {"offset": "0x13790"}, "msgpack::v1::type_error::type_error": {"offset": "0x161D0"}, "msgpack::v1::type_error::~type_error": {"offset": "0xB7B0"}, "rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>::Peek": {"offset": "0x1F390"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1CB20"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x1F060"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x1F170"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x1B360"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::String": {"offset": "0x1F4F0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xB490"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1CB50"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::NumberStream<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,0,0>::Peek": {"offset": "0x1F3B0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1A1C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x1A410"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1A4E0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1AEE0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x1B570"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x1BA00"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x1F3D0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xB530"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xB530"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x2DA0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xC600"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xB560"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xD990"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xDA50"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xDCC0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xE160"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xB570"}, "rapidjson::internal::DigitGen": {"offset": "0xC8B0"}, "rapidjson::internal::FastPath": {"offset": "0x1F280"}, "rapidjson::internal::Grisu2": {"offset": "0xD5A0"}, "rapidjson::internal::Prettify": {"offset": "0xDB00"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x1BE70"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x3830"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x3900"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xB530"}, "rapidjson::internal::WriteExponent": {"offset": "0xE0D0"}, "rapidjson::internal::u32toa": {"offset": "0xEC30"}, "rapidjson::internal::u64toa": {"offset": "0xEEA0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x1C8C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,void *> > >": {"offset": "0xB5A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB5A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef>,void *> > >": {"offset": "0x16200"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xB5A0"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1BF90"}, "std::_Facet_Register": {"offset": "0x2BD1C"}, "std::_Func_class<bool,DamageEventMetaData const &>::~_Func_class<bool,DamageEventMetaData const &>": {"offset": "0xFD60"}, "std::_Func_class<bool,GameEventData const &>::~_Func_class<bool,GameEventData const &>": {"offset": "0xFD60"}, "std::_Func_class<bool,GameEventMetaData const &>::~_Func_class<bool,GameEventMetaData const &>": {"offset": "0xFD60"}, "std::_Func_class<bool,PopulationCreationState *>::~_Func_class<bool,PopulationCreationState *>": {"offset": "0xFD60"}, "std::_Func_class<bool,char const *>::~_Func_class<bool,char const *>": {"offset": "0xFD60"}, "std::_Func_class<bool,fx::Resource *>::~_Func_class<bool,fx::Resource *>": {"offset": "0xFD60"}, "std::_Func_class<bool,fx::ResourceManager *>::~_Func_class<bool,fx::ResourceManager *>": {"offset": "0xFD60"}, "std::_Func_class<bool,fx::StreamingEntryData const &>::~_Func_class<bool,fx::StreamingEntryData const &>": {"offset": "0xFD60"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0xFD60"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Copy": {"offset": "0x20F70"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Do_call": {"offset": "0x20F90"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Move": {"offset": "0x20F70"}, "std::_Func_impl_no_alloc<<lambda_003a353a5f74230ae2302ee73aa1073f>,bool>::_Target_type": {"offset": "0x20FB0"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Copy": {"offset": "0x17C00"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Delete_this": {"offset": "0x17DF0"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Do_call": {"offset": "0x17C80"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Move": {"offset": "0x17C40"}, "std::_Func_impl_no_alloc<<lambda_020dcbb3c423cd868b84c6b8909d7e1c>,bool>::_Target_type": {"offset": "0x17DE0"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Copy": {"offset": "0x19D60"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Do_call": {"offset": "0x19D80"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Move": {"offset": "0x19D60"}, "std::_Func_impl_no_alloc<<lambda_03cc364e7064a3fb40abc15e0f58388c>,bool>::_Target_type": {"offset": "0x19DA0"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Copy": {"offset": "0x14720"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Do_call": {"offset": "0x14740"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Move": {"offset": "0x14720"}, "std::_Func_impl_no_alloc<<lambda_0595da9c6dbc552e529d9f62ebb75a49>,bool>::_Target_type": {"offset": "0x14760"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Copy": {"offset": "0x20FC0"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x20FD0"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Move": {"offset": "0x20FC0"}, "std::_Func_impl_no_alloc<<lambda_093abbc69718c9fb72116a50da6f64e3>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x20FE0"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Copy": {"offset": "0x18120"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Do_call": {"offset": "0x18140"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Move": {"offset": "0x18120"}, "std::_Func_impl_no_alloc<<lambda_13401df5fb35228a395cec28ad73d453>,bool,fx::Resource *>::_Target_type": {"offset": "0x18160"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Copy": {"offset": "0x19F80"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Do_call": {"offset": "0x19FA0"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Move": {"offset": "0x19F80"}, "std::_Func_impl_no_alloc<<lambda_2412fe6589986123712ed65cd52edd5d>,bool>::_Target_type": {"offset": "0x19FC0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Copy": {"offset": "0x154C0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Do_call": {"offset": "0x154E0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Move": {"offset": "0x154C0"}, "std::_Func_impl_no_alloc<<lambda_24b4b45961175ae31b41531607633394>,void,msgpack::v1::object_handle const &>::_Target_type": {"offset": "0x155E0"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Copy": {"offset": "0x19F30"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Do_call": {"offset": "0x19F50"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Move": {"offset": "0x19F30"}, "std::_Func_impl_no_alloc<<lambda_2592abcb829b4084ee211c2198982476>,bool>::_Target_type": {"offset": "0x19F70"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Copy": {"offset": "0xF8F0"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Do_call": {"offset": "0xF910"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Move": {"offset": "0xF8F0"}, "std::_Func_impl_no_alloc<<lambda_279e2605b363c41479f4498365983b0e>,bool>::_Target_type": {"offset": "0xF930"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Copy": {"offset": "0x20F10"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x20F20"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Move": {"offset": "0x20F10"}, "std::_Func_impl_no_alloc<<lambda_28ba4f8ff8fa5a0f04d5b62e0f09e5a1>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x20F30"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Copy": {"offset": "0x17A50"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Do_call": {"offset": "0x17A70"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Move": {"offset": "0x17A50"}, "std::_Func_impl_no_alloc<<lambda_2a08396ee3f85f757fa01dd53ccbc87c>,bool>::_Target_type": {"offset": "0x17BF0"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Copy": {"offset": "0x14770"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Do_call": {"offset": "0x14790"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Move": {"offset": "0x14770"}, "std::_Func_impl_no_alloc<<lambda_2c3d34ad3300dc12ca30170cc65c2c10>,bool,fx::Resource *>::_Target_type": {"offset": "0x147B0"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Copy": {"offset": "0xF940"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Delete_this": {"offset": "0xFA80"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Do_call": {"offset": "0xF960"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Move": {"offset": "0xF940"}, "std::_Func_impl_no_alloc<<lambda_2c42f571959016cd6ec4bfaa7a6f72e5>,bool>::_Target_type": {"offset": "0xFA70"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Copy": {"offset": "0x18170"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Do_call": {"offset": "0x18190"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Move": {"offset": "0x18170"}, "std::_Func_impl_no_alloc<<lambda_33f5d410504fa2fe3e34531bde4fcced>,bool,fx::Resource *>::_Target_type": {"offset": "0x181B0"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Copy": {"offset": "0x17780"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Delete_this": {"offset": "0x17920"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Do_call": {"offset": "0x17800"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Move": {"offset": "0x177C0"}, "std::_Func_impl_no_alloc<<lambda_37cd154843ef59c257675230155a41d2>,bool>::_Target_type": {"offset": "0x17910"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Copy": {"offset": "0x21050"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x21060"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Move": {"offset": "0x21050"}, "std::_Func_impl_no_alloc<<lambda_43d486098257d65e0b10c81abae36717>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x21090"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Copy": {"offset": "0x15000"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x15010"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Move": {"offset": "0x15000"}, "std::_Func_impl_no_alloc<<lambda_45eed0781dc134e58eed87cab2297b4f>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x15020"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Copy": {"offset": "0x19FD0"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Do_call": {"offset": "0x19FF0"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Move": {"offset": "0x19FD0"}, "std::_Func_impl_no_alloc<<lambda_4bdbc78dcce97760e95371a4ef4c4e7a>,bool,fx::Resource *>::_Target_type": {"offset": "0x1A010"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Copy": {"offset": "0x11DF0"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Do_call": {"offset": "0x11E10"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Move": {"offset": "0x11DF0"}, "std::_Func_impl_no_alloc<<lambda_4fe63e1d6bf82379a155822929cd740d>,bool,GameEventData const &>::_Target_type": {"offset": "0x11E30"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Copy": {"offset": "0x210D0"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x210E0"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Move": {"offset": "0x210D0"}, "std::_Func_impl_no_alloc<<lambda_511e56472e1e561e0bcb2751b479c7f2>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x21120"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Copy": {"offset": "0x15030"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x15040"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Move": {"offset": "0x15030"}, "std::_Func_impl_no_alloc<<lambda_5a81f8f510f6ca813b222992bfb5c0e1>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x150A0"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Copy": {"offset": "0x19C90"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Do_call": {"offset": "0x19CB0"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Move": {"offset": "0x19C90"}, "std::_Func_impl_no_alloc<<lambda_604e2b271774c10969a514a8cd75486c>,bool>::_Target_type": {"offset": "0x19CD0"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Copy": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Do_call": {"offset": "0xF870"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Move": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_606948061715cee3ce59356d4c8fc5c5>,bool>::_Target_type": {"offset": "0xF8C0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Copy": {"offset": "0x205A0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Do_call": {"offset": "0x205C0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Move": {"offset": "0x205A0"}, "std::_Func_impl_no_alloc<<lambda_624f2317b3fa9891892766bd865bb063>,bool>::_Target_type": {"offset": "0x205F0"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Copy": {"offset": "0x18070"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Delete_this": {"offset": "0x17DF0"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Do_call": {"offset": "0x180F0"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Move": {"offset": "0x180B0"}, "std::_Func_impl_no_alloc<<lambda_69436dab28cb5ae82ac6793ca27bb42a>,bool>::_Target_type": {"offset": "0x18110"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Copy": {"offset": "0x19E00"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Do_call": {"offset": "0x19E10"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Move": {"offset": "0x19E00"}, "std::_Func_impl_no_alloc<<lambda_7313d603560e00314763de484c32347b>,void,fwRefContainer<fx::Resource> const &>::_Target_type": {"offset": "0x19ED0"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Copy": {"offset": "0x1A070"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Do_call": {"offset": "0x1A090"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Move": {"offset": "0x1A070"}, "std::_Func_impl_no_alloc<<lambda_75740d1e9dcf6d416bf224ed3109e782>,bool>::_Target_type": {"offset": "0x1A0B0"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Copy": {"offset": "0x179A0"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Delete_this": {"offset": "0x17920"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Do_call": {"offset": "0x17A20"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Move": {"offset": "0x179E0"}, "std::_Func_impl_no_alloc<<lambda_798dd9cb8a85d121325c388b20aa99ca>,bool>::_Target_type": {"offset": "0x17A40"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Copy": {"offset": "0x20D00"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Delete_this": {"offset": "0xFA80"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Do_call": {"offset": "0x20D20"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Move": {"offset": "0x20D00"}, "std::_Func_impl_no_alloc<<lambda_7e6d24a3e904c5bbb11bc72cb1de8ae0>,bool>::_Target_type": {"offset": "0x20EA0"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Copy": {"offset": "0x19C10"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Do_call": {"offset": "0x19C30"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Move": {"offset": "0x19C10"}, "std::_Func_impl_no_alloc<<lambda_8292f04bbddc812a9392b8e6ab9f52ba>,bool,fx::StreamingEntryData const &>::_Target_type": {"offset": "0x19C80"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Copy": {"offset": "0x155F0"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Do_call": {"offset": "0x15610"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Move": {"offset": "0x155F0"}, "std::_Func_impl_no_alloc<<lambda_857a031631e5fc4a9633ca37f4483f3e>,void,msgpack::v1::object_handle const &>::_Target_type": {"offset": "0x15620"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Copy": {"offset": "0x20FF0"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Do_call": {"offset": "0x20F90"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Move": {"offset": "0x20FF0"}, "std::_Func_impl_no_alloc<<lambda_8a836e3360a4cd4145262bdbebee874e>,bool>::_Target_type": {"offset": "0x21010"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Copy": {"offset": "0x21020"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Move": {"offset": "0x21020"}, "std::_Func_impl_no_alloc<<lambda_98a3c18b9fa6fcc7a7022bcbf908cd85>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x21040"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Copy": {"offset": "0x14660"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x14670"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Move": {"offset": "0x14660"}, "std::_Func_impl_no_alloc<<lambda_9f3e394ebada362f209ebb3951d8e748>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x146C0"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Copy": {"offset": "0x15630"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Do_call": {"offset": "0x15650"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Move": {"offset": "0x15630"}, "std::_Func_impl_no_alloc<<lambda_a25e0b511e8e6626ed523e6a71557a14>,bool,PopulationCreationState *>::_Target_type": {"offset": "0x15670"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Copy": {"offset": "0xFA90"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Do_call": {"offset": "0xFAA0"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Move": {"offset": "0xFA90"}, "std::_Func_impl_no_alloc<<lambda_a937b5c8fd658eb0e545fab84019d0a0>,void,fx::ScriptContext &>::_Target_type": {"offset": "0xFAB0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Copy": {"offset": "0x19DB0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Do_call": {"offset": "0x19DD0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Move": {"offset": "0x19DB0"}, "std::_Func_impl_no_alloc<<lambda_aa61d066a437faf85656c3c77b4a396a>,bool>::_Target_type": {"offset": "0x19DF0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Copy": {"offset": "0x20EB0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x20EC0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Move": {"offset": "0x20EB0"}, "std::_Func_impl_no_alloc<<lambda_b3b9a0cbb4c00fa2d81a46fbc98a2f57>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x20ED0"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Copy": {"offset": "0x20F40"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x20F50"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Move": {"offset": "0x20F40"}, "std::_Func_impl_no_alloc<<lambda_b7ab20a0bc2764fe7632746291c71836>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x20F60"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Copy": {"offset": "0x11E40"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Do_call": {"offset": "0x11E60"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Move": {"offset": "0x11E40"}, "std::_Func_impl_no_alloc<<lambda_b9e2391745d4e4488731b15a28d2bbc4>,bool,GameEventMetaData const &>::_Target_type": {"offset": "0x11E80"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Copy": {"offset": "0x146D0"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Do_call": {"offset": "0x146F0"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Move": {"offset": "0x146D0"}, "std::_Func_impl_no_alloc<<lambda_b9fcc4fdc7b0da4a7068ff4267854eb4>,bool>::_Target_type": {"offset": "0x14710"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Copy": {"offset": "0x19CE0"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Do_call": {"offset": "0x19D00"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Move": {"offset": "0x19CE0"}, "std::_Func_impl_no_alloc<<lambda_ba514b249a8fcf9763eebb7994cced06>,bool,fx::ResourceManager *>::_Target_type": {"offset": "0x19D50"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Copy": {"offset": "0x14630"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x14640"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Move": {"offset": "0x14630"}, "std::_Func_impl_no_alloc<<lambda_c30bae9ea836b1d00d41f4d8ddf8ecdc>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x14650"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Copy": {"offset": "0x11430"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x11440"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Move": {"offset": "0x11430"}, "std::_Func_impl_no_alloc<<lambda_d0b61727daacb798d4f91468de6c01e1>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x11450"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Copy": {"offset": "0x19EE0"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Do_call": {"offset": "0x19F00"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Move": {"offset": "0x19EE0"}, "std::_Func_impl_no_alloc<<lambda_d0ca2981ffb3db4dd634f84429a34bbb>,bool>::_Target_type": {"offset": "0x19F20"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Copy": {"offset": "0x11C50"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x11C60"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Move": {"offset": "0x11C50"}, "std::_Func_impl_no_alloc<<lambda_d7068dd8fd7540b1707e94129b397c2a>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x11C80"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Copy": {"offset": "0x1A020"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Do_call": {"offset": "0x1A040"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Move": {"offset": "0x1A020"}, "std::_Func_impl_no_alloc<<lambda_dadab8e5d7e3181d1b69ebc2b72b4db7>,bool>::_Target_type": {"offset": "0x1A060"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Copy": {"offset": "0x20550"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Do_call": {"offset": "0x20570"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Move": {"offset": "0x20550"}, "std::_Func_impl_no_alloc<<lambda_e0eba4340a92cbfebf12a6f5d9d05e62>,bool,char const *>::_Target_type": {"offset": "0x20590"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Copy": {"offset": "0x210A0"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x210B0"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Move": {"offset": "0x210A0"}, "std::_Func_impl_no_alloc<<lambda_e2cbf9c2ead06d8aa64ff32ae20fb552>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x210C0"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Copy": {"offset": "0x11E90"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Do_call": {"offset": "0x11EB0"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Move": {"offset": "0x11E90"}, "std::_Func_impl_no_alloc<<lambda_e314335191a403ae7677aed1f34f5c95>,bool,DamageEventMetaData const &>::_Target_type": {"offset": "0x11ED0"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Copy": {"offset": "0x17E70"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Delete_this": {"offset": "0x17DF0"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Do_call": {"offset": "0x17EF0"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Move": {"offset": "0x17EB0"}, "std::_Func_impl_no_alloc<<lambda_f07fa65509661d169603ff7c382e5ad8>,bool>::_Target_type": {"offset": "0x18060"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Copy": {"offset": "0x20EE0"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF8E0"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x20EF0"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Get": {"offset": "0xF8D0"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Move": {"offset": "0x20EE0"}, "std::_Func_impl_no_alloc<<lambda_f6e96025b6b7568b50f6920c6fc61e14>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x20F00"}, "std::_Hash<std::_Umap_traits<unsigned int,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >,0> >::_Forced_rehash": {"offset": "0x22BA0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >,0> >::_Try_emplace<unsigned int const &>": {"offset": "0x21500"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1C150"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Forced_rehash": {"offset": "0x1F840"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Unchecked_erase": {"offset": "0x1FD10"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1C420"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x1F660"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >": {"offset": "0x1C8E0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > > > >::_Assign_grow": {"offset": "0x1F660"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > > > >": {"offset": "0x1C8E0"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x20780"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Free_non_head<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x1C220"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x1C2C0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x1C940"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,void *> > >": {"offset": "0x217F0"}, "std::_Maklocstr<char>": {"offset": "0xF7C0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0x11280"}, "std::_Ref_count_obj2<MissionCleanupData>::_Delete_this": {"offset": "0x176F0"}, "std::_Ref_count_obj2<MissionCleanupData>::_Destroy": {"offset": "0xB560"}, "std::_Throw_bad_array_new_length": {"offset": "0xE500"}, "std::_Throw_tree_length_error": {"offset": "0xE520"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x26910"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3AD0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3D50"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xB5C0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xFAD0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xFAD0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Erase": {"offset": "0x20B60"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x20640"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x20710"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB5A0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xFC60"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x10DB0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xE2A0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x11180"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x111E0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef>,void *> > >": {"offset": "0x15B20"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef> > >::_Insert_node": {"offset": "0xE2A0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x3A70"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xE2A0"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1C330"}, "std::_Xlen_string": {"offset": "0xE540"}, "std::allocator<char>::allocate": {"offset": "0xE560"}, "std::allocator<char>::deallocate": {"offset": "0x1FFA0"}, "std::allocator<msgpack::v2::object>::deallocate": {"offset": "0x16F60"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x1FF30"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x1FFE0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x1FFA0"}, "std::allocator<wchar_t>::allocate": {"offset": "0xE5C0"}, "std::bad_alloc::bad_alloc": {"offset": "0xB320"}, "std::bad_alloc::~bad_alloc": {"offset": "0xB7B0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xB3A0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xB7B0"}, "std::bad_cast::bad_cast": {"offset": "0x15480"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x39B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x291B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x21370"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x29320"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0xB680"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xE7B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xB140"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xB680"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x233D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xB6E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xE630"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x27EC0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x2B210"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x2B370"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xB6E0"}, "std::basic_string_view<char,std::char_traits<char> >::basic_string_view<char,std::char_traits<char> >": {"offset": "0x14AD0"}, "std::deque<UpdatingScriptThreadsScope,std::allocator<UpdatingScriptThreadsScope> >::_Growmap": {"offset": "0x19A10"}, "std::deque<UpdatingScriptThreadsScope,std::allocator<UpdatingScriptThreadsScope> >::_Xlen": {"offset": "0x19BF0"}, "std::deque<UpdatingScriptThreadsScope,std::allocator<UpdatingScriptThreadsScope> >::~deque<UpdatingScriptThreadsScope,std::allocator<UpdatingScriptThreadsScope> >": {"offset": "0x18510"}, "std::deque<rage::scrThread *,std::allocator<rage::scrThread *> >::_Growmap": {"offset": "0x19830"}, "std::deque<rage::scrThread *,std::allocator<rage::scrThread *> >::_Xlen": {"offset": "0x19BF0"}, "std::deque<rage::scrThread *,std::allocator<rage::scrThread *> >::~deque<rage::scrThread *,std::allocator<rage::scrThread *> >": {"offset": "0x18440"}, "std::deque<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Growmap": {"offset": "0x1FAC0"}, "std::deque<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Xlen": {"offset": "0x19BF0"}, "std::deque<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~deque<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1C9D0"}, "std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Growmap": {"offset": "0x1FAC0"}, "std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Xlen": {"offset": "0x19BF0"}, "std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::erase": {"offset": "0x22D60"}, "std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1C9D0"}, "std::distance<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >": {"offset": "0x1C3B0"}, "std::exception::exception": {"offset": "0xB3D0"}, "std::exception::what": {"offset": "0xF690"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x1C700"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > > > >": {"offset": "0x1C700"}, "std::function<bool __cdecl(DamageEventMetaData const &)>::~function<bool __cdecl(DamageEventMetaData const &)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(GameEventData const &)>::~function<bool __cdecl(GameEventData const &)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(GameEventMetaData const &)>::~function<bool __cdecl(GameEventMetaData const &)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(PopulationCreationState *)>::~function<bool __cdecl(PopulationCreationState *)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(char const *)>::~function<bool __cdecl(char const *)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(fx::Resource *)>::~function<bool __cdecl(fx::Resource *)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(fx::ResourceManager *)>::~function<bool __cdecl(fx::ResourceManager *)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(fx::StreamingEntryData const &)>::~function<bool __cdecl(fx::StreamingEntryData const &)>": {"offset": "0xFD60"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0xFD60"}, "std::function<void __cdecl(fwRefContainer<fx::Resource> const &)>::~function<void __cdecl(fwRefContainer<fx::Resource> const &)>": {"offset": "0xFD60"}, "std::function<void __cdecl(fx::ScriptContext &)>::~function<void __cdecl(fx::ScriptContext &)>": {"offset": "0xFD60"}, "std::function<void __cdecl(msgpack::v1::object_handle const &)>::~function<void __cdecl(msgpack::v1::object_handle const &)>": {"offset": "0xFD60"}, "std::list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1CAE0"}, "std::list<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,std::allocator<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >::~list<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >,std::allocator<std::pair<unsigned int const ,std::deque<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >": {"offset": "0x21830"}, "std::locale::~locale": {"offset": "0x269D0"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::ResourceCallbackComponent::CallbackRef,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef> > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::ResourceCallbackComponent::CallbackRef,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef> > >": {"offset": "0x16220"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x27090"}, "std::numpunct<char>::do_falsename": {"offset": "0x270A0"}, "std::numpunct<char>::do_grouping": {"offset": "0x270E0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x27120"}, "std::numpunct<char>::do_truename": {"offset": "0x27130"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x26760"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,fx::ResourceCallbackComponent::CallbackRef>": {"offset": "0x16250"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x16250"}, "std::runtime_error::runtime_error": {"offset": "0xB450"}, "std::runtime_error::~runtime_error": {"offset": "0xB7B0"}, "std::shared_ptr<MissionCleanupData>::~shared_ptr<MissionCleanupData>": {"offset": "0x186E0"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xB680"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x16250"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0xFD90"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x269B0"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x26430"}, "utf8::exception::exception": {"offset": "0x2A540"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x295C0"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x29EF0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x2A5D0"}, "utf8::invalid_code_point::what": {"offset": "0x2B520"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xB7B0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x2A640"}, "utf8::invalid_utf8::what": {"offset": "0x2B530"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xB7B0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x2A6A0"}, "utf8::not_enough_room::what": {"offset": "0x2B540"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xB7B0"}, "va<int>": {"offset": "0x14980"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x29CD0"}, "vva": {"offset": "0x2B500"}, "xbr::GetGameBuild": {"offset": "0x11950"}, "xbr::GetReplaceExecutableInit": {"offset": "0x2B640"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x2B860"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x2AA0"}}}