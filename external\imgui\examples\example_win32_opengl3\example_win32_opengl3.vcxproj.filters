﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="sources">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="imgui">
      <UniqueIdentifier>{a82cba23-9de0-45c2-b1e3-2eb1666702de}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_demo.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_draw.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_widgets.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\backends\imgui_impl_win32.cpp">
      <Filter>sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_tables.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\backends\imgui_impl_opengl3.cpp">
      <Filter>sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\imconfig.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\imgui.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\imgui_internal.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\backends\imgui_impl_win32.h">
      <Filter>sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\backends\imgui_impl_opengl3.h">
      <Filter>sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\backends\imgui_impl_opengl3_loader.h">
      <Filter>sources</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\README.txt" />
    <None Include="..\..\misc\natvis\imgui.natvis">
      <Filter>sources</Filter>
    </None>
  </ItemGroup>
</Project>