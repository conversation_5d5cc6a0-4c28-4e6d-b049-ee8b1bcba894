# Build directories
build/
bin/
obj/

# Visual Studio
.vs/
*.vcxproj.user
*.sln.docstates
*.suo
*.user
*.userosscache
*.sln.docstates

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Compiled binaries
*.dll
*.exe
*.lib
*.pdb
*.ilk
*.exp

# Logs
*.log

# Temporary files
*.tmp
*.temp

# IDE files
.vscode/
*.swp
*.swo

# External dependencies (will be downloaded)
external/imgui/

# Output files
offsets.json
offsets.h
offsets.CT
