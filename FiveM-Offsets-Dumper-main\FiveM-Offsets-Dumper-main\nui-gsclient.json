{"nui-gsclient.dll": {"<lambda_11c5c011f2722fe5846e3ebc09b80519>::~<lambda_11c5c011f2722fe5846e3ebc09b80519>": {"offset": "0x1C650"}, "<lambda_3c4da36579779a8aa59ffe6304652097>::~<lambda_3c4da36579779a8aa59ffe6304652097>": {"offset": "0x1C6D0"}, "<lambda_41af1ba482b752c39807db2ef15caf48>::~<lambda_41af1ba482b752c39807db2ef15caf48>": {"offset": "0xA0C0"}, "<lambda_47c7abed81b887d9b1f03e84be315afa>::~<lambda_47c7abed81b887d9b1f03e84be315afa>": {"offset": "0x1C730"}, "<lambda_4bd345ce870bcb6a9dba57dde6595eaf>::<lambda_4bd345ce870bcb6a9dba57dde6595eaf>": {"offset": "0xEB60"}, "<lambda_7604da78e8f33a312c72ae5a43f9b93b>::~<lambda_7604da78e8f33a312c72ae5a43f9b93b>": {"offset": "0x1C7E0"}, "<lambda_7871d71d345db730e5e5ee19837ae8ba>::~<lambda_7871d71d345db730e5e5ee19837ae8ba>": {"offset": "0x1C810"}, "<lambda_b5c74389bb0c23f2fc47efd332539dd8>::~<lambda_b5c74389bb0c23f2fc47efd332539dd8>": {"offset": "0x1C860"}, "<lambda_c4854cf4b185a6bb580702f1ea495b0b>::<lambda_c4854cf4b185a6bb580702f1ea495b0b>": {"offset": "0x1B720"}, "<lambda_c4854cf4b185a6bb580702f1ea495b0b>::~<lambda_c4854cf4b185a6bb580702f1ea495b0b>": {"offset": "0x1C8E0"}, "<lambda_ce34850a21ec92d50ae707a6354bd250>::~<lambda_ce34850a21ec92d50ae707a6354bd250>": {"offset": "0x1C730"}, "<lambda_f17ebabe33bc32be107ce6fff046b802>::~<lambda_f17ebabe33bc32be107ce6fff046b802>": {"offset": "0x1C7E0"}, "CfxState::CfxState": {"offset": "0x2FB30"}, "Component::As": {"offset": "0xE080"}, "Component::IsA": {"offset": "0xE140"}, "Component::SetCommandLine": {"offset": "0x9FA0"}, "Component::SetUserData": {"offset": "0xE150"}, "ComponentInstance::DoGameLoad": {"offset": "0xE120"}, "ComponentInstance::Initialize": {"offset": "0xE130"}, "ComponentInstance::Shutdown": {"offset": "0xE150"}, "ConVar<int>::ConVar<int>": {"offset": "0x1B7B0"}, "ConVar<int>::~ConVar<int>": {"offset": "0x1CA20"}, "ConsoleArgumentType<int,void>::Parse": {"offset": "0x27650"}, "ConsoleCommand::ConsoleCommand<<lambda_41af1ba482b752c39807db2ef15caf48> >": {"offset": "0xEB70"}, "ConsoleCommand::ConsoleCommand<<lambda_cd07e93ce4e084374db77c62c4f7d117> >": {"offset": "0xEDD0"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x1D010"}, "ConsoleFlagsToString": {"offset": "0x23C80"}, "ContinueLanQuery": {"offset": "0x23FA0"}, "CoreGetComponentRegistry": {"offset": "0x24370"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x24400"}, "CreateComponent": {"offset": "0xE160"}, "CreateVariableEntry<int>": {"offset": "0xFE50"}, "DllMain": {"offset": "0x3682C"}, "DoNtRaiseException": {"offset": "0x31FC0"}, "FatalErrorNoExceptRealV": {"offset": "0xB670"}, "FatalErrorRealV": {"offset": "0xB6A0"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x1DD0"}, "GSClient_GetFavorites": {"offset": "0x24920"}, "GSClient_HandleInfoResponse": {"offset": "0x24E00"}, "GSClient_Init": {"offset": "0x25830"}, "GSClient_Ping": {"offset": "0x258E0"}, "GSClient_PollSocket": {"offset": "0x25EC0"}, "GSClient_QueryOneServer": {"offset": "0x26150"}, "GSClient_QueryOneServerWrap": {"offset": "0x270F0"}, "GetAbsoluteCitPath": {"offset": "0x304F0"}, "GlobalErrorHandler": {"offset": "0xB8E0"}, "HookFunctionBase::RunAll": {"offset": "0x32E20"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x2F760"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x2FBF0"}, "HttpRequestOptions::HttpRequestOptions": {"offset": "0x1C340"}, "HttpRequestOptions::~HttpRequestOptions": {"offset": "0x1D0A0"}, "Info_ValueForKey": {"offset": "0x272C0"}, "InitFunction::Run": {"offset": "0xE190"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x31DD0"}, "InitFunctionBase::Register": {"offset": "0x32130"}, "InitFunctionBase::RunAll": {"offset": "0x32180"}, "InitSocket": {"offset": "0x27400"}, "Instance<HttpClient>::Get": {"offset": "0x27120"}, "LoadInfoBlob<<lambda_c4854cf4b185a6bb580702f1ea495b0b> >": {"offset": "0x10940"}, "MakeRelativeCitPath": {"offset": "0xBF80"}, "RaiseDebugException": {"offset": "0x320A0"}, "RequestInfoBlob<<lambda_c4854cf4b185a6bb580702f1ea495b0b> >": {"offset": "0x12FB0"}, "ScopedError::~ScopedError": {"offset": "0xA190"}, "SysError": {"offset": "0xC500"}, "ToNarrow": {"offset": "0x321B0"}, "ToWide": {"offset": "0x322A0"}, "TraceRealV": {"offset": "0x325B0"}, "Win32TrapAndJump64": {"offset": "0x32E50"}, "_DllMainCRTStartup": {"offset": "0x361F0"}, "_Init_thread_abort": {"offset": "0x35504"}, "_Init_thread_footer": {"offset": "0x35534"}, "_Init_thread_header": {"offset": "0x35594"}, "_Init_thread_notify": {"offset": "0x355FC"}, "_Init_thread_wait": {"offset": "0x35640"}, "_RTC_Initialize": {"offset": "0x3687C"}, "_RTC_Terminate": {"offset": "0x368B8"}, "_Smtx_lock_exclusive": {"offset": "0x353FC"}, "_Smtx_unlock_exclusive": {"offset": "0x35404"}, "__ArrayUnwind": {"offset": "0x35E04"}, "__GSHandlerCheck": {"offset": "0x35C20"}, "__GSHandlerCheckCommon": {"offset": "0x35C40"}, "__GSHandlerCheck_EH": {"offset": "0x35C9C"}, "__GSHandlerCheck_SEH": {"offset": "0x363AC"}, "__chkstk": {"offset": "0x35E80"}, "__crt_debugger_hook": {"offset": "0x365F0"}, "__dyn_tls_init": {"offset": "0x35A68"}, "__dyn_tls_on_demand_init": {"offset": "0x35AD0"}, "__isa_available_init": {"offset": "0x36444"}, "__local_stdio_printf_options": {"offset": "0xDF60"}, "__local_stdio_scanf_options": {"offset": "0x36850"}, "__raise_securityfailure": {"offset": "0x36230"}, "__report_gsfailure": {"offset": "0x36264"}, "__scrt_acquire_startup_lock": {"offset": "0x356E8"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x35724"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x35758"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x35770"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x35798"}, "__scrt_dllmain_exception_filter": {"offset": "0x357B0"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x35810"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x35840"}, "__scrt_fastfail": {"offset": "0x365F8"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x36874"}, "__scrt_initialize_crt": {"offset": "0x35854"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x36858"}, "__scrt_initialize_onexit_tables": {"offset": "0x358A0"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x3540C"}, "__scrt_initialize_type_info": {"offset": "0x35D7C"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x3592C"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x381AD"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x36774"}, "__scrt_release_startup_lock": {"offset": "0x359C4"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE150"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE150"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE150"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE150"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE150"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE080"}, "__scrt_throw_std_bad_alloc": {"offset": "0x36744"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xCD00"}, "__scrt_uninitialize_crt": {"offset": "0x359E8"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x354DC"}, "__scrt_uninitialize_type_info": {"offset": "0x35D8C"}, "__security_check_cookie": {"offset": "0x35D30"}, "__security_init_cookie": {"offset": "0x36780"}, "__std_find_trivial_1": {"offset": "0x35190"}, "__std_find_trivial_2": {"offset": "0x35260"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x353F0"}, "_get_startup_argv_mode": {"offset": "0x3676C"}, "_guard_check_icall_nop": {"offset": "0x9FA0"}, "_guard_dispatch_icall_nop": {"offset": "0x36A20"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x36A40"}, "_onexit": {"offset": "0x35A14"}, "_snprintf": {"offset": "0x2A660"}, "_wwassert": {"offset": "0x30870"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x381F1"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x38250"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x38267"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x38280"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x38294"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1210"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x1240"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x1270"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x12A0"}, "`dynamic initializer for '_init_instance_4''": {"offset": "0x12D0"}, "`dynamic initializer for 'g_cls''": {"offset": "0x1300"}, "`dynamic initializer for 'g_queryArg''": {"offset": "0x13D0"}, "`dynamic initializer for 'g_queryArgMutex''": {"offset": "0x13E0"}, "`dynamic initializer for 'g_queryArgOrig''": {"offset": "0x1410"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initFunction''": {"offset": "0x1420"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1470"}, "`dynamic initializer for 'tbb::detail::r1::concurrent_monitor_mutex::my_init_mutex''": {"offset": "0x1600"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>,<lambda_11c5c011f2722fe5846e3ebc09b80519> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D190"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>,<lambda_11c5c011f2722fe5846e3ebc09b80519> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D190"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>,<lambda_47c7abed81b887d9b1f03e84be315afa> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>,<lambda_47c7abed81b887d9b1f03e84be315afa> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>,<lambda_7604da78e8f33a312c72ae5a43f9b93b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>,<lambda_b5c74389bb0c23f2fc47efd332539dd8> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1F0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>,<lambda_b5c74389bb0c23f2fc47efd332539dd8> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1F0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>,<lambda_ce34850a21ec92d50ae707a6354bd250> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>,<lambda_f17ebabe33bc32be107ce6fff046b802> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D1D0"}, "atexit": {"offset": "0x35A50"}, "boost::optional<net::PeerAddress>::~optional<net::PeerAddress>": {"offset": "0x1CD20"}, "capture_previous_context": {"offset": "0x36338"}, "console::DPrintfv": {"offset": "0x24490"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x12C40"}, "console::Printfv": {"offset": "0x27720"}, "dllmain_crt_dispatch": {"offset": "0x35ED0"}, "dllmain_crt_process_attach": {"offset": "0x35F20"}, "dllmain_crt_process_detach": {"offset": "0x36038"}, "dllmain_dispatch": {"offset": "0x360BC"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD340"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA060"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x2EF80"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x2E320"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x28B10"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x1CC60"}, "fmt::v8::detail::add_compare": {"offset": "0x2E6F0"}, "fmt::v8::detail::assert_fail": {"offset": "0x2E830"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x2E880"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x2EA50"}, "fmt::v8::detail::bigint::square": {"offset": "0x2F350"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x2E320"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2870"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x2F610"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x14B00"}, "fmt::v8::detail::compare": {"offset": "0x2E9B0"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x14BC0"}, "fmt::v8::detail::count_digits": {"offset": "0xD120"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x2ABC0"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x2ACD0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2920"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x2EE50"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x2CB40"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x2F130"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x2CB70"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x2D940"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x2D510"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x2F0B0"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x2AD80"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x2AD80"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x14C90"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x14D50"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x2EDE0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2950"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x2C230"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x2C20"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x2C110"}, "fmt::v8::detail::format_float<double>": {"offset": "0x2A6D0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x2C370"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x2D00"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x14DD0"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x2C6D0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x2E20"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x2E20"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x2F80"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x154C0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x31E0"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x15740"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xDEB0"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x2A390"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x2CD80"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x2D000"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x2D290"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x2D400"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA0C0"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xA0C0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x32B0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xDCF0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x48B0"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x16F50"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x58B0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5460"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x5CF0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x2E090"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x2DF70"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5390"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x17B90"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x180B0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x17C60"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x184F0"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x18930"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x18A70"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6130"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x18BB0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6170"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x18CA0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6300"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x18E50"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x6CC0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x66D0"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x19980"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x19360"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x9C50"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0x9C50"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x73F0"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x19FA0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7810"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x79E0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7B80"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7B80"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x1A430"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x7D10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x7F30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x80B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9840"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8240"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8460"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8700"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8920"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8AA0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x8CC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x8EE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9060"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9280"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9400"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9620"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x1A5B0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x1A750"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x1A8F0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x1AA80"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x1AC20"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x1ADC0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x1AF60"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x1B110"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x1B2B0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x99D0"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x1B450"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x1B640"}, "fmt::v8::format_error::format_error": {"offset": "0x9E50"}, "fmt::v8::format_error::~format_error": {"offset": "0xA1F0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x31400"}, "fmt::v8::sprintf<wchar_t [32],unsigned __int64,wchar_t>": {"offset": "0x15830"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3720"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x160E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3500"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x15EB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x33D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x15DA0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x32E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x15C70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3720"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x160E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x35F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x15FB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3850"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x16220"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x43B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x16CD0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3DE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x16770"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x17930"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x31CA0"}, "fprintf": {"offset": "0xDF70"}, "fwEvent<wchar_t const *,wchar_t const *>::ConnectInternal": {"offset": "0x23AB0"}, "fwPlatformString::fwPlatformString": {"offset": "0x1C580"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA210"}, "fwRefCountable::AddRef": {"offset": "0x32DE0"}, "fwRefCountable::Release": {"offset": "0x32DF0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x32DD0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::Call": {"offset": "0x23210"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0xFBE0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x23440"}, "internal::ConsoleVariableEntry<int>::ConsoleVariableEntry<int>": {"offset": "0x1B940"}, "internal::ConsoleVariableEntry<int>::GetOfflineValue": {"offset": "0xE960"}, "internal::ConsoleVariableEntry<int>::GetValue": {"offset": "0xE820"}, "internal::ConsoleVariableEntry<int>::SaveOfflineValue": {"offset": "0xE980"}, "internal::ConsoleVariableEntry<int>::SetRawValue": {"offset": "0x27970"}, "internal::ConsoleVariableEntry<int>::SetValue": {"offset": "0xE860"}, "internal::ConsoleVariableEntry<int>::UpdateTrackingVariable": {"offset": "0xE990"}, "internal::Constraints<int,void>::Compare": {"offset": "0x236A0"}, "internal::UnparseArgument<int>": {"offset": "0x13620"}, "launch::IsSDKGuest": {"offset": "0x307F0"}, "rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>::Peek": {"offset": "0x276E0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1CFD0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x24650"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x24760"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x121C0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::String": {"offset": "0x27D00"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x9ED0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1D000"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::NumberStream<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,0,0>::Peek": {"offset": "0x27700"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x11020"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x11270"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x11340"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x11D40"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x123D0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x127D0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x278E0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x9F70"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::GetString": {"offset": "0x271C0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x9F70"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x17E0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB040"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::FindMember<rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x10130"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetInt": {"offset": "0x27180"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetString": {"offset": "0x27260"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x9FA0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC190"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC250"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC4C0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xC960"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0x9FB0"}, "rapidjson::internal::DigitGen": {"offset": "0xB2F0"}, "rapidjson::internal::FastPath": {"offset": "0x24870"}, "rapidjson::internal::Grisu2": {"offset": "0xBDA0"}, "rapidjson::internal::Prettify": {"offset": "0xC300"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x12E90"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2270"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2340"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0x9F70"}, "rapidjson::internal::WriteExponent": {"offset": "0xC8D0"}, "rapidjson::internal::u32toa": {"offset": "0xD430"}, "rapidjson::internal::u64toa": {"offset": "0xD6A0"}, "se::Object::~Object": {"offset": "0xA0C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<net::PeerAddress const ,std::shared_ptr<gameserveritemext_t> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<net::PeerAddress const ,std::shared_ptr<gameserveritemext_t> >,void *> > >": {"offset": "0x1CAD0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x1CA90"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x9FE0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >,void *> > >": {"offset": "0x1CAB0"}, "std::_Destroy_range<std::allocator<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x138D0"}, "std::_Facet_Register": {"offset": "0x3537C"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x1C7E0"}, "std::_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1C7E0"}, "std::_Func_class<bool,wchar_t const *,wchar_t const *>::~_Func_class<bool,wchar_t const *,wchar_t const *>": {"offset": "0x1C7E0"}, "std::_Func_class<void,ProgressInfo const &>::~_Func_class<void,ProgressInfo const &>": {"offset": "0x1C7E0"}, "std::_Func_class<void,bool,char const *,unsigned __int64>::~_Func_class<void,bool,char const *,unsigned __int64>": {"offset": "0x1C7E0"}, "std::_Func_class<void,int const &>::~_Func_class<void,int const &>": {"offset": "0x1C7E0"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1C7E0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x1C7E0"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x27E70"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x28050"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x28270"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_11c5c011f2722fe5846e3ebc09b80519>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x28570"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Copy": {"offset": "0xE4D0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Delete_this": {"offset": "0xE550"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Do_call": {"offset": "0xE530"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Target_type": {"offset": "0xE540"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x27EF0"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x28090"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x28290"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_47c7abed81b887d9b1f03e84be315afa>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x28580"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xE2F0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xE290"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xE370"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xE3C0"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0xE5F0"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0xE7A0"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0xE770"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE630"}, "std::_Func_impl_no_alloc<<lambda_7871d71d345db730e5e5ee19837ae8ba>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0xE790"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Copy": {"offset": "0xE9B0"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Delete_this": {"offset": "0xE760"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Do_call": {"offset": "0xE9C0"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Move": {"offset": "0xE9B0"}, "std::_Func_impl_no_alloc<<lambda_7dc6a386b82acc3fef031832612a7a66>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::_Target_type": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Copy": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Delete_this": {"offset": "0xE760"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Do_call": {"offset": "0xEB00"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Move": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_7f9830bd709697b0ba9177318203469a>,bool,wchar_t const *,wchar_t const *>::_Target_type": {"offset": "0xEB20"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x27F60"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x280D0"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x282B0"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_b5c74389bb0c23f2fc47efd332539dd8>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x28590"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Copy": {"offset": "0xE5D0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Delete_this": {"offset": "0xE760"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Do_call": {"offset": "0xE670"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Move": {"offset": "0xE5D0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Target_type": {"offset": "0xE750"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x27FE0"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x28090"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x282D0"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_ce34850a21ec92d50ae707a6354bd250>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x285A0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xE1A0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xE290"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xE220"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xE280"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE080"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xE270"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x33AA0"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x13DF0"}, "std::_Maklocstr<char>": {"offset": "0xDFC0"}, "std::_Maklocstr<wchar_t>": {"offset": "0x2AAB0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE080"}, "std::_Ref_count_obj2<gameserveritemext_t>::_Delete_this": {"offset": "0xE480"}, "std::_Ref_count_obj2<gameserveritemext_t>::_Destroy": {"offset": "0x28110"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Delete_this": {"offset": "0xE480"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Destroy": {"offset": "0xE3D0"}, "std::_Ref_count_obj2<std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >::_Delete_this": {"offset": "0xE480"}, "std::_Ref_count_obj2<std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >::_Destroy": {"offset": "0x28240"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x286D0"}, "std::_Throw_bad_array_new_length": {"offset": "0xCD00"}, "std::_Throw_bad_cast": {"offset": "0x285B0"}, "std::_Throw_tree_length_error": {"offset": "0xCD20"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x2E2E0"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x2E2E0"}, "std::_Traits_find<std::char_traits<char> >": {"offset": "0x14860"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Find<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13D30"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Copy_nodes<0>": {"offset": "0x137E0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Getal": {"offset": "0x14F40"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2510"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2790"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA000"}, "std::_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >::~_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >": {"offset": "0x34080"}, "std::_Tree_head_scoped_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >::~_Tree_head_scoped_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >": {"offset": "0x1CB00"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >,void *> > >": {"offset": "0x1CAB0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<net::PeerAddress const ,std::shared_ptr<gameserveritemext_t> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<net::PeerAddress const ,std::shared_ptr<gameserveritemext_t> >,void *> > >": {"offset": "0x13C80"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<net::PeerAddress const ,std::shared_ptr<gameserveritemext_t> > > >::_Insert_node": {"offset": "0xCAA0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x13B80"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x13B80"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xCAA0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x24B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCAA0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >,void *> > >": {"offset": "0x13C00"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> > > >::_Insert_node": {"offset": "0xCAA0"}, "std::_Tree_val<std::_Tree_simple_types<tbb::detail::d1::global_control *> >::_Erase_tree<tbb::detail::d1::tbb_allocator<std::_Tree_node<tbb::detail::d1::global_control *,void *> > >": {"offset": "0x34010"}, "std::_Uninitialized_backout_al<std::allocator<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~_Uninitialized_backout_al<std::allocator<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1CB40"}, "std::_Xlen_string": {"offset": "0xCD40"}, "std::allocator<char>::allocate": {"offset": "0xCD60"}, "std::allocator<char>::deallocate": {"offset": "0x32A50"}, "std::allocator<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::allocate": {"offset": "0x286F0"}, "std::allocator<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x28A30"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x32A50"}, "std::allocator<wchar_t>::allocate": {"offset": "0xCDC0"}, "std::bad_alloc::bad_alloc": {"offset": "0x33230"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA1F0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x9DE0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA1F0"}, "std::bad_cast::bad_cast": {"offset": "0x1C550"}, "std::bad_cast::~bad_cast": {"offset": "0xA1F0"}, "std::basic_filebuf<char,std::char_traits<char> >::_Endwrite": {"offset": "0x282F0"}, "std::basic_filebuf<char,std::char_traits<char> >::_Lock": {"offset": "0x284D0"}, "std::basic_filebuf<char,std::char_traits<char> >::_Reset_back": {"offset": "0x284F0"}, "std::basic_filebuf<char,std::char_traits<char> >::_Unlock": {"offset": "0x28690"}, "std::basic_filebuf<char,std::char_traits<char> >::close": {"offset": "0x288D0"}, "std::basic_filebuf<char,std::char_traits<char> >::imbue": {"offset": "0x28C30"}, "std::basic_filebuf<char,std::char_traits<char> >::overflow": {"offset": "0x29020"}, "std::basic_filebuf<char,std::char_traits<char> >::pbackfail": {"offset": "0x29370"}, "std::basic_filebuf<char,std::char_traits<char> >::seekoff": {"offset": "0x29740"}, "std::basic_filebuf<char,std::char_traits<char> >::seekpos": {"offset": "0x298E0"}, "std::basic_filebuf<char,std::char_traits<char> >::setbuf": {"offset": "0x29A20"}, "std::basic_filebuf<char,std::char_traits<char> >::sync": {"offset": "0x29B90"}, "std::basic_filebuf<char,std::char_traits<char> >::uflow": {"offset": "0x29D00"}, "std::basic_filebuf<char,std::char_traits<char> >::underflow": {"offset": "0x2A290"}, "std::basic_filebuf<char,std::char_traits<char> >::xsgetn": {"offset": "0x2A440"}, "std::basic_filebuf<char,std::char_traits<char> >::xsputn": {"offset": "0x2A590"}, "std::basic_filebuf<char,std::char_traits<char> >::~basic_filebuf<char,std::char_traits<char> >": {"offset": "0x1CB80"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::_Endwrite": {"offset": "0x283E0"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::_Lock": {"offset": "0x284D0"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::_Reset_back": {"offset": "0x28530"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::_Unlock": {"offset": "0x28690"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::close": {"offset": "0x28980"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::imbue": {"offset": "0x28C80"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::overflow": {"offset": "0x291E0"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::pbackfail": {"offset": "0x29490"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::seekoff": {"offset": "0x29810"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::seekpos": {"offset": "0x29980"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::setbuf": {"offset": "0x29B00"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::sync": {"offset": "0x29BE0"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::uflow": {"offset": "0x29FC0"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::underflow": {"offset": "0x2A310"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::xsgetn": {"offset": "0x2A580"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::xsputn": {"offset": "0x2A650"}, "std::basic_filebuf<wchar_t,std::char_traits<wchar_t> >::~basic_filebuf<wchar_t,std::char_traits<wchar_t> >": {"offset": "0x1CBF0"}, "std::basic_ifstream<char,std::char_traits<char> >::basic_ifstream<char,std::char_traits<char> >": {"offset": "0x1BBC0"}, "std::basic_istream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x1D210"}, "std::basic_istream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x1D2D0"}, "std::basic_ofstream<wchar_t,std::char_traits<wchar_t> >::basic_ofstream<wchar_t,std::char_traits<wchar_t> >": {"offset": "0x1BDD0"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x1D210"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x1D310"}, "std::basic_ostream<wchar_t,std::char_traits<wchar_t> >::_Sentry_base::~_Sentry_base": {"offset": "0x1D250"}, "std::basic_ostream<wchar_t,std::char_traits<wchar_t> >::sentry::~sentry": {"offset": "0x1D360"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char *>": {"offset": "0x23F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x23F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x141E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_55b0f89b53a0342680e40398a0d39887>,unsigned __int64,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x14350"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x14520"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x30BD0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x146B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x28760"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xCFB0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9B80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::replace": {"offset": "0x295A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA0C0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x13710"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xCE30"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x1C580"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x32A90"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x32BF0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA120"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x14C50"}, "std::exception::exception": {"offset": "0x9E10"}, "std::exception::what": {"offset": "0xDE90"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x1C7E0"}, "std::function<bool __cdecl(wchar_t const *,wchar_t const *)>::~function<bool __cdecl(wchar_t const *,wchar_t const *)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(ProgressInfo const &)>::~function<void __cdecl(ProgressInfo const &)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(bool,char const *,unsigned __int64)>::~function<void __cdecl(bool,char const *,unsigned __int64)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(int const &)>::~function<void __cdecl(int const &)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1BF90"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >,std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>)>": {"offset": "0x1C7E0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x1C7E0"}, "std::get<0,<lambda_59e2a1791af6bface35c61ef255959a3> >": {"offset": "0x14F40"}, "std::invalid_argument::invalid_argument": {"offset": "0x33310"}, "std::invalid_argument::~invalid_argument": {"offset": "0xA1F0"}, "std::invoke<<lambda_59e2a1791af6bface35c61ef255959a3> >": {"offset": "0x15410"}, "std::length_error::length_error": {"offset": "0x333A0"}, "std::length_error::~length_error": {"offset": "0xA1F0"}, "std::locale::~locale": {"offset": "0x1D290"}, "std::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>": {"offset": "0x34640"}, "std::logic_error::logic_error": {"offset": "0x333F0"}, "std::make_shared<std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >": {"offset": "0x15420"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Try_emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14930"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1CCC0"}, "std::map<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::shared_ptr<gameserveritemext_t>,std::less<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> > > >::~map<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::shared_ptr<gameserveritemext_t>,std::less<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> > > >": {"offset": "0x1CCF0"}, "std::move<<lambda_59e2a1791af6bface35c61ef255959a3> &>": {"offset": "0x14F40"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x2EC60"}, "std::numpunct<char>::do_falsename": {"offset": "0x2EC80"}, "std::numpunct<char>::do_grouping": {"offset": "0x2ED00"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x2ED40"}, "std::numpunct<char>::do_truename": {"offset": "0x2ED60"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x2E130"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x2E500"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x2EC70"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x2ECC0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x2ED00"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x2ED50"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x2EDA0"}, "std::out_of_range::out_of_range": {"offset": "0x334D0"}, "std::out_of_range::~out_of_range": {"offset": "0xA1F0"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CD30"}, "std::pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >::~pair<std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > const ,std::shared_ptr<gameserveritemext_t> >": {"offset": "0x1CDE0"}, "std::recursive_mutex::~recursive_mutex": {"offset": "0x1D2C0"}, "std::runtime_error::runtime_error": {"offset": "0x33520"}, "std::runtime_error::~runtime_error": {"offset": "0xA1F0"}, "std::shared_ptr<gameserveritemext_t>::~shared_ptr<gameserveritemext_t>": {"offset": "0x1CE80"}, "std::shared_ptr<int>::~shared_ptr<int>": {"offset": "0x1CE80"}, "std::shared_ptr<internal::ConsoleVariableEntry<int> >::~shared_ptr<internal::ConsoleVariableEntry<int> >": {"offset": "0x1CE80"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x1CE80"}, "std::shared_ptr<std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >::~shared_ptr<std::multimap<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,HttpIgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > >": {"offset": "0x1CE80"}, "std::thread::_Invoke<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,0>": {"offset": "0x14060"}, "std::thread::~thread": {"offset": "0x1D3B0"}, "std::to_string": {"offset": "0x29C40"}, "std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1C0E0"}, "std::tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<int,net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CED0"}, "std::tuple<net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Less<net::PeerAddress,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x140A0"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xA0C0"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CD30"}, "std::unique_lock<std::mutex>::~unique_lock<std::mutex>": {"offset": "0x1CEE0"}, "std::unique_lock<std::recursive_mutex>::~unique_lock<std::recursive_mutex>": {"offset": "0x1CEE0"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0x34720"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x1CF10"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x1CF40"}, "std::unique_ptr<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > > >::get": {"offset": "0x28B00"}, "std::unique_ptr<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > > >::unique_ptr<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > > ><std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > >,0>": {"offset": "0xEB60"}, "std::unique_ptr<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > > >::~unique_ptr<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> >,std::default_delete<std::tuple<<lambda_59e2a1791af6bface35c61ef255959a3> > > >": {"offset": "0x1CEF0"}, "std::use_facet<std::codecvt<char,char,_Mbstatet> >": {"offset": "0x159A0"}, "std::use_facet<std::codecvt<wchar_t,char,_Mbstatet> >": {"offset": "0x15A90"}, "std::use_facet<std::ctype<char> >": {"offset": "0x15B80"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x2DD10"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x2DE80"}, "tbb::detail::d0::raii_guard<<lambda_34676882c90d3454845fd2efa3e5d7a7> >::~raii_guard<<lambda_34676882c90d3454845fd2efa3e5d7a7> >": {"offset": "0x1CE50"}, "tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::destroy_node": {"offset": "0x28A80"}, "tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::init_bucket": {"offset": "0x28CD0"}, "tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::internal_clear": {"offset": "0x28F60"}, "tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::internal_find<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14F50"}, "tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::internal_insert<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,<lambda_14ef358a6050970295aca9b934844cc4> >": {"offset": "0x150E0"}, "tbb::detail::d1::segment_table<std::atomic<tbb::detail::d1::list_node<unsigned __int64> *>,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::concurrent_unordered_base<tbb::detail::d1::concurrent_unordered_set_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,tbb::detail::d1::tbb_allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::unordered_segment_table,63>::clear": {"offset": "0x287F0"}, "tbb::detail::d1::unique_scoped_lock<tbb::detail::d1::spin_mutex>::~unique_scoped_lock<tbb::detail::d1::spin_mutex>": {"offset": "0x340F0"}, "tbb::detail::r1::AvailableHwConcurrency": {"offset": "0x34340"}, "tbb::detail::r1::PrintExtraVersionInfo": {"offset": "0x34160"}, "tbb::detail::r1::`dynamic initializer for '__TBB_InitOnceHiddenInstance''": {"offset": "0x1630"}, "tbb::detail::r1::`dynamic initializer for 'allowed_parallelism_ctl''": {"offset": "0x14C0"}, "tbb::detail::r1::`dynamic initializer for 'lifetime_ctl''": {"offset": "0x1510"}, "tbb::detail::r1::`dynamic initializer for 'stack_size_ctl''": {"offset": "0x1560"}, "tbb::detail::r1::`dynamic initializer for 'terminate_on_exception_ctl''": {"offset": "0x15B0"}, "tbb::detail::r1::allocate_memory": {"offset": "0x33AD0"}, "tbb::detail::r1::allowed_parallelism_control::active_value": {"offset": "0x33D90"}, "tbb::detail::r1::allowed_parallelism_control::apply_active": {"offset": "0x33D80"}, "tbb::detail::r1::allowed_parallelism_control::default_value": {"offset": "0x33CF0"}, "tbb::detail::r1::allowed_parallelism_control::is_first_arg_preferred": {"offset": "0x33D70"}, "tbb::detail::r1::arena::has_enqueued_tasks": {"offset": "0x35130"}, "tbb::detail::r1::bad_last_alloc::bad_last_alloc": {"offset": "0x332A0"}, "tbb::detail::r1::bad_last_alloc::what": {"offset": "0x33A10"}, "tbb::detail::r1::bad_last_alloc::~bad_last_alloc": {"offset": "0xA1F0"}, "tbb::detail::r1::clear_address_waiter_table": {"offset": "0x34EC0"}, "tbb::detail::r1::concurrent_monitor_mutex::get_semaphore": {"offset": "0x348B0"}, "tbb::detail::r1::control_storage::active_value": {"offset": "0x33C60"}, "tbb::detail::r1::control_storage::apply_active": {"offset": "0x33C40"}, "tbb::detail::r1::control_storage::is_first_arg_preferred": {"offset": "0x33C50"}, "tbb::detail::r1::deallocate_memory": {"offset": "0x33B00"}, "tbb::detail::r1::detect_cpu_features": {"offset": "0x34230"}, "tbb::detail::r1::do_throw<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x32E70"}, "tbb::detail::r1::do_throw<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x32EA0"}, "tbb::detail::r1::do_throw<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x32ED0"}, "tbb::detail::r1::do_throw<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x32F00"}, "tbb::detail::r1::do_throw<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x32F30"}, "tbb::detail::r1::do_throw<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x32F60"}, "tbb::detail::r1::do_throw<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x32F90"}, "tbb::detail::r1::do_throw<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x32FC0"}, "tbb::detail::r1::do_throw<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x32FF0"}, "tbb::detail::r1::do_throw<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x33020"}, "tbb::detail::r1::do_throw<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x33050"}, "tbb::detail::r1::do_throw<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x33080"}, "tbb::detail::r1::do_throw_noexcept<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x330B0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x330D0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x330F0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x33110"}, "tbb::detail::r1::do_throw_noexcept<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x33130"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x33150"}, "tbb::detail::r1::do_throw_noexcept<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x33170"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x33190"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x331B0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x331D0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x331F0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x33210"}, "tbb::detail::r1::dummy_allocate_binding_handler": {"offset": "0xE080"}, "tbb::detail::r1::dummy_apply_affinity": {"offset": "0x9FA0"}, "tbb::detail::r1::dummy_deallocate_binding_handler": {"offset": "0x9FA0"}, "tbb::detail::r1::dummy_destroy_system_topology": {"offset": "0x9FA0"}, "tbb::detail::r1::dummy_get_default_concurrency": {"offset": "0x34E20"}, "tbb::detail::r1::dummy_restore_affinity": {"offset": "0x9FA0"}, "tbb::detail::r1::dynamic_link": {"offset": "0x34150"}, "tbb::detail::r1::dynamic_unlink": {"offset": "0x9FA0"}, "tbb::detail::r1::dynamic_unlink_all": {"offset": "0x9FA0"}, "tbb::detail::r1::gcc_rethrow_exception_broken": {"offset": "0xE140"}, "tbb::detail::r1::get_address_waiter_table": {"offset": "0x35060"}, "tbb::detail::r1::governor::acquire_resources": {"offset": "0x34E30"}, "tbb::detail::r1::governor::default_num_threads": {"offset": "0x34830"}, "tbb::detail::r1::governor::release_resources": {"offset": "0x34E80"}, "tbb::detail::r1::handle_perror": {"offset": "0x33890"}, "tbb::detail::r1::initialize_allocate_handler": {"offset": "0x33A40"}, "tbb::detail::r1::initialize_cache_aligned_allocate_handler": {"offset": "0x33A70"}, "tbb::detail::r1::initialize_cache_aligned_allocator": {"offset": "0x33B10"}, "tbb::detail::r1::initialize_hardware_concurrency_info": {"offset": "0x34400"}, "tbb::detail::r1::lifetime_control::apply_active": {"offset": "0x33EA0"}, "tbb::detail::r1::lifetime_control::default_value": {"offset": "0xE080"}, "tbb::detail::r1::lifetime_control::is_first_arg_preferred": {"offset": "0xE140"}, "tbb::detail::r1::market::add_ref_unsafe": {"offset": "0x34730"}, "tbb::detail::r1::market::app_parallelism_limit": {"offset": "0x34110"}, "tbb::detail::r1::market::release": {"offset": "0x34950"}, "tbb::detail::r1::market::set_active_num_workers": {"offset": "0x34AD0"}, "tbb::detail::r1::market::update_allotment": {"offset": "0x34D10"}, "tbb::detail::r1::missing_wait::missing_wait": {"offset": "0x33470"}, "tbb::detail::r1::missing_wait::what": {"offset": "0x33A20"}, "tbb::detail::r1::missing_wait::~missing_wait": {"offset": "0xA1F0"}, "tbb::detail::r1::runtime_warning": {"offset": "0x34280"}, "tbb::detail::r1::stack_size_control::apply_active": {"offset": "0x33C40"}, "tbb::detail::r1::stack_size_control::default_value": {"offset": "0x33E90"}, "tbb::detail::r1::std_cache_aligned_allocate": {"offset": "0x33C20"}, "tbb::detail::r1::std_cache_aligned_deallocate": {"offset": "0x33C30"}, "tbb::detail::r1::terminate_on_exception": {"offset": "0x34130"}, "tbb::detail::r1::terminate_on_exception_control::default_value": {"offset": "0xE080"}, "tbb::detail::r1::throw_exception": {"offset": "0x33960"}, "tbb::detail::r1::unsafe_wait::unsafe_wait": {"offset": "0x335B0"}, "tbb::detail::r1::unsafe_wait::~unsafe_wait": {"offset": "0xA1F0"}, "tbb::detail::r1::user_abort::user_abort": {"offset": "0x33640"}, "tbb::detail::r1::user_abort::what": {"offset": "0x33A30"}, "tbb::detail::r1::user_abort::~user_abort": {"offset": "0xA1F0"}, "utf8::exception::exception": {"offset": "0x31DF0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x30E70"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x317A0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x31E80"}, "utf8::invalid_code_point::what": {"offset": "0x32DA0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA1F0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x31EF0"}, "utf8::invalid_utf8::what": {"offset": "0x32DB0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA1F0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x31F50"}, "utf8::not_enough_room::what": {"offset": "0x32DC0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA1F0"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x31580"}, "vva": {"offset": "0x32D80"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1060"}}}