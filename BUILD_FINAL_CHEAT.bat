@echo off
title FiveM Cheat - BUILD FINAL DLL
color 0A

echo ========================================
echo    FIVEM CHEAT - BUILD FINAL DLL
echo ========================================
echo.
echo Compilando DLL com offsets HARDCODED!
echo Nao precisa buscar offsets em runtime.
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo PASSO 1: VERIFICANDO OFFSETS
echo ========================================

REM Verificar se offsets_auto.h existe
if not exist "examples\offsets_auto.h" (
    echo [INFO] Extraindo offsets dos arquivos JSON...
    python extract_offsets.py
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao extrair offsets!
        pause
        exit /b 1
    )
    echo [OK] Offsets extraidos com sucesso
) else (
    echo [OK] Offsets ja extraidos (155 offsets)
)

echo.
echo OFFSETS PRINCIPAIS HARDCODED:
echo - MODULE_BASE: 0x140000000 (FiveM_GameProcess.exe)
echo - GetPlayerPed: 0x141639CE8 (absoluto)
echo - GetAllPhysicalPlayers: 0x14118B6EB (absoluto)
echo - CWorld: 0x140737F0C (absoluto)
echo - g_vehiclePool: 0x140D7383C (absoluto)
echo - WeaponDamageModifier: 0x140415BD3 (absoluto)
echo - InfiniteAmmo: 0x14010695C1 (absoluto)
echo.

echo ========================================
echo PASSO 2: CONFIGURANDO IMGUI
echo ========================================

if not exist "external\imgui" (
    echo [INFO] Configurando Dear ImGui...
    call setup_imgui.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao configurar ImGui!
        pause
        exit /b 1
    )
    echo [OK] ImGui configurado
) else (
    echo [OK] ImGui ja configurado
)

echo.

echo ========================================
echo PASSO 3: COMPILANDO DLL FINAL
echo ========================================

cd examples

echo [INFO] Compilando FiveMCheat_FINAL.dll...
echo [INFO] Usando offsets HARDCODED (nao busca em runtime)
echo [INFO] Base: 0x140000000 + offsets relativos
echo.

REM Limpar builds anteriores
if exist "FiveMCheat_FINAL.dll" del "FiveMCheat_FINAL.dll"
if exist "FiveMSimpleCheat.dll" del "FiveMSimpleCheat.dll"

REM Compilar com nome especifico
echo Compilando com Visual Studio...

REM Tentar diferentes versoes do Visual Studio
set "VS_FOUND=0"

REM Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    echo [OK] Visual Studio 2022 encontrado
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    echo [OK] Visual Studio 2019 encontrado
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    echo [OK] Visual Studio 2017 encontrado
)

if "%VS_FOUND%"=="0" (
    echo [ERROR] Visual Studio nao encontrado!
    echo.
    echo INSTALE:
    echo 1. Visual Studio Community (gratuito)
    echo 2. Ou Visual Studio Build Tools
    echo.
    cd ..
    pause
    exit /b 1
)

echo.
echo [INFO] Compilando DLL...

REM Compilar DLL
cl.exe /LD /EHsc /std:c++17 ^
    /I"..\external\imgui" ^
    /I"..\external\imgui\backends" ^
    simple_cheat.cpp ^
    ..\external\imgui\*.cpp ^
    ..\external\imgui\backends\imgui_impl_dx11.cpp ^
    ..\external\imgui\backends\imgui_impl_win32.cpp ^
    /Fe:FiveMCheat_FINAL.dll ^
    /link user32.lib gdi32.lib d3d11.lib

if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao!
    echo.
    echo POSSIVEIS CAUSAS:
    echo - Erro nos offsets
    echo - Dependencias ausentes
    echo - Erro no codigo
    echo.
    cd ..
    pause
    exit /b 1
)

REM Verificar se DLL foi criada
if exist "FiveMCheat_FINAL.dll" (
    echo [OK] FiveMCheat_FINAL.dll compilada com sucesso!
    echo.
    echo INFORMACOES DA DLL:
    dir FiveMCheat_FINAL.dll | find "FiveMCheat_FINAL.dll"
    echo.
    
    REM Copiar para pasta raiz
    copy "FiveMCheat_FINAL.dll" "..\FiveMCheat_FINAL.dll"
    echo [OK] DLL copiada para pasta raiz
    
) else (
    echo [ERROR] FiveMCheat_FINAL.dll nao foi criada!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.

echo ========================================
echo PASSO 4: COMPILANDO INJETOR
echo ========================================

if not exist "SimpleInjector.exe" (
    echo [INFO] Compilando injetor...
    cd tools
    call build_injector.bat
    cd ..
    
    if exist "SimpleInjector.exe" (
        echo [OK] SimpleInjector.exe criado
    ) else (
        echo [WARNING] Injetor nao foi criado (use metodo manual)
    )
) else (
    echo [OK] SimpleInjector.exe ja existe
)

echo.

echo ========================================
echo BUILD CONCLUIDO COM SUCESSO!
echo ========================================
echo.
echo 📁 ARQUIVOS CRIADOS:
echo.
echo ✅ FiveMCheat_FINAL.dll - Cheat principal (pasta raiz)
echo ✅ examples\FiveMCheat_FINAL.dll - Copia na pasta examples
echo ✅ examples\offsets_auto.h - Header com 155 offsets hardcoded
echo ✅ SimpleInjector.exe - Injetor (se disponivel)
echo.
echo 🎯 CARACTERISTICAS DA DLL:
echo.
echo ✅ 155 offsets HARDCODED (nao busca em runtime)
echo ✅ Base fixa: 0x140000000 (FiveM_GameProcess.exe)
echo ✅ Enderecos absolutos pre-calculados
echo ✅ Nao precisa de arquivos JSON externos
echo ✅ Funciona independentemente
echo ✅ Otimizada para performance
echo.
echo 🚀 COMO USAR:
echo.
echo 1. Abra o FiveM e entre em um servidor
echo 2. Execute como ADMIN: SimpleInjector.exe
echo 3. Selecione opcao 2 (Injetar DLL)
echo 4. Selecione: FiveMCheat_FINAL.dll
echo 5. Aguarde confirmacao de injecao
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [INSERT] - Menu principal
echo [F1]     - Toggle ESP
echo [F2]     - Toggle God Mode
echo [F3]     - Teleport para waypoint
echo [F4]     - Reparar veiculo
echo [F5]     - Munição infinita
echo.
echo 📋 FUNCIONALIDADES:
echo.
echo ✅ ESP (ver jogadores atraves das paredes)
echo ✅ God Mode (vida/armadura infinita)
echo ✅ Teleport (coordenadas/waypoint)
echo ✅ Weapon Hacks (munição infinita, sem recarga)
echo ✅ Vehicle Hacks (reparar, velocidade, invencibilidade)
echo ✅ Menu ImGui completo
echo.
echo ⚠️  IMPORTANTE:
echo.
echo ✅ DLL compilada com offsets REAIS do seu dumper
echo ✅ Nao precisa buscar offsets em runtime
echo ✅ Performance otimizada
echo ✅ Use apenas em servidores privados/teste
echo ✅ Compativel com versao atual do FiveM
echo.

REM Criar arquivo de informacoes
echo FiveM Cheat FINAL Build - %date% %time% > BUILD_INFO.txt
echo. >> BUILD_INFO.txt
echo Status: Build concluido com sucesso >> BUILD_INFO.txt
echo DLL: FiveMCheat_FINAL.dll >> BUILD_INFO.txt
echo Offsets: 155 hardcoded (absolutos) >> BUILD_INFO.txt
echo Base: 0x140000000 (FiveM_GameProcess.exe) >> BUILD_INFO.txt
echo. >> BUILD_INFO.txt
echo Principais offsets hardcoded: >> BUILD_INFO.txt
echo - GetPlayerPed: 0x141639CE8 >> BUILD_INFO.txt
echo - GetAllPhysicalPlayers: 0x14118B6EB >> BUILD_INFO.txt
echo - CWorld: 0x140737F0C >> BUILD_INFO.txt
echo - g_vehiclePool: 0x140D7383C >> BUILD_INFO.txt
echo - WeaponDamageModifier: 0x140415BD3 >> BUILD_INFO.txt
echo - InfiniteAmmo: 0x14010695C1 >> BUILD_INFO.txt
echo. >> BUILD_INFO.txt

echo ========================================
echo ✅ DLL FINAL PRONTA PARA USO!
echo Informacoes salvas em: BUILD_INFO.txt
echo ========================================

echo.
echo Deseja abrir o injetor agora? (s/n)
set /p choice="> "

if /i "%choice%"=="s" (
    if exist "SimpleInjector.exe" (
        echo [INFO] Abrindo SimpleInjector...
        start SimpleInjector.exe
    ) else (
        echo [INFO] Use Process Hacker ou outro injetor
        echo Arquivo: FiveMCheat_FINAL.dll
        echo Processo: FiveM_GameProcess.exe
    )
)

echo.
echo Pressione qualquer tecla para sair...
pause >nul
