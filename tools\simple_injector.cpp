/*
 * Simple DLL Injector for FiveM Testing
 * Facilita a injeção das DLLs durante os testes
 */

#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>
#include <vector>

class SimpleInjector {
private:
    DWORD GetProcessId(const std::wstring& processName) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }

        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);

        if (Process32FirstW(hSnapshot, &pe32)) {
            do {
                if (processName == pe32.szExeFile) {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }

    bool InjectDLL(DWORD processId, const std::wstring& dllPath) {
        // Abrir processo
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::wcout << L"[ERROR] Falha ao abrir processo. Erro: " << GetLastError() << std::endl;
            return false;
        }

        // Alocar memória no processo alvo
        SIZE_T pathSize = (dllPath.length() + 1) * sizeof(wchar_t);
        LPVOID pRemotePath = VirtualAllocEx(hProcess, NULL, pathSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!pRemotePath) {
            std::wcout << L"[ERROR] Falha ao alocar memória. Erro: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        // Escrever caminho da DLL na memória do processo
        SIZE_T bytesWritten;
        if (!WriteProcessMemory(hProcess, pRemotePath, dllPath.c_str(), pathSize, &bytesWritten)) {
            std::wcout << L"[ERROR] Falha ao escrever na memória. Erro: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Obter endereço da função LoadLibraryW
        HMODULE hKernel32 = GetModuleHandleW(L"kernel32.dll");
        if (!hKernel32) {
            std::wcout << L"[ERROR] Falha ao obter handle do kernel32.dll" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        LPVOID pLoadLibrary = GetProcAddress(hKernel32, "LoadLibraryW");
        if (!pLoadLibrary) {
            std::wcout << L"[ERROR] Falha ao obter endereço do LoadLibraryW" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Criar thread remota para carregar a DLL
        HANDLE hRemoteThread = CreateRemoteThread(hProcess, NULL, 0, 
            (LPTHREAD_START_ROUTINE)pLoadLibrary, pRemotePath, 0, NULL);
        
        if (!hRemoteThread) {
            std::wcout << L"[ERROR] Falha ao criar thread remota. Erro: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Aguardar thread completar
        WaitForSingleObject(hRemoteThread, INFINITE);

        // Verificar se a DLL foi carregada
        DWORD exitCode;
        GetExitCodeThread(hRemoteThread, &exitCode);

        // Limpeza
        CloseHandle(hRemoteThread);
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);

        if (exitCode == 0) {
            std::wcout << L"[ERROR] LoadLibrary retornou NULL" << std::endl;
            return false;
        }

        return true;
    }

public:
    void ShowMenu() {
        std::wcout << L"========================================" << std::endl;
        std::wcout << L"    Simple FiveM DLL Injector v1.0" << std::endl;
        std::wcout << L"========================================" << std::endl;
        std::wcout << L"" << std::endl;
    }

    std::vector<DWORD> FindFiveMProcesses() {
        std::vector<DWORD> processes;
        
        // Procurar por diferentes nomes de processo do FiveM
        std::vector<std::wstring> processNames = {
            L"FiveM.exe",
            L"FiveM_GTAProcess.exe",
            L"FiveM_GameProcess.exe"
        };

        for (const auto& name : processNames) {
            DWORD pid = GetProcessId(name);
            if (pid != 0) {
                processes.push_back(pid);
                std::wcout << L"[OK] Processo encontrado: " << name << L" (PID: " << pid << L")" << std::endl;
            }
        }

        return processes;
    }

    bool InjectToFiveM(const std::wstring& dllPath) {
        // Verificar se arquivo existe
        if (GetFileAttributesW(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
            std::wcout << L"[ERROR] Arquivo não encontrado: " << dllPath << std::endl;
            return false;
        }

        // Encontrar processos do FiveM
        auto processes = FindFiveMProcesses();
        if (processes.empty()) {
            std::wcout << L"[ERROR] Nenhum processo do FiveM encontrado!" << std::endl;
            std::wcout << L"Certifique-se de que o FiveM está rodando." << std::endl;
            return false;
        }

        // Injetar no primeiro processo encontrado
        DWORD targetPid = processes[0];
        std::wcout << L"[INFO] Injetando " << dllPath << L" no processo " << targetPid << L"..." << std::endl;

        if (InjectDLL(targetPid, dllPath)) {
            std::wcout << L"[OK] DLL injetada com sucesso!" << std::endl;
            return true;
        } else {
            std::wcout << L"[ERROR] Falha na injeção!" << std::endl;
            return false;
        }
    }

    void Run() {
        ShowMenu();

        while (true) {
            std::wcout << L"Selecione uma opção:" << std::endl;
            std::wcout << L"1. Injetar FiveMOffsetDumper.dll" << std::endl;
            std::wcout << L"2. Injetar FiveMSimpleCheat.dll" << std::endl;
            std::wcout << L"3. Injetar DLL customizada" << std::endl;
            std::wcout << L"4. Listar processos FiveM" << std::endl;
            std::wcout << L"5. Sair" << std::endl;
            std::wcout << L"" << std::endl;
            std::wcout << L"Digite sua escolha (1-5): ";

            int choice;
            std::wcin >> choice;

            switch (choice) {
                case 1: {
                    std::wstring dllPath = L"FiveMOffsetDumper.dll";
                    if (InjectToFiveM(dllPath)) {
                        std::wcout << L"[INFO] Pressione INSERT no jogo para abrir a interface do dumper" << std::endl;
                    }
                    break;
                }
                case 2: {
                    std::wstring dllPath = L"examples\\FiveMSimpleCheat.dll";
                    if (InjectToFiveM(dllPath)) {
                        std::wcout << L"[INFO] Controles do cheat:" << std::endl;
                        std::wcout << L"  INSERT - Abrir/fechar menu" << std::endl;
                        std::wcout << L"  F1 - Toggle ESP" << std::endl;
                        std::wcout << L"  F2 - Toggle God Mode" << std::endl;
                    }
                    break;
                }
                case 3: {
                    std::wcout << L"Digite o caminho da DLL: ";
                    std::wstring customPath;
                    std::wcin.ignore();
                    std::getline(std::wcin, customPath);
                    InjectToFiveM(customPath);
                    break;
                }
                case 4: {
                    std::wcout << L"Procurando processos FiveM..." << std::endl;
                    FindFiveMProcesses();
                    break;
                }
                case 5: {
                    std::wcout << L"Saindo..." << std::endl;
                    return;
                }
                default: {
                    std::wcout << L"[ERROR] Opção inválida!" << std::endl;
                    break;
                }
            }

            std::wcout << L"" << std::endl;
            std::wcout << L"Pressione qualquer tecla para continuar..." << std::endl;
            std::wcin.ignore();
            std::wcin.get();
            std::wcout << L"" << std::endl;
        }
    }
};

int main() {
    // Configurar console para Unicode
    SetConsoleOutputCP(CP_UTF8);
    std::wcout.imbue(std::locale(""));

    // Verificar se está rodando como administrador
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
        DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }

    if (!isAdmin) {
        std::wcout << L"[WARNING] Não está rodando como administrador!" << std::endl;
        std::wcout << L"Algumas injeções podem falhar. Recomenda-se executar como admin." << std::endl;
        std::wcout << L"" << std::endl;
    }

    SimpleInjector injector;
    injector.Run();

    return 0;
}
