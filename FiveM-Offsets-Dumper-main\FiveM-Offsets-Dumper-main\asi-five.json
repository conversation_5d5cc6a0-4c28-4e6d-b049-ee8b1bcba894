{"asi-five.dll": {"CfxState::CfxState": {"offset": "0xF7B0"}, "Component::As": {"offset": "0x10740"}, "Component::IsA": {"offset": "0x128C0"}, "Component::SetCommandLine": {"offset": "0x9F30"}, "Component::SetUserData": {"offset": "0x12950"}, "ComponentInstance::DoGameLoad": {"offset": "0x107E0"}, "ComponentInstance::Initialize": {"offset": "0x128B0"}, "ComponentInstance::Shutdown": {"offset": "0x12950"}, "CoreGetComponentRegistry": {"offset": "0xB160"}, "CoreGetGlobalInstanceRegistry": {"offset": "0xB1F0"}, "CreateComponent": {"offset": "0x141E0"}, "DllMain": {"offset": "0x1DB88"}, "DoNtRaiseException": {"offset": "0x1ABC0"}, "FatalErrorNoExceptRealV": {"offset": "0xB600"}, "FatalErrorRealV": {"offset": "0xB630"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x1D60"}, "GetAbsoluteCitPath": {"offset": "0x19420"}, "GlobalErrorHandler": {"offset": "0xB870"}, "HookFunctionBase::RunAll": {"offset": "0x1B830"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0xF380"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x18C40"}, "InitFunctionBase::RunAll": {"offset": "0x1AD30"}, "MakeRelativeCitPath": {"offset": "0xBF10"}, "RaiseDebugException": {"offset": "0x1ACA0"}, "ScopedError::~ScopedError": {"offset": "0xA120"}, "SysError": {"offset": "0xC490"}, "ToNarrow": {"offset": "0x1AD60"}, "ToWide": {"offset": "0x1AE50"}, "TraceRealV": {"offset": "0x1B160"}, "Win32TrapAndJump64": {"offset": "0x1BE00"}, "_DllMainCRTStartup": {"offset": "0x1D53C"}, "_Init_thread_abort": {"offset": "0x1C8D0"}, "_Init_thread_footer": {"offset": "0x1C900"}, "_Init_thread_header": {"offset": "0x1C960"}, "_Init_thread_notify": {"offset": "0x1C9C8"}, "_Init_thread_wait": {"offset": "0x1CA0C"}, "_RTC_Initialize": {"offset": "0x1DBF4"}, "_RTC_Terminate": {"offset": "0x1DC30"}, "__ArrayUnwind": {"offset": "0x1D1B8"}, "__GSHandlerCheck": {"offset": "0x1CFEC"}, "__GSHandlerCheckCommon": {"offset": "0x1D00C"}, "__GSHandlerCheck_EH": {"offset": "0x1D068"}, "__GSHandlerCheck_SEH": {"offset": "0x1D6F8"}, "__crt_debugger_hook": {"offset": "0x1D92C"}, "__dyn_tls_init": {"offset": "0x1CE34"}, "__dyn_tls_on_demand_init": {"offset": "0x1CE9C"}, "__isa_available_init": {"offset": "0x1D780"}, "__local_stdio_printf_options": {"offset": "0xDEF0"}, "__local_stdio_scanf_options": {"offset": "0x1DBC8"}, "__raise_securityfailure": {"offset": "0x1D57C"}, "__report_gsfailure": {"offset": "0x1D5B0"}, "__scrt_acquire_startup_lock": {"offset": "0x1CAB4"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x1CAF0"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x1CB24"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x1CB3C"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x1CB64"}, "__scrt_dllmain_exception_filter": {"offset": "0x1CB7C"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x1CBDC"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x1CC0C"}, "__scrt_fastfail": {"offset": "0x1D934"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x1DBEC"}, "__scrt_initialize_crt": {"offset": "0x1CC20"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x1DBD0"}, "__scrt_initialize_onexit_tables": {"offset": "0x1CC6C"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x1C7D8"}, "__scrt_initialize_type_info": {"offset": "0x1DBAC"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x1CCF8"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x1E8FC"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x1DAD0"}, "__scrt_release_startup_lock": {"offset": "0x1CD90"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x12950"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x12950"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x12950"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x12950"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x12950"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0x10740"}, "__scrt_throw_std_bad_alloc": {"offset": "0x1DAA0"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xCC90"}, "__scrt_uninitialize_crt": {"offset": "0x1CDB4"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x1C8A8"}, "__scrt_uninitialize_type_info": {"offset": "0x1DBBC"}, "__security_check_cookie": {"offset": "0x1D100"}, "__security_init_cookie": {"offset": "0x1DADC"}, "__std_find_trivial_1": {"offset": "0x1BE20"}, "__std_find_trivial_2": {"offset": "0x1BEF0"}, "__std_fs_code_page": {"offset": "0x1C074"}, "__std_fs_convert_narrow_to_wide": {"offset": "0x1C09C"}, "__std_fs_convert_wide_to_narrow": {"offset": "0x1C0E4"}, "__std_fs_convert_wide_to_narrow_replace_chars": {"offset": "0x1C208"}, "__std_fs_get_stats": {"offset": "0x1C400"}, "__std_fs_open_handle": {"offset": "0x1C728"}, "__std_system_error_allocate_message": {"offset": "0x1BFCC"}, "__std_system_error_deallocate_message": {"offset": "0x1C060"}, "_get_startup_argv_mode": {"offset": "0x1DAC8"}, "_guard_check_icall_nop": {"offset": "0x9F30"}, "_guard_dispatch_icall_nop": {"offset": "0x1DD70"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x1DD90"}, "_onexit": {"offset": "0x1CDE0"}, "_wwassert": {"offset": "0x19720"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x1E940"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x1E99F"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x1E9B6"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x1E9CF"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x1E9E3"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_10''": {"offset": "0x1210"}, "`dynamic initializer for '_init_instance_11''": {"offset": "0x1240"}, "`dynamic initializer for '_init_instance_9''": {"offset": "0x1270"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1420"}, "atexit": {"offset": "0x1CE1C"}, "capture_previous_context": {"offset": "0x1D684"}, "console::Printf<char const *,int,int>": {"offset": "0xE090"}, "dllmain_crt_dispatch": {"offset": "0x1D21C"}, "dllmain_crt_process_attach": {"offset": "0x1D26C"}, "dllmain_crt_process_detach": {"offset": "0x1D384"}, "dllmain_dispatch": {"offset": "0x1D408"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD2D0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0x9FF0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x18460"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x17AA0"}, "fmt::v8::detail::add_compare": {"offset": "0x17C70"}, "fmt::v8::detail::assert_fail": {"offset": "0x17DB0"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x17E00"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x17FD0"}, "fmt::v8::detail::bigint::square": {"offset": "0x18830"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x17AA0"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2800"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x18AF0"}, "fmt::v8::detail::compare": {"offset": "0x17F30"}, "fmt::v8::detail::count_digits": {"offset": "0xD0B0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x145F0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x28B0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x18330"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x164C0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x18610"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x164F0"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x171B0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x16D80"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x18590"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x14700"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x14700"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x182C0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x28E0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x15BB0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x2BB0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x15A90"}, "fmt::v8::detail::format_float<double>": {"offset": "0x14210"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x15CF0"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x2C90"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x16050"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x2DB0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x2F10"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x3170"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xDE40"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x16700"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x16980"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x16C10"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA050"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3240"}, "fmt::v8::detail::utf8_decode": {"offset": "0xDC80"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4840"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5840"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x53F0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x5C80"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x17810"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x176F0"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5320"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x60C0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6100"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6290"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x6C50"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6660"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x9BE0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7380"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x77A0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7970"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7B10"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7B10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x7CA0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x7EC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x8040"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x97D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x81D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x83F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8690"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x88B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8A30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x8C50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x8E70"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x8FF0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9210"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9390"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x95B0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9960"}, "fmt::v8::format_error::format_error": {"offset": "0x9DE0"}, "fmt::v8::format_error::~format_error": {"offset": "0xA180"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x1A010"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x36B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3490"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3360"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3270"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x36B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3580"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x37E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4340"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3D70"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0xF260"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x1A8B0"}, "fprintf": {"offset": "0xDF00"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA1A0"}, "fwRefCountable::AddRef": {"offset": "0x1B7F0"}, "fwRefCountable::Release": {"offset": "0x1B800"}, "fwRefCountable::~fwRefCountable": {"offset": "0x1B7E0"}, "fx::client::GetPureLevel": {"offset": "0x12680"}, "launch::IsSDKGuest": {"offset": "0x128D0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x9E60"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x9F00"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1770"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xAFD0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x9F30"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC120"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC1E0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC450"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xC8F0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0x9F40"}, "rapidjson::internal::DigitGen": {"offset": "0xB280"}, "rapidjson::internal::Grisu2": {"offset": "0xBD30"}, "rapidjson::internal::Prettify": {"offset": "0xC290"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2200"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x22D0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0x9F00"}, "rapidjson::internal::WriteExponent": {"offset": "0xC860"}, "rapidjson::internal::u32toa": {"offset": "0xD3C0"}, "rapidjson::internal::u64toa": {"offset": "0xD630"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >,void *> > >": {"offset": "0xFF60"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x9F70"}, "std::_Allocate<16,std::_Default_allocate_traits,0>": {"offset": "0xE2F0"}, "std::_Facet_Register": {"offset": "0x1C788"}, "std::_Func_class<void,void *>::~_Func_class<void,void *>": {"offset": "0xFF80"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Copy": {"offset": "0x12A40"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Delete_this": {"offset": "0x12A50"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Do_call": {"offset": "0x12AA0"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Get": {"offset": "0x12C60"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Move": {"offset": "0x12A40"}, "std::_Func_impl_no_alloc<<lambda_4c5ef0e61b89288335ec4a95a5f91bb5>,void,void *>::_Target_type": {"offset": "0x133C0"}, "std::_Generic_error_category::message": {"offset": "0x13D20"}, "std::_Generic_error_category::name": {"offset": "0x13E20"}, "std::_Make_ec": {"offset": "0x12D80"}, "std::_Maklocstr<char>": {"offset": "0xDF50"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0x10740"}, "std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl>::_Delete_this": {"offset": "0x12A60"}, "std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl>::_Destroy": {"offset": "0x12A80"}, "std::_String_val<std::_Simple_types<wchar_t> >::_Xran": {"offset": "0x13650"}, "std::_System_error::_System_error": {"offset": "0xFB70"}, "std::_System_error_category::default_error_condition": {"offset": "0x13AD0"}, "std::_System_error_category::message": {"offset": "0x13D70"}, "std::_System_error_category::name": {"offset": "0x13E30"}, "std::_System_error_message::~_System_error_message": {"offset": "0x10170"}, "std::_Throw_bad_array_new_length": {"offset": "0xCC90"}, "std::_Throw_system_error": {"offset": "0x13520"}, "std::_Throw_system_error_from_std_win_error": {"offset": "0x13560"}, "std::_Throw_tree_length_error": {"offset": "0xCCB0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x17A60"}, "std::_Tree<std::_Tmap_traits<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::function<void __cdecl(void *)>,std::less<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >,std::allocator<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > >,0> >::_Find_hint<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >": {"offset": "0xE5B0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::function<void __cdecl(void *)>,std::less<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >,std::allocator<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > >,0> >::~_Tree<std::_Tmap_traits<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::function<void __cdecl(void *)>,std::less<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >,std::allocator<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > >,0> >": {"offset": "0xFFC0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x24A0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2720"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0x9F90"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >,void *> > >": {"offset": "0xFF60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >,void *> > >": {"offset": "0xE510"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > > >::_Insert_node": {"offset": "0xCA30"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2440"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCA30"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >": {"offset": "0xFFF0"}, "std::_Xlen_string": {"offset": "0xCCD0"}, "std::allocator<char>::allocate": {"offset": "0xCCF0"}, "std::allocator<char>::deallocate": {"offset": "0x13A50"}, "std::allocator<unsigned char>::allocate": {"offset": "0xCCF0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x13A50"}, "std::allocator<wchar_t>::allocate": {"offset": "0xCD50"}, "std::allocator<wchar_t>::deallocate": {"offset": "0x13A90"}, "std::bad_alloc::bad_alloc": {"offset": "0x1DA80"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA180"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x9D70"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA180"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2380"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0xE9E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0xEB50"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0xECE0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>": {"offset": "0xEF80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x13670"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xCF40"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9B10"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::resize": {"offset": "0x13FA0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA050"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0xE350"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>": {"offset": "0xEE30"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA0B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xCDC0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign": {"offset": "0x13700"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xF6E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x13E40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x1B600"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::resize": {"offset": "0x14040"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA0B0"}, "std::error_category::default_error_condition": {"offset": "0x13B50"}, "std::error_category::equivalent": {"offset": "0x13B80"}, "std::exception::exception": {"offset": "0x9DA0"}, "std::exception::what": {"offset": "0xDE20"}, "std::filesystem::_Convert_wide_to_narrow_replace_chars<std::char_traits<char>,std::allocator<char> >": {"offset": "0xE420"}, "std::filesystem::_Dir_enum_impl::_Advance_and_reset_if_no_more_files": {"offset": "0x12960"}, "std::filesystem::_Dir_enum_impl::_Creator::~_Creator": {"offset": "0x10130"}, "std::filesystem::_Dir_enum_impl::_Dir_enum_impl": {"offset": "0xF870"}, "std::filesystem::_Dir_enum_impl::_Initialize_dir_enum<std::filesystem::_Dir_enum_impl>": {"offset": "0xE880"}, "std::filesystem::_Dir_enum_impl::_Open_dir": {"offset": "0x12DA0"}, "std::filesystem::_Dir_enum_impl::_Refresh": {"offset": "0x131C0"}, "std::filesystem::_Dir_enum_impl::_Skip_dots": {"offset": "0x13370"}, "std::filesystem::_Find_file_handle::~_Find_file_handle": {"offset": "0x10160"}, "std::filesystem::_Find_filename": {"offset": "0x12B10"}, "std::filesystem::_Find_root_name_end": {"offset": "0x12B70"}, "std::filesystem::_Get_any_status": {"offset": "0x12C70"}, "std::filesystem::_Is_dot_or_dotdot": {"offset": "0x12D50"}, "std::filesystem::_Range_compare": {"offset": "0x13160"}, "std::filesystem::_Throw_fs_error": {"offset": "0x134A0"}, "std::filesystem::filesystem_error::_Pretty_message": {"offset": "0x12E70"}, "std::filesystem::filesystem_error::filesystem_error": {"offset": "0xFD90"}, "std::filesystem::filesystem_error::what": {"offset": "0x141D0"}, "std::filesystem::filesystem_error::~filesystem_error": {"offset": "0x10180"}, "std::filesystem::path::compare": {"offset": "0x13830"}, "std::filesystem::path::filename": {"offset": "0x13BC0"}, "std::filesystem::path::is_absolute": {"offset": "0x13C80"}, "std::filesystem::path::string": {"offset": "0x140D0"}, "std::filesystem::path::~path": {"offset": "0xA1A0"}, "std::locale::~locale": {"offset": "0x17B20"}, "std::make_error_code": {"offset": "0x13D00"}, "std::map<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::function<void __cdecl(void *)>,std::less<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >,std::allocator<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > > >::~map<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::function<void __cdecl(void *)>,std::less<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > >,std::allocator<std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> > > >": {"offset": "0xFFC0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x181E0"}, "std::numpunct<char>::do_falsename": {"offset": "0x181F0"}, "std::numpunct<char>::do_grouping": {"offset": "0x18230"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x18270"}, "std::numpunct<char>::do_truename": {"offset": "0x18280"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x178B0"}, "std::pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >::~pair<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > const ,std::function<void __cdecl(void *)> >": {"offset": "0x10030"}, "std::runtime_error::runtime_error": {"offset": "0x9E20"}, "std::shared_ptr<std::filesystem::_Dir_enum_impl>::~shared_ptr<std::filesystem::_Dir_enum_impl>": {"offset": "0x10070"}, "std::system_error::system_error": {"offset": "0xFEA0"}, "std::system_error::~system_error": {"offset": "0xA180"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x17B00"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x17580"}, "utf8::exception::exception": {"offset": "0x1A9E0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x19A80"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x1A3B0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x1AA70"}, "utf8::invalid_code_point::what": {"offset": "0x1B7B0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA180"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x1AAE0"}, "utf8::invalid_utf8::what": {"offset": "0x1B7C0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA180"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x1AB40"}, "utf8::not_enough_room::what": {"offset": "0x1B7D0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA180"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x1A190"}, "vva": {"offset": "0x1B790"}, "xbr::GetGameBuild": {"offset": "0x125A0"}, "xbr::GetReplaceExecutableInit": {"offset": "0x1B860"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x1BA80"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1470"}}}