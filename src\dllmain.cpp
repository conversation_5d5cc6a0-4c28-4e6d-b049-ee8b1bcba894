#include <Windows.h>
#include <thread>
#include <iostream>
#include "OffsetDumper.h"
#include "GUI.h"

// Global instances
OffsetDumper* g_dumper = nullptr;
GUI* g_gui = nullptr;
std::thread* g_mainThread = nullptr;

void MainThread(HMODULE hModule) {
    // Allocate console for debugging
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    std::cout << "[FiveM Offset Dumper] Initializing..." << std::endl;
    
    try {
        // Initialize offset dumper
        g_dumper = new OffsetDumper();
        if (!g_dumper->Initialize()) {
            std::cerr << "[ERROR] Failed to initialize offset dumper" << std::endl;
            return;
        }

        // Initialize GUI
        g_gui = new GUI();
        if (!g_gui->Initialize()) {
            std::cerr << "[ERROR] Failed to initialize GUI" << std::endl;
            return;
        }
        
        std::cout << "[FiveM Offset Dumper] Successfully initialized!" << std::endl;
        std::cout << "[FiveM Offset Dumper] Press INSERT to toggle GUI" << std::endl;
        
        // Main loop
        while (true) {
            if (GetAsyncKeyState(VK_INSERT) & 1) {
                g_gui->ToggleVisibility();
            }
            
            if (GetAsyncKeyState(VK_END) & 1) {
                break;
            }
            
            g_gui->Render();
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception: " << e.what() << std::endl;
    }
    
    // Cleanup
    if (g_gui) {
        g_gui->Shutdown();
        delete g_gui;
        g_gui = nullptr;
    }
    
    if (g_dumper) {
        delete g_dumper;
        g_dumper = nullptr;
    }
    
    std::cout << "[FiveM Offset Dumper] Shutting down..." << std::endl;
    FreeConsole();
    FreeLibraryAndExitThread(hModule, 0);
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        g_mainThread = new std::thread(MainThread, hModule);
        break;
    case DLL_PROCESS_DETACH:
        if (g_mainThread && g_mainThread->joinable()) {
            g_mainThread->join();
            delete g_mainThread;
        }
        break;
    }
    return TRUE;
}
