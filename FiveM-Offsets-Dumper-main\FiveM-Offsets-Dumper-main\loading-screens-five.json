{"loading-screens-five.dll": {"<lambda_5d390c504404984603a01667d3d4b147>::~<lambda_5d390c504404984603a01667d3d4b147>": {"offset": "0x2DA80"}, "<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>::~<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>": {"offset": "0x2DA80"}, "<lambda_6085d0141ab590130fe09f26899e7cc9>::~<lambda_6085d0141ab590130fe09f26899e7cc9>": {"offset": "0x2DA80"}, "<lambda_6850f9b66df829470809110098aea58f>::~<lambda_6850f9b66df829470809110098aea58f>": {"offset": "0x2DA80"}, "<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x16F70"}, "<lambda_7a33ece0fd67ec639c89b5667afebcea>::~<lambda_7a33ece0fd67ec639c89b5667afebcea>": {"offset": "0x2DA80"}, "<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>::~<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>": {"offset": "0x2DA80"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x16F70"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xA920"}, "CDataFileMgr::FindNextEntry": {"offset": "0x1EAF0"}, "CDataFileMgr::FindPreviousEntry": {"offset": "0x1EBB0"}, "CfxState::CfxState": {"offset": "0x3BDE0"}, "Component::As": {"offset": "0xE8E0"}, "Component::IsA": {"offset": "0xE9A0"}, "Component::SetCommandLine": {"offset": "0xA800"}, "Component::SetUserData": {"offset": "0xE9B0"}, "ComponentInstance::DoGameLoad": {"offset": "0xE980"}, "ComponentInstance::Initialize": {"offset": "0xE990"}, "ComponentInstance::Shutdown": {"offset": "0xE9B0"}, "ConVar<bool>::ConVar<bool>": {"offset": "0x2D760"}, "ConVar<bool>::~ConVar<bool>": {"offset": "0x2DAB0"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x221F0"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0xF780"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0xF960"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x17580"}, "ConsoleFlagsToString": {"offset": "0x1D470"}, "CoreGetComponentRegistry": {"offset": "0x1D790"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x1D820"}, "CountRelevantDataFileEntries": {"offset": "0x1D8B0"}, "CreateComponent": {"offset": "0xE9C0"}, "CreateVariableEntry<bool>": {"offset": "0x100A0"}, "DestroyFrame": {"offset": "0x34F90"}, "DllMain": {"offset": "0x4350C"}, "DoNtRaiseException": {"offset": "0x3E370"}, "FatalErrorNoExceptRealV": {"offset": "0xBED0"}, "FatalErrorRealV": {"offset": "0xBF00"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x2630"}, "GetAbsoluteCitPath": {"offset": "0x3C7A0"}, "GlobalErrorHandler": {"offset": "0xC140"}, "HookFunction::Run": {"offset": "0xED10"}, "HookFunctionBase::Register": {"offset": "0x3F1D0"}, "HookFunctionBase::RunAll": {"offset": "0x3F1F0"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x3B940"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x3BEA0"}, "InitFunction::Run": {"offset": "0xE9F0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x3E170"}, "InitFunctionBase::Register": {"offset": "0x3E4E0"}, "InitFunctionBase::RunAll": {"offset": "0x3E530"}, "Instance<ICoreGameInit>::Get": {"offset": "0x35340"}, "InstrumentFunction<LoadScreenFuncs>": {"offset": "0x11C40"}, "InstrumentedCallStub<LoadScreenFuncs>::InstrumentedTarget": {"offset": "0x1F620"}, "InstrumentedCallStub<LoadScreenFuncs>::InternalMain": {"offset": "0xF0D0"}, "InvokeNUIScript": {"offset": "0x353A0"}, "LoadDefDats": {"offset": "0x21C70"}, "LoadsThread::DoRun": {"offset": "0x28450"}, "LoadsThread::Reset": {"offset": "0x29370"}, "MakeRelativeCitPath": {"offset": "0xC7E0"}, "NativeInvoke::Invoke<-2879672645410256730,int>": {"offset": "0x2A270"}, "NativeInvoke::Invoke<-4939229729199161819,int,float,float,float,float,float,float>": {"offset": "0x2A3A0"}, "NativeInvoke::Invoke<3582399230505917858,int>": {"offset": "0x2A600"}, "NativeInvoke::Invoke<469568048723526251,int,int,float,float,float,bool,bool,bool,bool>": {"offset": "0x2A730"}, "NativeInvoke::Invoke<569060033405794044,int,bool,bool,int,bool,bool>": {"offset": "0x2A9E0"}, "RaiseDebugException": {"offset": "0x3E450"}, "ScopedError::~ScopedError": {"offset": "0xA9F0"}, "ShouldSkipLoading": {"offset": "0x35CF0"}, "SysError": {"offset": "0xCD60"}, "ToNarrow": {"offset": "0x3E560"}, "ToWide": {"offset": "0x3E650"}, "TraceRealV": {"offset": "0x3E960"}, "UpdateLoadTiming": {"offset": "0x35E90"}, "Win32TrapAndJump64": {"offset": "0x40060"}, "_DllMainCRTStartup": {"offset": "0x42EC0"}, "_Init_thread_abort": {"offset": "0x42160"}, "_Init_thread_footer": {"offset": "0x42190"}, "_Init_thread_header": {"offset": "0x421F0"}, "_Init_thread_notify": {"offset": "0x42258"}, "_Init_thread_wait": {"offset": "0x4229C"}, "_RTC_Initialize": {"offset": "0x4355C"}, "_RTC_Terminate": {"offset": "0x43598"}, "_Smtx_lock_shared": {"offset": "0x42008"}, "_Smtx_unlock_shared": {"offset": "0x42010"}, "__ArrayUnwind": {"offset": "0x42AD4"}, "__GSHandlerCheck": {"offset": "0x4287C"}, "__GSHandlerCheckCommon": {"offset": "0x4289C"}, "__GSHandlerCheck_EH": {"offset": "0x428F8"}, "__GSHandlerCheck_SEH": {"offset": "0x4307C"}, "__chkstk": {"offset": "0x42B50"}, "__crt_debugger_hook": {"offset": "0x432B0"}, "__dyn_tls_init": {"offset": "0x426C4"}, "__dyn_tls_on_demand_init": {"offset": "0x4272C"}, "__isa_available_init": {"offset": "0x43104"}, "__local_stdio_printf_options": {"offset": "0xE7C0"}, "__local_stdio_scanf_options": {"offset": "0x43530"}, "__raise_securityfailure": {"offset": "0x42F00"}, "__report_gsfailure": {"offset": "0x42F34"}, "__scrt_acquire_startup_lock": {"offset": "0x42344"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x42380"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x423B4"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x423CC"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x423F4"}, "__scrt_dllmain_exception_filter": {"offset": "0x4240C"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x4246C"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x4249C"}, "__scrt_fastfail": {"offset": "0x432B8"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x43554"}, "__scrt_initialize_crt": {"offset": "0x424B0"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x43538"}, "__scrt_initialize_onexit_tables": {"offset": "0x424FC"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x42068"}, "__scrt_initialize_type_info": {"offset": "0x429DC"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x42588"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x452CC"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x43454"}, "__scrt_release_startup_lock": {"offset": "0x42620"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE9B0"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE9B0"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE9B0"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE9B0"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE9B0"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE8E0"}, "__scrt_throw_std_bad_alloc": {"offset": "0x43424"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD560"}, "__scrt_uninitialize_crt": {"offset": "0x42644"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x42138"}, "__scrt_uninitialize_type_info": {"offset": "0x429EC"}, "__security_check_cookie": {"offset": "0x42990"}, "__security_init_cookie": {"offset": "0x43460"}, "__std_find_trivial_1": {"offset": "0x41E40"}, "__std_find_trivial_8": {"offset": "0x41F10"}, "_get_startup_argv_mode": {"offset": "0x4344C"}, "_guard_check_icall_nop": {"offset": "0xA800"}, "_guard_dispatch_icall_nop": {"offset": "0x436E0"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x43700"}, "_onexit": {"offset": "0x42670"}, "_wwassert": {"offset": "0x3CAA0"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x4533C"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x4539B"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x453B2"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x453CB"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x453DF"}, "`dynamic initializer for 'OnInstrumentedFunctionCall''": {"offset": "0x1290"}, "`dynamic initializer for 'OnReturnDataFileEntry''": {"offset": "0x12A0"}, "`dynamic initializer for '_drawLoadingSpinner''": {"offset": "0x12B0"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1700"}, "`dynamic initializer for '_init_instance_15''": {"offset": "0x1730"}, "`dynamic initializer for '_init_instance_39''": {"offset": "0x1760"}, "`dynamic initializer for '_init_instance_40''": {"offset": "0x1790"}, "`dynamic initializer for '_init_instance_41''": {"offset": "0x17C0"}, "`dynamic initializer for '_init_instance_42''": {"offset": "0x12F0"}, "`dynamic initializer for '_init_instance_43''": {"offset": "0x1320"}, "`dynamic initializer for '_init_instance_44''": {"offset": "0x1350"}, "`dynamic initializer for '_sf_updateEndOfFrame''": {"offset": "0x1380"}, "`dynamic initializer for 'g_dlcNameMap''": {"offset": "0x13C0"}, "`dynamic initializer for 'g_lastInstrumentedFuncs''": {"offset": "0x1400"}, "`dynamic initializer for 'g_loadTiming''": {"offset": "0x1850"}, "`dynamic initializer for 'g_origShutdown''": {"offset": "0x1890"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'g_visitedTimings''": {"offset": "0x18A0"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1440"}, "`dynamic initializer for 'hookFunctionSpinner''": {"offset": "0x1480"}, "`dynamic initializer for 'initFunction''": {"offset": "0x14C0"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x1B70"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1B20"}, "`dynamic initializer for 'loadsThread''": {"offset": "0x1AE0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x1210"}, "`dynamic initializer for 'xbr::virt::Base<15,2802,0,6>::ms_initFunction''": {"offset": "0x1250"}, "`dynamic initializer for 'xbr::virt::Base<2,2802,2,6>::ms_initFunction''": {"offset": "0x1680"}, "`dynamic initializer for 'xbr::virt::Base<45,2802,0,6>::ms_initFunction''": {"offset": "0x16C0"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::RegIDMap": {"offset": "0x16F20"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::~RegIDMap": {"offset": "0x17AF0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>,<lambda_5d390c504404984603a01667d3d4b147> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>,<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>,<lambda_6085d0141ab590130fe09f26899e7cc9> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>,<lambda_6850f9b66df829470809110098aea58f> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>,<lambda_7a33ece0fd67ec639c89b5667afebcea> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>,<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x2DD50"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17B80"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17BA0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17BA0"}, "atexit": {"offset": "0x426AC"}, "capture_previous_context": {"offset": "0x43008"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x11F30"}, "console::Printfv": {"offset": "0x22930"}, "decode_ext": {"offset": "0x40280"}, "decode_imm": {"offset": "0x40550"}, "decode_insn": {"offset": "0x40600"}, "decode_mem_disp": {"offset": "0x40800"}, "decode_modrm_reg": {"offset": "0x408B0"}, "decode_modrm_rm": {"offset": "0x40950"}, "decode_opcode": {"offset": "0x40CE0"}, "decode_operand": {"offset": "0x40D10"}, "decode_reg": {"offset": "0x41400"}, "decode_vex": {"offset": "0x41530"}, "dllmain_crt_dispatch": {"offset": "0x42BA0"}, "dllmain_crt_process_attach": {"offset": "0x42BF0"}, "dllmain_crt_process_detach": {"offset": "0x42D08"}, "dllmain_dispatch": {"offset": "0x42D8C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xDBA0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA8C0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x3B160"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x3A7A0"}, "fmt::v8::detail::add_compare": {"offset": "0x3A970"}, "fmt::v8::detail::assert_fail": {"offset": "0x3AAB0"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x3AB00"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x3ACD0"}, "fmt::v8::detail::bigint::square": {"offset": "0x3B530"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x3A7A0"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x30D0"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x3B7F0"}, "fmt::v8::detail::compare": {"offset": "0x3AC30"}, "fmt::v8::detail::count_digits": {"offset": "0xD980"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x372F0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x3180"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x3B030"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x391C0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x3B310"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x391F0"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x39EB0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x39A80"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x3B290"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x37400"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x37400"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x3AFC0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x31B0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x388B0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3480"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x38790"}, "fmt::v8::detail::format_float<double>": {"offset": "0x36E40"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x389F0"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x3560"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x38D50"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3680"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x37E0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x3A40"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE710"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x39400"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x39680"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x39910"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA920"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3B10"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE550"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5110"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x6110"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5CC0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x6550"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x3A510"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x3A3F0"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5BF0"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6990"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x69D0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6B60"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x7520"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6F30"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA4B0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7C50"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x8070"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x8240"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x83E0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x83E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8570"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x8790"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x8910"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0xA0A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8AA0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8CC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8F60"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x9180"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x9300"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x9520"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x9740"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x98C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9AE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9C60"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9E80"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xA230"}, "fmt::v8::format_error::format_error": {"offset": "0xA6B0"}, "fmt::v8::format_error::~format_error": {"offset": "0xAA50"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x3D7A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3F80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3D60"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3F80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3E50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x40B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4640"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x2D640"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x3E040"}, "fprintf": {"offset": "0xE7D0"}, "fwEvent<>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<enum HostState,enum HostState>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<enum rage::InitFunctionType,int,int>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<enum rage::InitFunctionType,int,rage::InitFunctionData &>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<enum rage::InitFunctionType,rage::InitFunctionData const &>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<enum rage::InitFunctionType>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<int,char const *>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<int,void *,void *>::ConnectInternal": {"offset": "0x1D2A0"}, "fwEvent<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::ConnectInternal": {"offset": "0x1D2A0"}, "fwPlatformString::fwPlatformString": {"offset": "0x2D990"}, "fwPlatformString::~fwPlatformString": {"offset": "0xAA70"}, "fwRefContainer<fx::Resource>::~fwRefContainer<fx::Resource>": {"offset": "0x2DBE0"}, "fwRefContainer<fx::ResourceMetaDataComponent>::~fwRefContainer<fx::ResourceMetaDataComponent>": {"offset": "0x2DBE0"}, "fwRefCountable::AddRef": {"offset": "0x3F190"}, "fwRefCountable::Release": {"offset": "0x3F1A0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x3F180"}, "hook::AllocateFunctionStub": {"offset": "0x3F250"}, "hook::TransformPattern": {"offset": "0x3F910"}, "hook::details::StubInitFunction::Run": {"offset": "0xED20"}, "hook::get_pattern<char,17>": {"offset": "0x15F40"}, "hook::get_pattern<char,26>": {"offset": "0x15F40"}, "hook::get_pattern<char,34>": {"offset": "0x15F40"}, "hook::get_pattern<char,37>": {"offset": "0x15F40"}, "hook::get_pattern<char,46>": {"offset": "0x15F40"}, "hook::get_pattern<char,51>": {"offset": "0x15F40"}, "hook::get_pattern<void,18>": {"offset": "0x15F40"}, "hook::get_pattern<void,23>": {"offset": "0x15F40"}, "hook::get_pattern<void,25>": {"offset": "0x15F40"}, "hook::get_pattern<void,29>": {"offset": "0x15F40"}, "hook::get_pattern<void,32>": {"offset": "0x15F40"}, "hook::get_pattern<void,36>": {"offset": "0x15F40"}, "hook::get_pattern<void,41>": {"offset": "0x15F40"}, "hook::get_pattern<void,42>": {"offset": "0x15F40"}, "hook::get_pattern<void,45>": {"offset": "0x15F40"}, "hook::get_pattern<void,51>": {"offset": "0x15F40"}, "hook::pattern::EnsureMatches": {"offset": "0x3F2B0"}, "hook::pattern::Initialize": {"offset": "0x3F610"}, "hook::pattern::~pattern": {"offset": "0x17C00"}, "inp_file_hook": {"offset": "0x40080"}, "inp_next": {"offset": "0x416D0"}, "inp_uint32": {"offset": "0x41750"}, "inp_uint64": {"offset": "0x417A0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x1C590"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0xFE30"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x1C7C0"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x16530"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0xEC80"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0xEB00"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0xECE0"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x24A70"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0xEB80"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0xECF0"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x1CA20"}, "jitasm::Backend::Assemble": {"offset": "0x1A230"}, "jitasm::Backend::Encode": {"offset": "0x1DC00"}, "jitasm::Backend::EncodeALU": {"offset": "0x1DFA0"}, "jitasm::Backend::EncodeImm": {"offset": "0x1E090"}, "jitasm::Backend::EncodeJMP": {"offset": "0x1E210"}, "jitasm::Backend::EncodeModRM": {"offset": "0x1E430"}, "jitasm::Backend::EncodeOpcode": {"offset": "0x1E6B0"}, "jitasm::Backend::EncodePrefixes": {"offset": "0x1E740"}, "jitasm::Backend::GetWRXB": {"offset": "0x1F590"}, "jitasm::Backend::dd": {"offset": "0x26BA0"}, "jitasm::Frontend::AppendInstr": {"offset": "0x1A100"}, "jitasm::Frontend::Assemble": {"offset": "0x1A530"}, "jitasm::Frontend::Label::~Label": {"offset": "0xA920"}, "jitasm::Frontend::ResolveJump": {"offset": "0x22BA0"}, "jitasm::Frontend::add": {"offset": "0x26670"}, "jitasm::Frontend::mov": {"offset": "0x27240"}, "jitasm::Frontend::movaps": {"offset": "0x27420"}, "jitasm::Frontend::pop": {"offset": "0x275D0"}, "jitasm::Frontend::push": {"offset": "0x276D0"}, "jitasm::Frontend::pxor": {"offset": "0x27950"}, "jitasm::Frontend::sub": {"offset": "0x27D20"}, "jitasm::Frontend::vxorps": {"offset": "0x27FD0"}, "jitasm::Frontend::xorps": {"offset": "0x280D0"}, "jitasm::Frontend::~Frontend": {"offset": "0x17780"}, "jitasm::Instr::Instr": {"offset": "0x16A50"}, "jitasm::compiler::BasicBlock::BasicBlock": {"offset": "0x169C0"}, "jitasm::compiler::BasicBlock::GetLifetime": {"offset": "0x1F520"}, "jitasm::compiler::BasicBlock::IsDominated": {"offset": "0x1F8B0"}, "jitasm::compiler::Compile": {"offset": "0x1CEC0"}, "jitasm::compiler::ControlFlowGraph::Build": {"offset": "0x1AD70"}, "jitasm::compiler::ControlFlowGraph::DetectLoops": {"offset": "0x1D8C0"}, "jitasm::compiler::ControlFlowGraph::MakeDepthFirstBlocks": {"offset": "0x22100"}, "jitasm::compiler::ControlFlowGraph::clear": {"offset": "0x26A70"}, "jitasm::compiler::ControlFlowGraph::get_block": {"offset": "0x26E40"}, "jitasm::compiler::ControlFlowGraph::initialize": {"offset": "0x26F40"}, "jitasm::compiler::ControlFlowGraph::~ControlFlowGraph": {"offset": "0x17610"}, "jitasm::compiler::DominatorFinder::Compress": {"offset": "0x1D230"}, "jitasm::compiler::DominatorFinder::~DominatorFinder": {"offset": "0x17680"}, "jitasm::compiler::GenerateEpilog": {"offset": "0x1EC70"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::GpRegOperator>": {"offset": "0x10AB0"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::MmxRegOperator>": {"offset": "0x11070"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::XmmRegOperator>": {"offset": "0x116F0"}, "jitasm::compiler::GenerateProlog": {"offset": "0x1F060"}, "jitasm::compiler::Lifetime::AddUsePoint": {"offset": "0x19C20"}, "jitasm::compiler::Lifetime::AssignRegister": {"offset": "0x1A8E0"}, "jitasm::compiler::Lifetime::BuildIntervals": {"offset": "0x1B6C0"}, "jitasm::compiler::Lifetime::Interval::Interval": {"offset": "0x16B90"}, "jitasm::compiler::Lifetime::Interval::~Interval": {"offset": "0x17800"}, "jitasm::compiler::Lifetime::LessAssignOrder::num_of_assignable": {"offset": "0x27570"}, "jitasm::compiler::Lifetime::Lifetime": {"offset": "0x16C60"}, "jitasm::compiler::Lifetime::SpillIdentification": {"offset": "0x24E00"}, "jitasm::compiler::Lifetime::~Lifetime": {"offset": "0x17990"}, "jitasm::compiler::LinearScanRegisterAlloc": {"offset": "0x1F8D0"}, "jitasm::compiler::LiveVariableAnalysis": {"offset": "0x1FD30"}, "jitasm::compiler::Operations::Operations": {"offset": "0x16E30"}, "jitasm::compiler::PrepareCompile": {"offset": "0x222E0"}, "jitasm::compiler::RewriteInstructions": {"offset": "0x237F0"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::GpRegOperator> >": {"offset": "0x10380"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::MmxRegOperator> >": {"offset": "0x10630"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::XmmRegOperator> >": {"offset": "0x10970"}, "jitasm::compiler::VariableManager::AllocSpillSlots": {"offset": "0x19E30"}, "jitasm::compiler::VariableManager::UpdateVarSize": {"offset": "0x25760"}, "jitasm::compiler::VariableManager::~VariableManager": {"offset": "0x17B50"}, "jitasm::detail::CodeBuffer::Reset": {"offset": "0x22AF0"}, "jitasm::detail::CodeBuffer::~CodeBuffer": {"offset": "0x17540"}, "jitasm::detail::ScopedLock<jitasm::detail::SpinLock>::~ScopedLock<jitasm::detail::SpinLock>": {"offset": "0x16FA0"}, "launch::IsSDKGuest": {"offset": "0x35B10"}, "modrm": {"offset": "0x41840"}, "rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>::Peek": {"offset": "0x35B90"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x2DD10"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x35070"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x35180"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x2D8F0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x2BD50"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA730"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x2DD40"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::NumberStream<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,0,0>::Peek": {"offset": "0x35BB0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x2AC00"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x2AE50"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x2AF20"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x2B920"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x2BF60"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x2C360"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x35BD0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA7D0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA7D0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x2040"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0x34C90"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::SetStringRaw": {"offset": "0x35C60"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA800"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC9F0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xCAB0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xCD20"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xD1C0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA810"}, "rapidjson::internal::DigitGen": {"offset": "0xBB50"}, "rapidjson::internal::FastPath": {"offset": "0x35290"}, "rapidjson::internal::Grisu2": {"offset": "0xC600"}, "rapidjson::internal::Prettify": {"offset": "0xCB60"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x2C840"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2AD0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2BA0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA7D0"}, "rapidjson::internal::WriteExponent": {"offset": "0xD130"}, "rapidjson::internal::u32toa": {"offset": "0xDC90"}, "rapidjson::internal::u64toa": {"offset": "0xDF00"}, "resolve_mode": {"offset": "0x41890"}, "resolve_operand_size": {"offset": "0x41A10"}, "se::Object::~Object": {"offset": "0xA920"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x2DB20"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA840"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x16FB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x16FD0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA840"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned __int64 const ,std::chrono::duration<__int64,std::ratio<1,1000> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned __int64 const ,std::chrono::duration<__int64,std::ratio<1,1000> > >,void *> > >": {"offset": "0x2DB40"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x16FD0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<unsigned __int64,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<unsigned __int64,void *> > >": {"offset": "0x16FB0"}, "std::_Default_allocator_traits<std::allocator<jitasm::compiler::Lifetime::Interval> >::construct<jitasm::compiler::Lifetime::Interval,jitasm::compiler::Lifetime::Interval const &>": {"offset": "0x15C30"}, "std::_Destroy_range<std::allocator<jitasm::compiler::Lifetime::Interval> >": {"offset": "0x12180"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2C960"}, "std::_Facet_Register": {"offset": "0x42018"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x16F70"}, "std::_Func_class<bool,enum HostState,enum HostState>::~_Func_class<bool,enum HostState,enum HostState>": {"offset": "0x16F70"}, "std::_Func_class<bool,enum rage::InitFunctionType,int,int>::~_Func_class<bool,enum rage::InitFunctionType,int,int>": {"offset": "0x16F70"}, "std::_Func_class<bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::~_Func_class<bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>": {"offset": "0x16F70"}, "std::_Func_class<bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::~_Func_class<bool,enum rage::InitFunctionType,rage::InitFunctionData const &>": {"offset": "0x16F70"}, "std::_Func_class<bool,enum rage::InitFunctionType>::~_Func_class<bool,enum rage::InitFunctionType>": {"offset": "0x16F70"}, "std::_Func_class<bool,int,char const *>::~_Func_class<bool,int,char const *>": {"offset": "0x16F70"}, "std::_Func_class<bool,int,void *,void *>::~_Func_class<bool,int,void *,void *>": {"offset": "0x16F70"}, "std::_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x16F70"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x16F70"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x16F70"}, "std::_Func_class<void,fx::ScriptContext &>::_Reset_move": {"offset": "0x36B40"}, "std::_Func_class<void,fx::ScriptContext &>::~_Func_class<void,fx::ScriptContext &>": {"offset": "0x16F70"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x16F70"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Copy": {"offset": "0x282F0"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Do_call": {"offset": "0x28310"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Move": {"offset": "0x282F0"}, "std::_Func_impl_no_alloc<<lambda_020208b0e0a36ad69f926d618cf66354>,bool,enum HostState,enum HostState>::_Target_type": {"offset": "0x28330"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Copy": {"offset": "0x281B0"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Do_call": {"offset": "0x281D0"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Move": {"offset": "0x281B0"}, "std::_Func_impl_no_alloc<<lambda_05329b97b4e3354ddec7ead020418453>,bool,enum rage::InitFunctionType,int,int>::_Target_type": {"offset": "0x281F0"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x295F0"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x29610"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x295F0"}, "std::_Func_impl_no_alloc<<lambda_218f926e55133abd4d3d3b6ca7d2f780>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x29640"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0xF560"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0xF580"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0xF560"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0xF660"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Copy": {"offset": "0x28390"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Do_call": {"offset": "0x283B0"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Move": {"offset": "0x28390"}, "std::_Func_impl_no_alloc<<lambda_27422173a65a181c16a2ef7522e8d059>,bool,int,void *,void *>::_Target_type": {"offset": "0x283D0"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Copy": {"offset": "0xEE20"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Do_call": {"offset": "0xEE40"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Move": {"offset": "0xEE20"}, "std::_Func_impl_no_alloc<<lambda_2d2a58cd090cb309be8c673da65c4ce8>,bool>::_Target_type": {"offset": "0xEE60"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Copy": {"offset": "0x29740"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Do_call": {"offset": "0x29760"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Move": {"offset": "0x29740"}, "std::_Func_impl_no_alloc<<lambda_3a6692e6c80ea029815359c513aa378d>,bool>::_Target_type": {"offset": "0x29780"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x2A100"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x2A120"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x2A100"}, "std::_Func_impl_no_alloc<<lambda_43fe2a0c2f5ac49ba854f6089af8e843>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x2A140"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29E80"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x298F0"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29F00"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_5d390c504404984603a01667d3d4b147>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29F60"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29790"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x298F0"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29810"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_5ecf81dc2b4f1620e1b1e6a4478eed94>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x298E0"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29B80"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x298F0"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29C00"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_6085d0141ab590130fe09f26899e7cc9>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29C60"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29C70"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x298F0"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29CF0"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_6850f9b66df829470809110098aea58f>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29E70"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Copy": {"offset": "0x2A010"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Do_call": {"offset": "0x2A030"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Move": {"offset": "0x2A010"}, "std::_Func_impl_no_alloc<<lambda_6f7b226809a528ba72e89344b6d55d32>,bool>::_Target_type": {"offset": "0x2A040"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xEFF0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xEF50"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xF070"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xF0C0"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29950"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x298F0"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x299D0"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_7a33ece0fd67ec639c89b5667afebcea>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29B70"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Copy": {"offset": "0x294A0"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0x29590"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29520"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_7f12dbfd2aac39f6f5cbb99b0e9915d9>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29580"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xEE70"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xEF50"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xEEF0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xEF40"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Copy": {"offset": "0x28430"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Do_call": {"offset": "0x29350"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Move": {"offset": "0x28430"}, "std::_Func_impl_no_alloc<<lambda_8b7421f57f11bdbed9b3d618940dba1c>,bool>::_Target_type": {"offset": "0x29380"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Copy": {"offset": "0x28200"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Do_call": {"offset": "0x28220"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Move": {"offset": "0x28200"}, "std::_Func_impl_no_alloc<<lambda_8ccc4ca0333b6d2b740c5b31cfc891e7>,bool,enum rage::InitFunctionType,int,rage::InitFunctionData &>::_Target_type": {"offset": "0x28240"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Copy": {"offset": "0x28250"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Do_call": {"offset": "0x28270"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Move": {"offset": "0x28250"}, "std::_Func_impl_no_alloc<<lambda_8dcb57dbf137996faedc2baf56653ce7>,bool,enum rage::InitFunctionType,rage::InitFunctionData const &>::_Target_type": {"offset": "0x28290"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Copy": {"offset": "0xED40"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Do_call": {"offset": "0xED60"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Move": {"offset": "0xED40"}, "std::_Func_impl_no_alloc<<lambda_b22099e7cd7540a02b96ee4c6307ebf8>,bool>::_Target_type": {"offset": "0xEDA0"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Copy": {"offset": "0x283E0"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Do_call": {"offset": "0x28400"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Move": {"offset": "0x283E0"}, "std::_Func_impl_no_alloc<<lambda_ba1954cd842d4f9c4890ca75aca32e91>,bool,int,char const *>::_Target_type": {"offset": "0x28420"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Copy": {"offset": "0x29F70"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Do_call": {"offset": "0x29F90"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Move": {"offset": "0x29F70"}, "std::_Func_impl_no_alloc<<lambda_bb55b7385050dedd556cf32e37cfd83f>,bool>::_Target_type": {"offset": "0x29FB0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0xF460"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0xF4C0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xE8E0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Copy": {"offset": "0x29FC0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Do_call": {"offset": "0x29FE0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Move": {"offset": "0x29FC0"}, "std::_Func_impl_no_alloc<<lambda_c962f8d1bbb3a12b2dc62956b8cca104>,bool>::_Target_type": {"offset": "0x2A000"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x282A0"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x282C0"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x282A0"}, "std::_Func_impl_no_alloc<<lambda_c9edada0d40c766bc462dfdb720427f9>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x282E0"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Copy": {"offset": "0x2A050"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Do_call": {"offset": "0x2A070"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Move": {"offset": "0x2A050"}, "std::_Func_impl_no_alloc<<lambda_d5e86719cbcf4f8ee3b92cbb20a28559>,void,fwRefContainer<fx::Resource> const &>::_Target_type": {"offset": "0x2A0A0"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Copy": {"offset": "0x29650"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Do_call": {"offset": "0x29670"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Move": {"offset": "0x29650"}, "std::_Func_impl_no_alloc<<lambda_d7bbb4b0aa4529f573af4f3c674e8885>,bool>::_Target_type": {"offset": "0x29730"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Copy": {"offset": "0xEDD0"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Do_call": {"offset": "0xEDF0"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Move": {"offset": "0xEDD0"}, "std::_Func_impl_no_alloc<<lambda_db2d0e133860722aa118ca07946520d3>,bool>::_Target_type": {"offset": "0xEE10"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29440"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29460"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Move": {"offset": "0x29440"}, "std::_Func_impl_no_alloc<<lambda_decc3ad61d1e1da7e469c87a9b07cb56>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29490"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Copy": {"offset": "0x2A0B0"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Do_call": {"offset": "0x2A0D0"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Move": {"offset": "0x2A0B0"}, "std::_Func_impl_no_alloc<<lambda_eb2f2925c678fb620382f71d1b6f1206>,bool>::_Target_type": {"offset": "0x2A0F0"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Copy": {"offset": "0x29410"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x29420"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Move": {"offset": "0x29410"}, "std::_Func_impl_no_alloc<<lambda_ec8cf7fe0fd1c9f27437fc82d3041c7d>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x29430"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x28340"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xEDC0"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x28360"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEDB0"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x28340"}, "std::_Func_impl_no_alloc<<lambda_fc6545f874f7405efd0ba9f5a96da918>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x28380"}, "std::_Guess_median_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x12F50"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x133C0"}, "std::_Make_heap_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x13630"}, "std::_Maklocstr<char>": {"offset": "0xE820"}, "std::_Med3_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x137B0"}, "std::_Med3_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x138E0"}, "std::_Med3_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x13850"}, "std::_Move_unchecked<jitasm::Instr *,jitasm::Instr *>": {"offset": "0x139B0"}, "std::_Partition_by_median_guess_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x139E0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x13FA0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x13D10"}, "std::_Pop_heap_hole_by_index<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64>,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x14300"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x14530"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x14430"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE8E0"}, "std::_Ref_count_obj2<ConVar<bool> >::_Delete_this": {"offset": "0xEAB0"}, "std::_Ref_count_obj2<ConVar<bool> >::_Destroy": {"offset": "0x293D0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0xEAB0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0xEA00"}, "std::_Sort_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x150A0"}, "std::_Sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x14E80"}, "std::_Sort_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x15850"}, "std::_Sort_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x15560"}, "std::_Throw_bad_array_new_length": {"offset": "0xD560"}, "std::_Throw_tree_length_error": {"offset": "0xD580"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x3A760"}, "std::_Traits_compare<std::char_traits<char> >": {"offset": "0x2D3D0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2D70"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2FF0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA860"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Find_hint<int>": {"offset": "0x2D000"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::~_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >": {"offset": "0x2DB70"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2CC70"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Erase": {"offset": "0x36570"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2D290"}, "std::_Tree<std::_Tset_traits<unsigned __int64,std::less<unsigned __int64>,std::allocator<unsigned __int64>,0> >::clear": {"offset": "0x36D80"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x2D360"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA840"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x16FD0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Erase_tree<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x2CE00"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x2CF40"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x36710"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x36AE0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x36BB0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x12E30"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x2CEC0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2D10"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned __int64 const ,std::chrono::duration<__int64,std::ratio<1,1000> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned __int64 const ,std::chrono::duration<__int64,std::ratio<1,1000> > >,void *> > >": {"offset": "0x2CE60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned __int64 const ,std::chrono::duration<__int64,std::ratio<1,1000> > > > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x12E90"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xD300"}, "std::_Tree_val<std::_Tree_simple_types<unsigned __int64> >::_Erase_tree<std::allocator<std::_Tree_node<unsigned __int64,void *> > >": {"offset": "0x12E30"}, "std::_Tree_val<std::_Tree_simple_types<unsigned __int64> >::_Insert_node": {"offset": "0xD300"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2DBD0"}, "std::_Uninitialized_move<jitasm::Instr *,std::allocator<jitasm::Instr> >": {"offset": "0x15B90"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2D430"}, "std::_Xlen_string": {"offset": "0xD5A0"}, "std::allocator<char>::allocate": {"offset": "0xD5C0"}, "std::allocator<char>::deallocate": {"offset": "0x3EE00"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x26830"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x26C70"}, "std::allocator<int>::allocate": {"offset": "0x267C0"}, "std::allocator<int>::deallocate": {"offset": "0x26C20"}, "std::allocator<jitasm::Instr>::allocate": {"offset": "0x26910"}, "std::allocator<jitasm::Instr>::deallocate": {"offset": "0x26D10"}, "std::allocator<jitasm::compiler::BasicBlock *>::allocate": {"offset": "0x26830"}, "std::allocator<jitasm::compiler::BasicBlock *>::deallocate": {"offset": "0x26C70"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::allocate": {"offset": "0x26980"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::deallocate": {"offset": "0x26D60"}, "std::allocator<jitasm::compiler::OrderedLabel>::allocate": {"offset": "0x268A0"}, "std::allocator<jitasm::compiler::OrderedLabel>::deallocate": {"offset": "0x26CC0"}, "std::allocator<jitasm::compiler::RegUsePoint>::deallocate": {"offset": "0x26CC0"}, "std::allocator<jitasm::compiler::VarAttribute>::deallocate": {"offset": "0x26DB0"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x36C80"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x36DF0"}, "std::allocator<std::pair<unsigned __int64,unsigned __int64> >::deallocate": {"offset": "0x26CC0"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x26830"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x26C70"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x3EE00"}, "std::allocator<unsigned int>::allocate": {"offset": "0x267C0"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x26C20"}, "std::allocator<void *>::deallocate": {"offset": "0x26C70"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD620"}, "std::bad_alloc::bad_alloc": {"offset": "0x43404"}, "std::bad_alloc::~bad_alloc": {"offset": "0xAA50"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA640"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xAA50"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x17BC0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x17D00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2C50"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x3CE00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x14670"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x3CF70"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x36CF0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD810"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA3E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA920"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x37220"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA980"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD690"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x3BD10"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x3EE40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x3EFA0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA980"}, "std::chrono::steady_clock::now": {"offset": "0x27500"}, "std::deque<InstrumentedFuncMeta,std::allocator<InstrumentedFuncMeta> >::_Growmap": {"offset": "0x25D50"}, "std::deque<InstrumentedFuncMeta,std::allocator<InstrumentedFuncMeta> >::_Xlen": {"offset": "0x26610"}, "std::deque<InstrumentedFuncMeta,std::allocator<InstrumentedFuncMeta> >::~deque<InstrumentedFuncMeta,std::allocator<InstrumentedFuncMeta> >": {"offset": "0x170F0"}, "std::deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >::~deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >": {"offset": "0x17020"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Growmap": {"offset": "0x25F30"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Tidy": {"offset": "0x263B0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Xlen": {"offset": "0x26610"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::~deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x171C0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Growmap": {"offset": "0x25B70"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Tidy": {"offset": "0x262F0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Xlen": {"offset": "0x26610"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_back": {"offset": "0x277D0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_front": {"offset": "0x27890"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::~deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >": {"offset": "0x16FF0"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x15F00"}, "std::exception::exception": {"offset": "0xA670"}, "std::exception::what": {"offset": "0xE6F0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(enum HostState,enum HostState)>::~function<bool __cdecl(enum HostState,enum HostState)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(enum rage::InitFunctionType)>::~function<bool __cdecl(enum rage::InitFunctionType)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(enum rage::InitFunctionType,int,int)>::~function<bool __cdecl(enum rage::InitFunctionType,int,int)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(enum rage::InitFunctionType,int,rage::InitFunctionData &)>::~function<bool __cdecl(enum rage::InitFunctionType,int,rage::InitFunctionData &)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(enum rage::InitFunctionType,rage::InitFunctionData const &)>::~function<bool __cdecl(enum rage::InitFunctionType,rage::InitFunctionData const &)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(int,char const *)>::~function<bool __cdecl(int,char const *)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(int,void *,void *)>::~function<bool __cdecl(int,void *,void *)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x16F70"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x16F70"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x16F70"}, "std::function<void __cdecl(fwRefContainer<fx::Resource> const &)>::~function<void __cdecl(fwRefContainer<fx::Resource> const &)>": {"offset": "0x16F70"}, "std::function<void __cdecl(fx::ScriptContext &)>::~function<void __cdecl(fx::ScriptContext &)>": {"offset": "0x16F70"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x16F70"}, "std::locale::~locale": {"offset": "0x3A820"}, "std::make_shared<ConVar<bool>,char const (&)[20],enum ConsoleVariableFlags,bool>": {"offset": "0x2D4B0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x3AEE0"}, "std::numpunct<char>::do_falsename": {"offset": "0x3AEF0"}, "std::numpunct<char>::do_grouping": {"offset": "0x3AF30"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x3AF70"}, "std::numpunct<char>::do_truename": {"offset": "0x3AF80"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x3A5B0"}, "std::pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2DC20"}, "std::rotate<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<jitasm::compiler::BasicBlock *> > > >": {"offset": "0x16320"}, "std::runtime_error::runtime_error": {"offset": "0xA6F0"}, "std::shared_lock<std::shared_mutex>::~shared_lock<std::shared_mutex>": {"offset": "0x2DC90"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x171F0"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x171F0"}, "std::to_string": {"offset": "0x27E70"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x17250"}, "std::unique_ptr<fwEvent<int,char const *>::callback,std::default_delete<fwEvent<int,char const *>::callback> >::~unique_ptr<fwEvent<int,char const *>::callback,std::default_delete<fwEvent<int,char const *>::callback> >": {"offset": "0x17240"}, "std::unique_ptr<fwEvent<int,void *,void *>::callback,std::default_delete<fwEvent<int,void *,void *>::callback> >::~unique_ptr<fwEvent<int,void *,void *>::callback,std::default_delete<fwEvent<int,void *,void *>::callback> >": {"offset": "0x17240"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x3A800"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x3A280"}, "ud_decode": {"offset": "0x41AB0"}, "ud_disassemble": {"offset": "0x40090"}, "ud_init": {"offset": "0x400F0"}, "ud_insn_len": {"offset": "0x40190"}, "ud_insn_mnemonic": {"offset": "0x401A0"}, "ud_insn_off": {"offset": "0x401B0"}, "ud_insn_opr": {"offset": "0x401C0"}, "ud_set_asm_buffer": {"offset": "0x401F0"}, "ud_set_input_buffer": {"offset": "0x40220"}, "ud_set_mode": {"offset": "0x40250"}, "ud_set_pc": {"offset": "0x40270"}, "utf8::exception::exception": {"offset": "0x3E190"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x3D210"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x3DB40"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x3E220"}, "utf8::invalid_code_point::what": {"offset": "0x3F150"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xAA50"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x3E290"}, "utf8::invalid_utf8::what": {"offset": "0x3F160"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xAA50"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x3E2F0"}, "utf8::not_enough_room::what": {"offset": "0x3F170"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xAA50"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x3D920"}, "vva": {"offset": "0x3F130"}, "xbr::GetGameBuild": {"offset": "0x1F440"}, "xbr::GetReplaceExecutableInit": {"offset": "0x3FAC0"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x3FCE0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1BC0"}}}