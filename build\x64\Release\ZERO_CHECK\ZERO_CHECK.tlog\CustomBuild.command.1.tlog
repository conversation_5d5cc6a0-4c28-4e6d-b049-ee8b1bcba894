^C:\USERS\<USER>\DESKTOP\FIVEM OFFSETS\BUILD\CMAKEFILES\AE1FEABA6CC7A589E8628609D780E69E\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/fivem offsets" "-BC:/Users/<USER>/Desktop/fivem offsets/build" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Desktop/fivem offsets/build/FiveMOffsetDumper.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
