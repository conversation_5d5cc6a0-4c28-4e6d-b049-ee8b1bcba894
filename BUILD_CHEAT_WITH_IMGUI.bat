@echo off
title FiveM Cheat - BUILD COM IMGUI
color 0A

echo ========================================
echo    FIVEM CHEAT - BUILD COM IMGUI
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo VERIFICANDO DEPENDENCIAS
echo ========================================

REM Verificar se ImGui existe
if not exist "external\imgui\imgui.h" (
    echo [ERROR] ImGui nao encontrado!
    echo.
    echo SOLUCAO:
    echo 1. Execute: SETUP_IMGUI_NEW.bat
    echo 2. Aguarde a configuracao
    echo 3. Execute este script novamente
    echo.
    pause
    exit /b 1
)

echo [OK] ImGui encontrado
echo.

REM Verificar se offsets existem
if not exist "examples\offsets_auto.h" (
    echo [WARNING] offsets_auto.h nao encontrado
    echo [INFO] Criando offsets basicos...
    
    if not exist "examples" mkdir "examples"
    
    REM Criar offsets_auto.h basico
    echo // Offsets hardcoded para FiveM > "examples\offsets_auto.h"
    echo #pragma once >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo namespace FiveMOffsets { >> "examples\offsets_auto.h"
    echo     constexpr uintptr_t MODULE_BASE = 0x140000000; >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Functions { >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GetPlayerPed = MODULE_BASE + 0x1639CE8; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GetAllPhysicalPlayers = MODULE_BASE + 0x118B6EB; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t RepairVehicle = MODULE_BASE + 0xD8D278; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GiveAmmo = MODULE_BASE + 0xD89B8C; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Globals { >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t g_vehiclePool = MODULE_BASE + 0xD7383C; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t CWorld = MODULE_BASE + 0x737F0C; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Modifiers { >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t WeaponDamageModifier = MODULE_BASE + 0x415BD3; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t InfiniteAmmo = MODULE_BASE + 0x10695C1; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t VehicleDamageModifier = MODULE_BASE + 0xEF6849; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo } >> "examples\offsets_auto.h"
    
    echo [OK] offsets_auto.h criado
) else (
    echo [OK] offsets_auto.h encontrado
)

echo.

echo ========================================
echo CRIANDO CHEAT COM IMGUI
echo ========================================

REM Criar cheat com ImGui se nao existir
if not exist "examples\cheat_with_imgui.cpp" (
    echo [INFO] Criando cheat com ImGui...
    
    echo // FiveM Cheat com ImGui e Offsets Hardcoded > "examples\cheat_with_imgui.cpp"
    echo #include ^<windows.h^> >> "examples\cheat_with_imgui.cpp"
    echo #include ^<iostream^> >> "examples\cheat_with_imgui.cpp"
    echo #include ^<vector^> >> "examples\cheat_with_imgui.cpp"
    echo #include ^<d3d11.h^> >> "examples\cheat_with_imgui.cpp"
    echo #include "../external/imgui/imgui.h" >> "examples\cheat_with_imgui.cpp"
    echo #include "../external/imgui/backends/imgui_impl_win32.h" >> "examples\cheat_with_imgui.cpp"
    echo #include "../external/imgui/backends/imgui_impl_dx11.h" >> "examples\cheat_with_imgui.cpp"
    echo #include "offsets_auto.h" >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo #pragma comment^(lib, "d3d11.lib"^) >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Variaveis globais >> "examples\cheat_with_imgui.cpp"
    echo bool g_showMenu = false; >> "examples\cheat_with_imgui.cpp"
    echo bool g_espEnabled = false; >> "examples\cheat_with_imgui.cpp"
    echo bool g_godModeEnabled = false; >> "examples\cheat_with_imgui.cpp"
    echo bool g_infiniteAmmoEnabled = false; >> "examples\cheat_with_imgui.cpp"
    echo bool g_noReloadEnabled = false; >> "examples\cheat_with_imgui.cpp"
    echo bool g_vehicleGodModeEnabled = false; >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // DirectX 11 >> "examples\cheat_with_imgui.cpp"
    echo ID3D11Device* g_pd3dDevice = nullptr; >> "examples\cheat_with_imgui.cpp"
    echo ID3D11DeviceContext* g_pd3dDeviceContext = nullptr; >> "examples\cheat_with_imgui.cpp"
    echo IDXGISwapChain* g_pSwapChain = nullptr; >> "examples\cheat_with_imgui.cpp"
    echo ID3D11RenderTargetView* g_mainRenderTargetView = nullptr; >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Funcao para ler memoria com seguranca >> "examples\cheat_with_imgui.cpp"
    echo template^<typename T^> >> "examples\cheat_with_imgui.cpp"
    echo T ReadMemory^(uintptr_t address^) { >> "examples\cheat_with_imgui.cpp"
    echo     __try { >> "examples\cheat_with_imgui.cpp"
    echo         return *reinterpret_cast^<T*^>^(address^); >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\cheat_with_imgui.cpp"
    echo         return T{}; >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Funcao para escrever memoria com seguranca >> "examples\cheat_with_imgui.cpp"
    echo template^<typename T^> >> "examples\cheat_with_imgui.cpp"
    echo void WriteMemory^(uintptr_t address, T value^) { >> "examples\cheat_with_imgui.cpp"
    echo     __try { >> "examples\cheat_with_imgui.cpp"
    echo         *reinterpret_cast^<T*^>^(address^) = value; >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\cheat_with_imgui.cpp"
    echo         // Ignorar erros >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Obter jogador local >> "examples\cheat_with_imgui.cpp"
    echo uintptr_t GetLocalPlayer^(^) { >> "examples\cheat_with_imgui.cpp"
    echo     auto GetPlayerPed = reinterpret_cast^<uintptr_t^(*^)^(int^)^>^(FiveMOffsets::Functions::GetPlayerPed^); >> "examples\cheat_with_imgui.cpp"
    echo     __try { >> "examples\cheat_with_imgui.cpp"
    echo         return GetPlayerPed^(-1^); >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\cheat_with_imgui.cpp"
    echo         return 0; >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // God Mode >> "examples\cheat_with_imgui.cpp"
    echo void UpdateGodMode^(^) { >> "examples\cheat_with_imgui.cpp"
    echo     if ^(g_godModeEnabled^) { >> "examples\cheat_with_imgui.cpp"
    echo         uintptr_t localPlayer = GetLocalPlayer^(^); >> "examples\cheat_with_imgui.cpp"
    echo         if ^(localPlayer^) { >> "examples\cheat_with_imgui.cpp"
    echo             WriteMemory^<float^>^(localPlayer + 0x280, 100.0f^); // Health >> "examples\cheat_with_imgui.cpp"
    echo             WriteMemory^<float^>^(localPlayer + 0x14C, 100.0f^); // Armor >> "examples\cheat_with_imgui.cpp"
    echo         } >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Infinite Ammo >> "examples\cheat_with_imgui.cpp"
    echo void UpdateInfiniteAmmo^(^) { >> "examples\cheat_with_imgui.cpp"
    echo     if ^(g_infiniteAmmoEnabled^) { >> "examples\cheat_with_imgui.cpp"
    echo         WriteMemory^<bool^>^(FiveMOffsets::Modifiers::InfiniteAmmo, true^); >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Renderizar menu ImGui >> "examples\cheat_with_imgui.cpp"
    echo void RenderMenu^(^) { >> "examples\cheat_with_imgui.cpp"
    echo     if ^(!g_showMenu^) return; >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Begin^("FiveM Cheat Menu", ^&g_showMenu^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Text^("FiveM Cheat com Offsets Hardcoded"^); >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Separator^(^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     if ^(ImGui::Button^("Player Hacks"^)^) {} >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Checkbox^("God Mode", ^&g_godModeEnabled^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Separator^(^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     if ^(ImGui::Button^("Weapon Hacks"^)^) {} >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Checkbox^("Infinite Ammo", ^&g_infiniteAmmoEnabled^); >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Checkbox^("No Reload", ^&g_noReloadEnabled^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Separator^(^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     if ^(ImGui::Button^("Vehicle Hacks"^)^) {} >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Checkbox^("Vehicle God Mode", ^&g_vehicleGodModeEnabled^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Separator^(^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     if ^(ImGui::Button^("ESP"^)^) {} >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::Checkbox^("Enable ESP", ^&g_espEnabled^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     ImGui::End^(^); >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // Thread principal do cheat >> "examples\cheat_with_imgui.cpp"
    echo DWORD WINAPI CheatThread^(LPVOID lpParam^) { >> "examples\cheat_with_imgui.cpp"
    echo     AllocConsole^(^); >> "examples\cheat_with_imgui.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stdin^), "CONIN$", "r", stdin^); >> "examples\cheat_with_imgui.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stdout^), "CONOUT$", "w", stdout^); >> "examples\cheat_with_imgui.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stderr^), "CONOUT$", "w", stderr^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     std::cout ^<^< "[FiveM Cheat] Iniciado com ImGui e offsets hardcoded!" ^<^< std::endl; >> "examples\cheat_with_imgui.cpp"
    echo     std::cout ^<^< "[FiveM Cheat] INSERT = Menu, F1 = God Mode, F2 = Infinite Ammo" ^<^< std::endl; >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     while ^(true^) { >> "examples\cheat_with_imgui.cpp"
    echo         // Toggle menu >> "examples\cheat_with_imgui.cpp"
    echo         if ^(GetAsyncKeyState^(VK_INSERT^) ^& 0x8000^) { >> "examples\cheat_with_imgui.cpp"
    echo             g_showMenu = !g_showMenu; >> "examples\cheat_with_imgui.cpp"
    echo             std::cout ^<^< "[Menu] " ^<^< ^(g_showMenu ? "ABERTO" : "FECHADO"^) ^<^< std::endl; >> "examples\cheat_with_imgui.cpp"
    echo             Sleep^(500^); >> "examples\cheat_with_imgui.cpp"
    echo         } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo         // Toggle God Mode >> "examples\cheat_with_imgui.cpp"
    echo         if ^(GetAsyncKeyState^(VK_F1^) ^& 0x8000^) { >> "examples\cheat_with_imgui.cpp"
    echo             g_godModeEnabled = !g_godModeEnabled; >> "examples\cheat_with_imgui.cpp"
    echo             std::cout ^<^< "[God Mode] " ^<^< ^(g_godModeEnabled ? "ON" : "OFF"^) ^<^< std::endl; >> "examples\cheat_with_imgui.cpp"
    echo             Sleep^(500^); >> "examples\cheat_with_imgui.cpp"
    echo         } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo         // Toggle Infinite Ammo >> "examples\cheat_with_imgui.cpp"
    echo         if ^(GetAsyncKeyState^(VK_F2^) ^& 0x8000^) { >> "examples\cheat_with_imgui.cpp"
    echo             g_infiniteAmmoEnabled = !g_infiniteAmmoEnabled; >> "examples\cheat_with_imgui.cpp"
    echo             std::cout ^<^< "[Infinite Ammo] " ^<^< ^(g_infiniteAmmoEnabled ? "ON" : "OFF"^) ^<^< std::endl; >> "examples\cheat_with_imgui.cpp"
    echo             Sleep^(500^); >> "examples\cheat_with_imgui.cpp"
    echo         } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo         // Atualizar cheats >> "examples\cheat_with_imgui.cpp"
    echo         UpdateGodMode^(^); >> "examples\cheat_with_imgui.cpp"
    echo         UpdateInfiniteAmmo^(^); >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo         // Renderizar ImGui >> "examples\cheat_with_imgui.cpp"
    echo         if ^(g_showMenu^) { >> "examples\cheat_with_imgui.cpp"
    echo             ImGui::NewFrame^(^); >> "examples\cheat_with_imgui.cpp"
    echo             RenderMenu^(^); >> "examples\cheat_with_imgui.cpp"
    echo             ImGui::Render^(^); >> "examples\cheat_with_imgui.cpp"
    echo         } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo         Sleep^(50^); >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo     return 0; >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    echo. >> "examples\cheat_with_imgui.cpp"
    echo // DLL Entry Point >> "examples\cheat_with_imgui.cpp"
    echo BOOL APIENTRY DllMain^(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved^) { >> "examples\cheat_with_imgui.cpp"
    echo     switch ^(ul_reason_for_call^) { >> "examples\cheat_with_imgui.cpp"
    echo         case DLL_PROCESS_ATTACH: >> "examples\cheat_with_imgui.cpp"
    echo             CreateThread^(nullptr, 0, CheatThread, nullptr, 0, nullptr^); >> "examples\cheat_with_imgui.cpp"
    echo             break; >> "examples\cheat_with_imgui.cpp"
    echo         case DLL_PROCESS_DETACH: >> "examples\cheat_with_imgui.cpp"
    echo             FreeConsole^(^); >> "examples\cheat_with_imgui.cpp"
    echo             break; >> "examples\cheat_with_imgui.cpp"
    echo     } >> "examples\cheat_with_imgui.cpp"
    echo     return TRUE; >> "examples\cheat_with_imgui.cpp"
    echo } >> "examples\cheat_with_imgui.cpp"
    
    echo [OK] cheat_with_imgui.cpp criado
) else (
    echo [OK] cheat_with_imgui.cpp ja existe
)

echo.

echo ========================================
echo COMPILANDO DLL COM IMGUI
echo ========================================

REM Configurar Visual Studio
echo [INFO] Configurando Visual Studio...

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    echo [OK] Visual Studio 2022 encontrado
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    echo [OK] Visual Studio 2019 encontrado
) else (
    echo [ERROR] Visual Studio nao encontrado!
    pause
    exit /b 1
)

echo.

REM Entrar na pasta examples
cd examples

echo [INFO] Compilando FiveMCheat_ImGui.dll...
echo [INFO] Usando offsets hardcoded + ImGui

REM Limpar builds anteriores
if exist "FiveMCheat_ImGui.dll" del "FiveMCheat_ImGui.dll"

REM Compilar DLL com ImGui
cl.exe /LD /EHsc /std:c++17 ^
    /I"..\external\imgui" ^
    /I"..\external\imgui\backends" ^
    cheat_with_imgui.cpp ^
    ..\external\imgui\imgui.cpp ^
    ..\external\imgui\backends\imgui_impl_dx11.cpp ^
    ..\external\imgui\backends\imgui_impl_win32.cpp ^
    /Fe:FiveMCheat_ImGui.dll ^
    /link user32.lib gdi32.lib d3d11.lib >nul 2>&1

if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao!
    echo.
    echo Tentando com mais detalhes...
    cl.exe /LD /EHsc /std:c++17 ^
        /I"..\external\imgui" ^
        /I"..\external\imgui\backends" ^
        cheat_with_imgui.cpp ^
        ..\external\imgui\imgui.cpp ^
        ..\external\imgui\backends\imgui_impl_dx11.cpp ^
        ..\external\imgui\backends\imgui_impl_win32.cpp ^
        /Fe:FiveMCheat_ImGui.dll ^
        /link user32.lib gdi32.lib d3d11.lib
    cd ..
    pause
    exit /b 1
)

REM Verificar se DLL foi criada
if exist "FiveMCheat_ImGui.dll" (
    echo [OK] FiveMCheat_ImGui.dll compilada com sucesso!
    echo.
    
    REM Mostrar informacoes da DLL
    dir FiveMCheat_ImGui.dll | find "FiveMCheat_ImGui.dll"
    
    REM Copiar para pasta raiz
    copy "FiveMCheat_ImGui.dll" "..\FiveMCheat_ImGui.dll" >nul
    echo [OK] DLL copiada para pasta raiz
    
) else (
    echo [ERROR] FiveMCheat_ImGui.dll nao foi criada!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.

echo ========================================
echo COMPILACAO CONCLUIDA!
echo ========================================
echo.
echo ✅ ARQUIVOS CRIADOS:
echo.
echo 📁 FiveMCheat_ImGui.dll - Cheat com ImGui
echo 📁 examples\cheat_with_imgui.cpp - Codigo fonte
echo 📁 examples\offsets_auto.h - Offsets hardcoded
echo 📁 external\imgui\ - Dear ImGui
echo.
echo 🎯 CARACTERISTICAS:
echo.
echo ✅ Interface ImGui profissional
echo ✅ Offsets HARDCODED (enderecos absolutos)
echo ✅ Menu interativo (INSERT)
echo ✅ Funcionalidades completas
echo ✅ Console para debug
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [INSERT] - Abrir/fechar MENU IMGUI
echo [F1]     - Toggle God Mode
echo [F2]     - Toggle Infinite Ammo
echo.
echo 📋 MENU IMGUI:
echo.
echo ✅ Player Hacks (God Mode, etc)
echo ✅ Weapon Hacks (Infinite Ammo, No Reload)
echo ✅ Vehicle Hacks (God Mode, etc)
echo ✅ ESP (Enable/Disable)
echo.
echo 🚀 COMO USAR:
echo.
echo 1. Abra o FiveM e entre em um servidor
echo 2. Use um injetor para injetar FiveMCheat_ImGui.dll
echo 3. Pressione INSERT para abrir o menu ImGui
echo 4. Use F1/F2 para atalhos rapidos
echo.

echo ========================================
echo ✅ CHEAT COM IMGUI PRONTO!
echo ========================================

echo.
echo Pressione qualquer tecla para sair...
pause >nul
