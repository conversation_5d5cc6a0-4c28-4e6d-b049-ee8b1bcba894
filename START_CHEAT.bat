@echo off
title FiveM Cheat - INICIAR CHEAT
color 0A

echo ========================================
echo        FIVEM CHEAT - INICIAR
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] EXECUTE COMO ADMINISTRADOR!
    echo.
    echo 1. Clique com botao direito neste arquivo
    echo 2. Selecione "Executar como administrador"
    echo.
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo PASSO 1: VERIFICANDO FIVEM
echo ========================================

REM Verificar se FiveM está rodando
tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GameProcess.exe" 2>NUL | find /I /N "FiveM_GameProcess.exe">NUL
set "fivem_game_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 if %fivem_game_running% neq 0 (
    echo [WARNING] FiveM NAO ESTA RODANDO!
    echo.
    echo INSTRUCOES:
    echo 1. Abra o FiveM
    echo 2. Entre em um servidor (preferencialmente privado/teste)
    echo 3. Aguarde carregar COMPLETAMENTE no jogo
    echo 4. Execute este script novamente
    echo.
    echo Deseja continuar mesmo assim? (s/n)
    set /p choice="> "
    if /i not "%choice%"=="s" exit /b 1
) else (
    echo [OK] FiveM detectado rodando!
    
    REM Mostrar processos encontrados
    if %fivem_running% equ 0 echo     - FiveM.exe encontrado
    if %fivem_gta_running% equ 0 echo     - FiveM_GTAProcess.exe encontrado  
    if %fivem_game_running% equ 0 echo     - FiveM_GameProcess.exe encontrado
)

echo.

echo ========================================
echo PASSO 2: COMPILANDO CHEAT
echo ========================================

REM Verificar se offsets existem
if not exist "examples\offsets_auto.h" (
    echo [INFO] Extraindo offsets dos seus arquivos JSON...
    python extract_offsets.py
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao extrair offsets!
        pause
        exit /b 1
    )
)

echo [OK] Offsets reais encontrados (155 offsets)

REM Configurar ImGui se necessário
if not exist "external\imgui" (
    echo [INFO] Configurando Dear ImGui...
    call setup_imgui.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao configurar ImGui!
        pause
        exit /b 1
    )
)

REM Compilar cheat
echo [INFO] Compilando cheat com offsets reais...
cd examples
call build_cheat.bat
if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao!
    cd ..
    pause
    exit /b 1
)

if exist "FiveMSimpleCheat.dll" (
    echo [OK] FiveMSimpleCheat.dll compilado!
) else (
    echo [ERROR] DLL nao foi criada!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.

echo ========================================
echo PASSO 3: PREPARANDO INJETOR
echo ========================================

REM Compilar injetor se necessário
if not exist "SimpleInjector.exe" (
    echo [INFO] Compilando injetor...
    cd tools
    call build_injector.bat
    cd ..
)

if exist "SimpleInjector.exe" (
    echo [OK] SimpleInjector.exe disponivel
) else (
    echo [WARNING] Injetor nao disponivel, usando metodo alternativo
)

echo.

echo ========================================
echo PASSO 4: INJETANDO CHEAT
echo ========================================

echo [INFO] Iniciando injecao do cheat...
echo.

if exist "SimpleInjector.exe" (
    echo METODO 1: Usando SimpleInjector
    echo.
    echo INSTRUCOES:
    echo 1. O SimpleInjector vai abrir
    echo 2. Selecione opcao 2 (Injetar FiveMSimpleCheat.dll)
    echo 3. Aguarde mensagem de sucesso
    echo.
    echo Pressione ENTER para abrir o injetor...
    pause >nul
    
    start SimpleInjector.exe
    
    echo [INFO] SimpleInjector aberto!
    echo Aguarde a injecao ser concluida...
    
) else (
    echo METODO 2: Injecao Manual
    echo.
    echo OPCOES PARA INJETAR:
    echo.
    echo A) Process Hacker (Recomendado):
    echo    1. Baixe Process Hacker
    echo    2. Abra como administrador
    echo    3. Encontre FiveM_GameProcess.exe
    echo    4. Clique direito ^> Miscellaneous ^> Inject DLL
    echo    5. Selecione: examples\FiveMSimpleCheat.dll
    echo.
    echo B) Extreme Injector:
    echo    1. Baixe Extreme Injector
    echo    2. Process: FiveM_GameProcess.exe
    echo    3. DLL: examples\FiveMSimpleCheat.dll
    echo    4. Clique Inject
    echo.
    echo C) PowerShell (Avancado):
    echo    Executar: quick_inject.bat
    echo.
    
    echo Pressione qualquer tecla apos injetar...
    pause >nul
)

echo.

echo ========================================
echo CHEAT INICIADO! - INSTRUCOES DE USO
echo ========================================
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [INSERT] - Abrir/fechar MENU PRINCIPAL
echo [F1]     - Toggle ESP (ver jogadores)
echo [F2]     - Toggle GOD MODE (vida infinita)
echo [F3]     - Teleport para waypoint
echo [F4]     - Reparar veiculo atual
echo [F5]     - Munição infinita
echo.
echo 📋 MENU PRINCIPAL (INSERT):
echo.
echo ┌─ Player Hacks
echo │  ├─ God Mode On/Off
echo │  ├─ Heal (vida completa)
echo │  ├─ Teleport para coordenadas
echo │  └─ Stamina infinita
echo │
echo ├─ Weapon Hacks  
echo │  ├─ Infinite Ammo
echo │  ├─ No Reload
echo │  └─ Damage Multiplier
echo │
echo ├─ Vehicle Hacks
echo │  ├─ Repair Vehicle
echo │  ├─ Speed Boost
echo │  └─ Invincible Vehicle
echo │
echo └─ ESP Settings
echo    ├─ Toggle ESP
echo    ├─ Show Health/Armor
echo    └─ Show Distance
echo.
echo 🎯 TESTE SUGERIDO:
echo.
echo 1. Pressione INSERT para abrir o menu
echo 2. Ative ESP (F1) para ver outros jogadores
echo 3. Ative God Mode (F2) e deixe NPCs atacarem
echo 4. Teste teleport e outras funcionalidades
echo.
echo ⚠️  IMPORTANTE:
echo.
echo ✓ Use apenas em servidores PRIVADOS/TESTE
echo ✓ Offsets baseados nos seus arquivos JSON reais
echo ✓ 155 offsets extraidos automaticamente
echo ✓ Se algo nao funcionar, os offsets podem precisar atualizacao
echo.

REM Criar log de inicio
echo FiveM Cheat Started - %date% %time% > cheat_started_log.txt
echo Status: Cheat iniciado com sucesso >> cheat_started_log.txt
echo DLL: examples\FiveMSimpleCheat.dll >> cheat_started_log.txt
echo Offsets: 155 offsets reais >> cheat_started_log.txt

echo ========================================
echo ✅ CHEAT INICIADO COM SUCESSO!
echo ========================================
echo.
echo O cheat esta rodando no FiveM!
echo Use os controles acima para testar.
echo.
echo Log salvo em: cheat_started_log.txt
echo.

echo Pressione qualquer tecla para sair...
pause >nul
