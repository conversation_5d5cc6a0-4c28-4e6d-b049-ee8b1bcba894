{"rage-graphics-five.dll": {"<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x16C40"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x16C40"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xBB90"}, "AddCrashometry": {"offset": "0x5A830"}, "AddCrashometryV": {"offset": "0x5A9B0"}, "AddTextureOverride": {"offset": "0x3AD20"}, "AllocateBuffer": {"offset": "0x5F900"}, "BufferBackedDXGISwapChain::GetBuffer": {"offset": "0x368B0"}, "BufferBackedDXGISwapChain::GetContainingOutput": {"offset": "0x10030"}, "BufferBackedDXGISwapChain::GetDesc": {"offset": "0x36900"}, "BufferBackedDXGISwapChain::GetDevice": {"offset": "0x36480"}, "BufferBackedDXGISwapChain::GetFrameStatistics": {"offset": "0x10030"}, "BufferBackedDXGISwapChain::GetFullscreenState": {"offset": "0x368F0"}, "BufferBackedDXGISwapChain::GetLastPresentCount": {"offset": "0x10030"}, "BufferBackedDXGISwapChain::GetParent": {"offset": "0x36470"}, "BufferBackedDXGISwapChain::GetPrivateData": {"offset": "0x36470"}, "BufferBackedDXGISwapChain::Present": {"offset": "0x36520"}, "BufferBackedDXGISwapChain::RecreateFromDesc": {"offset": "0x3F130"}, "BufferBackedDXGISwapChain::ResizeBuffers": {"offset": "0x36930"}, "BufferBackedDXGISwapChain::ResizeTarget": {"offset": "0x10030"}, "BufferBackedDXGISwapChain::SetFullscreenState": {"offset": "0x10030"}, "BufferBackedDXGISwapChain::SetPrivateData": {"offset": "0x36470"}, "BufferBackedDXGISwapChain::SetPrivateDataInterface": {"offset": "0x36470"}, "CaptureBufferOutput": {"offset": "0x3AD70"}, "CaptureInternalScreenshot": {"offset": "0x3B1D0"}, "CfxState::CfxState": {"offset": "0x38980"}, "ClearRenderTarget": {"offset": "0x54E10"}, "Component::As": {"offset": "0x10030"}, "Component::IsA": {"offset": "0x100F0"}, "Component::SetCommandLine": {"offset": "0xBA70"}, "Component::SetUserData": {"offset": "0x10100"}, "ComponentInstance::DoGameLoad": {"offset": "0x100D0"}, "ComponentInstance::Initialize": {"offset": "0x100E0"}, "ComponentInstance::Shutdown": {"offset": "0x10100"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Allocate_page": {"offset": "0x41270"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Assign_and_destroy_item": {"offset": "0x41400"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Copy_item": {"offset": "0x41390"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Deallocate_page": {"offset": "0x41300"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Move_item": {"offset": "0x413E0"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::~concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >": {"offset": "0x415D0"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::_Allocate_page": {"offset": "0x41270"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::_Assign_and_destroy_item": {"offset": "0x41200"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::_Copy_item": {"offset": "0x41120"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::_Deallocate_page": {"offset": "0x41300"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::_Move_item": {"offset": "0x411A0"}, "Concurrency::concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >::~concurrent_queue<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >,std::allocator<std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> > > >": {"offset": "0x41640"}, "Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >::_Find": {"offset": "0x40580"}, "Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >::_Initialize_bucket": {"offset": "0x40AC0"}, "Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >::_Insert<std::pair<void *,bool> >": {"offset": "0x37510"}, "Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >::_Set_bucket": {"offset": "0x40C80"}, "Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<void *,bool,Concurrency::details::_Hash_compare<void *,std::hash<void *>,std::equal_to<void *> >,std::allocator<std::pair<void * const,bool> >,0> >": {"offset": "0x38BC0"}, "Concurrency::details::_Split_order_list_value<std::pair<void * const,bool>,std::allocator<std::pair<void * const,bool> > >::_Buynode<std::pair<void *,bool> >": {"offset": "0x37100"}, "Concurrency::details::_Split_ordered_list<std::pair<void * const,bool>,std::allocator<std::pair<void * const,bool> > >::~_Split_ordered_list<std::pair<void * const,bool>,std::allocator<std::pair<void * const,bool> > >": {"offset": "0x38D40"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x34800"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0x298B0"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0x29A90"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x31880"}, "ConsoleFlagsToString": {"offset": "0x33380"}, "CoreGetComponentRegistry": {"offset": "0xCCA0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0xCD30"}, "CreateComponent": {"offset": "0x10110"}, "CreateD3D11DeviceWrap": {"offset": "0x3B9A0"}, "CreateD3D11DeviceWrapOrig": {"offset": "0x3BB30"}, "CreateRasterizerState": {"offset": "0x1EF10"}, "CreateRenderTarget": {"offset": "0x54E30"}, "CreateSamplerState": {"offset": "0x1EF20"}, "CreateTrampolineFunction": {"offset": "0x5FCA0"}, "CreateVariableEntry<bool>": {"offset": "0x2A460"}, "CreateWindowExWStub": {"offset": "0x425F0"}, "D3D11CreateDeviceWoo": {"offset": "0x290E0"}, "D3DPresent": {"offset": "0x3C3F0"}, "DXGetErrorDescriptionW": {"offset": "0x43260"}, "DXGetErrorStringW": {"offset": "0x446A0"}, "DeferredFullscreenSwapChain::GetBuffer": {"offset": "0x369F0"}, "DeferredFullscreenSwapChain::GetContainingOutput": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::GetDesc": {"offset": "0x36B10"}, "DeferredFullscreenSwapChain::GetDevice": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::GetFrameStatistics": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::GetFullscreenState": {"offset": "0x36A20"}, "DeferredFullscreenSwapChain::GetLastPresentCount": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::GetParent": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::GetPrivateData": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::Present": {"offset": "0x369E0"}, "DeferredFullscreenSwapChain::ResizeBuffers": {"offset": "0x36B20"}, "DeferredFullscreenSwapChain::ResizeTarget": {"offset": "0x36B30"}, "DeferredFullscreenSwapChain::SetFullscreenState": {"offset": "0x36A00"}, "DeferredFullscreenSwapChain::SetPrivateData": {"offset": "0x36470"}, "DeferredFullscreenSwapChain::SetPrivateDataInterface": {"offset": "0x36470"}, "DispatchMessageWFake": {"offset": "0x428F0"}, "DisplayD3DCrashMessageENBSeries": {"offset": "0x3C840"}, "DisplayD3DCrashMessageGeneric": {"offset": "0x3C8F0"}, "DisplayD3DCrashMessageGraphicsMods": {"offset": "0x3C9A0"}, "DisplayD3DCrashMessageReShade": {"offset": "0x3CA50"}, "DisplayD3DCrashMessageReShadeENBSeries": {"offset": "0x3CB00"}, "DisplayD3DCrashMessageWrap": {"offset": "0x3CBB0"}, "DllMain": {"offset": "0x6201C"}, "DoNtRaiseException": {"offset": "0x5D070"}, "DrawImSprite": {"offset": "0x1F270"}, "EnableAllHooksLL": {"offset": "0x5EDA0"}, "EnableHook": {"offset": "0x5EEF0"}, "EnableHookLL": {"offset": "0x5F010"}, "EnqueueGenericDrawCommand": {"offset": "0x203A0"}, "FatalErrorNoExceptRealV": {"offset": "0xD140"}, "FatalErrorRealV": {"offset": "0xD170"}, "FindFirstFileAStub": {"offset": "0x290F0"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x38A0"}, "FreeBuffer": {"offset": "0x5F930"}, "Freeze": {"offset": "0x5F120"}, "GenerateLineStrip": {"offset": "0x20840"}, "GetAbsoluteCitPath": {"offset": "0x5AC50"}, "GetAbsoluteGamePath": {"offset": "0x5AF50"}, "GetBlendState": {"offset": "0x20D00"}, "GetCursorInfoFake": {"offset": "0x42960"}, "GetD3D11Device": {"offset": "0x20D10"}, "GetD3D11DeviceContext": {"offset": "0x20D30"}, "GetDepthStencilState": {"offset": "0x20D50"}, "GetGameResolution": {"offset": "0x20E40"}, "GetGraphicsModDetails": {"offset": "0x3D920"}, "GetImDiffuseSamplerState": {"offset": "0x20ED0"}, "GetInvariantD3D11Device": {"offset": "0x3DEC0"}, "GetInvariantD3D11DeviceContext": {"offset": "0x3DF90"}, "GetMemoryBlock": {"offset": "0x5F9B0"}, "GetProcAddressStub": {"offset": "0x29100"}, "GetRasterizerState": {"offset": "0x20FA0"}, "GetStockStateIdentifier": {"offset": "0x20FB0"}, "GetTickCountStub": {"offset": "0x29160"}, "GfxForceVsync": {"offset": "0x3E190"}, "GlobalErrorHandler": {"offset": "0xD3B0"}, "GoGetAdapter": {"offset": "0x3E1A0"}, "HandleMessageLoop": {"offset": "0x42980"}, "HookClipCursor": {"offset": "0x3E370"}, "HookCreateWindowExW": {"offset": "0x3E380"}, "HookFunction::Run": {"offset": "0x10140"}, "HookFunctionBase::Register": {"offset": "0x5E790"}, "HookFunctionBase::RunAll": {"offset": "0x5E7B0"}, "HookGetCursorPos": {"offset": "0x3E780"}, "HookGetForegroundWindow": {"offset": "0x3E7B0"}, "HookSetCapture": {"offset": "0x10030"}, "HookSetFocus": {"offset": "0x16C00"}, "HookShowCursor": {"offset": "0x3E910"}, "HookShowWindow": {"offset": "0x3E370"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x38280"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x5A050"}, "HostSharedData<GameRenderData>::HostSharedData<GameRenderData>": {"offset": "0x384D0"}, "HostSharedData<ReverseGameData>::HostSharedData<ReverseGameData>": {"offset": "0x38730"}, "InitFunction::Run": {"offset": "0xFB10"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x5CE80"}, "InitFunctionBase::Register": {"offset": "0x5D1E0"}, "InitFunctionBase::RunAll": {"offset": "0x5D230"}, "InitializeBuffer": {"offset": "0xBA70"}, "InvokeRender": {"offset": "0x3E920"}, "IsCL2": {"offset": "0x3EB10"}, "IsCodePadding": {"offset": "0x60050"}, "IsExecutableAddress": {"offset": "0x5FC10"}, "IsInRenderQuery": {"offset": "0x3EB90"}, "IsOnRenderThread": {"offset": "0x210A0"}, "IsSafeToUseDXGI": {"offset": "0x3EC20"}, "IsValidGraphicsLibrary": {"offset": "0x338A0"}, "IsWindows10OrGreater": {"offset": "0x3EDF0"}, "LimitFrameTime": {"offset": "0x3EEB0"}, "LoadLibraryAHook": {"offset": "0x34680"}, "LoadLibraryAStub": {"offset": "0x29190"}, "MH_CreateHook": {"offset": "0x5F2D0"}, "MH_EnableHook": {"offset": "0x5F560"}, "MH_Initialize": {"offset": "0x5F570"}, "MH_Uninitialize": {"offset": "0x5F610"}, "MakeRelativeCitPath": {"offset": "0xDA50"}, "MakeRelativeGamePath": {"offset": "0x3EFB0"}, "Microsoft::WRL::ComPtr<BufferBackedDXGISwapChain>::~ComPtr<BufferBackedDXGISwapChain>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<DeferredFullscreenSwapChain>::~ComPtr<DeferredFullscreenSwapChain>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11BlendState>::~ComPtr<ID3D11BlendState>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11DepthStencilView>::~ComPtr<ID3D11DepthStencilView>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11Device>::~ComPtr<ID3D11Device>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11DeviceContext>::~ComPtr<ID3D11DeviceContext>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11PixelShader>::~ComPtr<ID3D11PixelShader>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11RenderTargetView>::~ComPtr<ID3D11RenderTargetView>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11SamplerState>::~ComPtr<ID3D11SamplerState>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11ShaderResourceView>::~ComPtr<ID3D11ShaderResourceView>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11Texture2D>::ComPtr<ID3D11Texture2D>": {"offset": "0x38270"}, "Microsoft::WRL::ComPtr<ID3D11Texture2D>::~ComPtr<ID3D11Texture2D>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3D11VertexShader>::~ComPtr<ID3D11VertexShader>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<ID3DUserDefinedAnnotation>::~ComPtr<ID3DUserDefinedAnnotation>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIAdapter1>::~ComPtr<IDXGIAdapter1>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIAdapter>::~ComPtr<IDXGIAdapter>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIDevice>::~ComPtr<IDXGIDevice>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIKeyedMutex>::~ComPtr<IDXGIKeyedMutex>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIOutput>::~ComPtr<IDXGIOutput>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGIResource>::~ComPtr<IDXGIResource>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IDXGISwapChain1>::~ComPtr<IDXGISwapChain1>": {"offset": "0x38B40"}, "Microsoft::WRL::ComPtr<IUnknown>::~ComPtr<IUnknown>": {"offset": "0x38B40"}, "Microsoft::WRL::Details::MakeAllocator<BufferBackedDXGISwapChain>::~MakeAllocator<BufferBackedDXGISwapChain>": {"offset": "0x38B70"}, "Microsoft::WRL::Details::RuntimeClassImpl<Microsoft::WRL::RuntimeClassFlags<2>,1,0,1,IDXGISwapChain>::AddRef": {"offset": "0x363A0"}, "Microsoft::WRL::Details::RuntimeClassImpl<Microsoft::WRL::RuntimeClassFlags<2>,1,0,1,IDXGISwapChain>::QueryInterface": {"offset": "0x36300"}, "Microsoft::WRL::Details::RuntimeClassImpl<Microsoft::WRL::RuntimeClassFlags<2>,1,0,1,IDXGISwapChain>::Release": {"offset": "0x363D0"}, "Microsoft::WRL::RuntimeClass<Microsoft::WRL::RuntimeClassFlags<2>,IDXGISwapChain>::~RuntimeClass<Microsoft::WRL::RuntimeClassFlags<2>,IDXGISwapChain>": {"offset": "0x38B90"}, "NewWndProc": {"offset": "0x42A70"}, "PeekMessageWFake": {"offset": "0x42CE0"}, "PopDrawBlitImShader": {"offset": "0x23560"}, "ProcessThreadIPs": {"offset": "0x5F6D0"}, "ProtectedMemmove": {"offset": "0xFED0"}, "PushDrawBlitImShader": {"offset": "0x23BF0"}, "RagePresentWrap": {"offset": "0x3F110"}, "RaiseDebugException": {"offset": "0x5D150"}, "RemoveTextureOverride": {"offset": "0x3F450"}, "RenderBufferToBuffer": {"offset": "0x3F590"}, "RenderThreadKickStub": {"offset": "0x42EB0"}, "RenderThreadWaitStub": {"offset": "0x42F10"}, "ReverseGameData::ReverseGameData": {"offset": "0x38A40"}, "RootCheckPresented": {"offset": "0x291D0"}, "RootD3D11CreateDevice": {"offset": "0x29290"}, "RootSetPresented": {"offset": "0x29780"}, "ScanForReshades": {"offset": "0x34AC0"}, "ScopedError::~ScopedError": {"offset": "0xBC60"}, "SetBlendState": {"offset": "0x25C40"}, "SetDepthStencilState": {"offset": "0x25C60"}, "SetImDiffuseSamplerState": {"offset": "0x25C70"}, "SetRasterizerState": {"offset": "0x25CE0"}, "SetTextureGtaIm": {"offset": "0x25D20"}, "SetTextureHook": {"offset": "0x3FC40"}, "SetThreadName": {"offset": "0x5B300"}, "SetWindowLongPtrAHook": {"offset": "0x3FD70"}, "SetWorldMatrix": {"offset": "0x25D80"}, "SetupQueryHook": {"offset": "0x3FD90"}, "ShowCursorWrap": {"offset": "0x42F40"}, "ShowWindowWrap": {"offset": "0x430A0"}, "SysError": {"offset": "0xDFD0"}, "ToNarrow": {"offset": "0x5D260"}, "ToWide": {"offset": "0x5D350"}, "TraceRealV": {"offset": "0x5D660"}, "TranslateMessageFake": {"offset": "0xBA70"}, "UiDone": {"offset": "0x3FE00"}, "Unfreeze": {"offset": "0x5F880"}, "UninitializeBuffer": {"offset": "0x5FC50"}, "WaitForQueryHook": {"offset": "0x40090"}, "WaitableTimer::~WaitableTimer": {"offset": "0x38F60"}, "WakeWindowThreadFor": {"offset": "0x43190"}, "Win32TrapAndJump64": {"offset": "0x5ED80"}, "WrapCreateBackbuffer": {"offset": "0x40180"}, "WrapVideoModeChange": {"offset": "0x402B0"}, "_DllMainCRTStartup": {"offset": "0x61A60"}, "_Init_thread_abort": {"offset": "0x60C64"}, "_Init_thread_footer": {"offset": "0x60C94"}, "_Init_thread_header": {"offset": "0x60CF4"}, "_Init_thread_notify": {"offset": "0x60D5C"}, "_Init_thread_wait": {"offset": "0x60DA0"}, "_RTC_Initialize": {"offset": "0x6206C"}, "_RTC_Terminate": {"offset": "0x620A8"}, "__ArrayUnwind": {"offset": "0x61640"}, "__GSHandlerCheck": {"offset": "0x61380"}, "__GSHandlerCheckCommon": {"offset": "0x613A0"}, "__GSHandlerCheck_EH": {"offset": "0x613FC"}, "__GSHandlerCheck_SEH": {"offset": "0x614DC"}, "__chkstk": {"offset": "0x616F0"}, "__crt_debugger_hook": {"offset": "0x61DC8"}, "__dyn_tls_init": {"offset": "0x611C8"}, "__dyn_tls_on_demand_init": {"offset": "0x61230"}, "__isa_available_init": {"offset": "0x61C1C"}, "__local_stdio_printf_options": {"offset": "0xFA30"}, "__local_stdio_scanf_options": {"offset": "0x62040"}, "__raise_securityfailure": {"offset": "0x61AA0"}, "__report_gsfailure": {"offset": "0x61AD4"}, "__scrt_acquire_startup_lock": {"offset": "0x60E48"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x60E84"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x60EB8"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x60ED0"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x60EF8"}, "__scrt_dllmain_exception_filter": {"offset": "0x60F10"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x60F70"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x60FA0"}, "__scrt_fastfail": {"offset": "0x61DD0"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x62064"}, "__scrt_initialize_crt": {"offset": "0x60FB4"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x62048"}, "__scrt_initialize_onexit_tables": {"offset": "0x61000"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x60B6C"}, "__scrt_initialize_type_info": {"offset": "0x616A4"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x6108C"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x63F4C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x61F64"}, "__scrt_release_startup_lock": {"offset": "0x61124"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x10100"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x10100"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x10100"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x10100"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x10100"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0x10030"}, "__scrt_throw_std_bad_alloc": {"offset": "0x61F3C"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xE7D0"}, "__scrt_uninitialize_crt": {"offset": "0x61148"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x60C3C"}, "__scrt_uninitialize_type_info": {"offset": "0x616B4"}, "__security_check_cookie": {"offset": "0x61490"}, "__security_init_cookie": {"offset": "0x61F70"}, "__std_find_trivial_1": {"offset": "0x60850"}, "__std_find_trivial_2": {"offset": "0x60920"}, "__std_find_trivial_8": {"offset": "0x609F0"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x60B10"}, "_get_startup_argv_mode": {"offset": "0x3E370"}, "_guard_check_icall_nop": {"offset": "0xBA70"}, "_guard_dispatch_icall_nop": {"offset": "0x62250"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x62270"}, "_onexit": {"offset": "0x61174"}, "_wwassert": {"offset": "0x5B4F0"}, "`LimitFrameTime'::`2'::ReturnToken::~ReturnToken": {"offset": "0x38EF0"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x63FBC"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x64039"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x64050"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x64069"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x6407D"}, "`dynamic initializer for 'HostSharedData<ReverseGameData>::m_fakeData''": {"offset": "0x2190"}, "`dynamic initializer for 'OnD3DPostReset''": {"offset": "0x2A40"}, "`dynamic initializer for 'OnFlipModelHook''": {"offset": "0x21E0"}, "`dynamic initializer for 'OnGrcCreateDevice''": {"offset": "0x21F0"}, "`dynamic initializer for 'OnInternalScreenshot''": {"offset": "0x2200"}, "`dynamic initializer for 'OnPostFrontendRender''": {"offset": "0x2210"}, "`dynamic initializer for 'OnRequestInternalScreenshot''": {"offset": "0x2220"}, "`dynamic initializer for 'OnTryCreateSwapChain''": {"offset": "0x2230"}, "`dynamic initializer for '_clearRenderTarget''": {"offset": "0x2A50"}, "`dynamic initializer for '_createRenderTarget''": {"offset": "0x2A90"}, "`dynamic initializer for '_getResourceCache''": {"offset": "0x2AD0"}, "`dynamic initializer for '_grcBindTexture''": {"offset": "0x2240"}, "`dynamic initializer for '_grcLighting''": {"offset": "0x2280"}, "`dynamic initializer for '_grcWorldIdentity''": {"offset": "0x22C0"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_28''": {"offset": "0x2300"}, "`dynamic initializer for '_init_instance_29''": {"offset": "0x2330"}, "`dynamic initializer for '_init_instance_30''": {"offset": "0x2360"}, "`dynamic initializer for '_init_instance_31''": {"offset": "0x2390"}, "`dynamic initializer for '_init_instance_36''": {"offset": "0x1F40"}, "`dynamic initializer for '_init_instance_37''": {"offset": "0x1F70"}, "`dynamic initializer for '_init_instance_38''": {"offset": "0x1FA0"}, "`dynamic initializer for '_setWorldMatrix''": {"offset": "0x14B0"}, "`dynamic initializer for 'activateMatrix''": {"offset": "0x14F0"}, "`dynamic initializer for 'addImVertex''": {"offset": "0x1530"}, "`dynamic initializer for 'alignDrawCommandBuffer''": {"offset": "0x1570"}, "`dynamic initializer for 'allocDrawCommand''": {"offset": "0x15B0"}, "`dynamic initializer for 'beginImVertices''": {"offset": "0x15F0"}, "`dynamic initializer for 'createBlendState''": {"offset": "0x1630"}, "`dynamic initializer for 'createDepthStencilState''": {"offset": "0x1670"}, "`dynamic initializer for 'createRasterizerState''": {"offset": "0x16B0"}, "`dynamic initializer for 'createSamplerState''": {"offset": "0x16F0"}, "`dynamic initializer for 'drawImVertices''": {"offset": "0x1730"}, "`dynamic initializer for 'flushRenderStates''": {"offset": "0x23C0"}, "`dynamic initializer for 'g_curFn''": {"offset": "0x27C0"}, "`dynamic initializer for 'g_gameWindowEvent''": {"offset": "0x2400"}, "`dynamic initializer for 'g_mapScratchBuffer''": {"offset": "0x27B0"}, "`dynamic initializer for 'g_preRenderThreadKick''": {"offset": "0x27D0"}, "`dynamic initializer for 'g_queriesSetUp''": {"offset": "0x2430"}, "`dynamic initializer for 'g_resources''": {"offset": "0x24E0"}, "`dynamic initializer for 'g_textureOverrides''": {"offset": "0x24F0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'g_wndFuncQueue''": {"offset": "0x2800"}, "`dynamic initializer for 'g_wndMsgQueue''": {"offset": "0x2840"}, "`dynamic initializer for 'getSamplerState''": {"offset": "0x17B0"}, "`dynamic initializer for 'grcResourceCache_flushQueue''": {"offset": "0x2B10"}, "`dynamic initializer for 'grcResourceCache_getAndUpdateAvailableMemory''": {"offset": "0x2B50"}, "`dynamic initializer for 'grcResourceCache_queueDelete''": {"offset": "0x2B90"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x17F0"}, "`dynamic initializer for 'hookFunctionSafe''": {"offset": "0x25B0"}, "`dynamic initializer for 'initFunction''": {"offset": "0x11E0"}, "`dynamic initializer for 'initFunctionEarlyUMD''": {"offset": "0x25F0"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x2D90"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x2F60"}, "`dynamic initializer for 'mh''": {"offset": "0x1220"}, "`dynamic initializer for 'popImShaderAndResetParams''": {"offset": "0x19B0"}, "`dynamic initializer for 'popSubShader''": {"offset": "0x19F0"}, "`dynamic initializer for 'popSubShaderUnk''": {"offset": "0x1A30"}, "`dynamic initializer for 'setBlendState''": {"offset": "0x1A70"}, "`dynamic initializer for 'setDepthStencilState''": {"offset": "0x1AB0"}, "`dynamic initializer for 'setImGenParams''": {"offset": "0x1AF0"}, "`dynamic initializer for 'setRasterizerState''": {"offset": "0x1B30"}, "`dynamic initializer for 'setSamplerState''": {"offset": "0x1B70"}, "`dynamic initializer for 'setScreenSpaceMatrix''": {"offset": "0x1BB0"}, "`dynamic initializer for 'setSubShader''": {"offset": "0x1BF0"}, "`dynamic initializer for 'setSubShaderUnk''": {"offset": "0x1C30"}, "`dynamic initializer for 'setTextureGtaIm''": {"offset": "0x1C70"}, "`dynamic initializer for 'shaderIdHookFunc''": {"offset": "0x1CB0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,0,6>::ms_initFunction''": {"offset": "0x1270"}, "`dynamic initializer for 'xbr::virt::Base<33,2802,2,6>::ms_initFunction''": {"offset": "0x21A0"}, "`dynamic initializer for 'xbr::virt::Base<9,2802,0,6>::ms_initFunction''": {"offset": "0x1F00"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::RegIDMap": {"offset": "0x16BB0"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::~RegIDMap": {"offset": "0x17590"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x31910"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x31910"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x31930"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x31930"}, "atexit": {"offset": "0x611B0"}, "capture_previous_context": {"offset": "0x61BA8"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2A740"}, "console::Printf<>": {"offset": "0x2A990"}, "console::Printf<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2AA80"}, "console::Printfv": {"offset": "0x348F0"}, "dllmain_crt_dispatch": {"offset": "0x61740"}, "dllmain_crt_process_attach": {"offset": "0x61790"}, "dllmain_crt_process_detach": {"offset": "0x618A8"}, "dllmain_dispatch": {"offset": "0x6192C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xEE10"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xBB30"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x59870"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x58BA0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x35FD0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x31740"}, "fmt::v8::detail::add_compare": {"offset": "0x58FE0"}, "fmt::v8::detail::assert_fail": {"offset": "0x59120"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x59170"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x59340"}, "fmt::v8::detail::bigint::square": {"offset": "0x59C40"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x58BA0"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x4340"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x59F00"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x2B070"}, "fmt::v8::detail::compare": {"offset": "0x592A0"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B130"}, "fmt::v8::detail::count_digits": {"offset": "0xEBF0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x553D0"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x554E0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x43F0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x59740"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x57350"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x59A20"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x57380"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x58150"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x57D20"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x599A0"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x55590"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x55590"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x2B200"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x2B2C0"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x596D0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x4420"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x56A40"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x46F0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x56920"}, "fmt::v8::detail::format_float<double>": {"offset": "0x54EE0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x56B80"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x47D0"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2B340"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x56EE0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x48F0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x48F0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x4A50"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x2B4B0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x4CB0"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x2B730"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xF980"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x36250"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x57590"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x57810"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x57AA0"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x57C10"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xBB90"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xBB90"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x4D80"}, "fmt::v8::detail::utf8_decode": {"offset": "0xF7C0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x6380"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2CB80"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x7380"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x6F30"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x77C0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x588A0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x58780"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x6E60"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2D6A0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x2DBC0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x2D770"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x2E000"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2E440"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2E580"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x7C00"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2E6C0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x7C40"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2E7B0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x7DD0"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x2E960"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x8790"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x81A0"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2F490"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x2EE70"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xB720"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0xB720"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x8EC0"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x2FAB0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x92E0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x94B0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x9650"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x9650"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x2FF40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x97E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x9A00"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x9B80"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0xB310"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x9D10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x9F30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0xA1D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0xA3F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0xA570"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0xA790"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0xA9B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0xAB30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0xAD50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0xAED0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0xB0F0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x300C0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x30260"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x30400"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x30590"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x30730"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x308D0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x30A70"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x30C20"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x30DC0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xB4A0"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x30F60"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x31150"}, "fmt::v8::format_error::format_error": {"offset": "0xB920"}, "fmt::v8::format_error::~format_error": {"offset": "0xBCC0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x5C1B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x51F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2BD10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4FD0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2BAE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4EA0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2B9D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4DB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2B8A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x51F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2BD10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x50C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2BBE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5320"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2BE50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5E80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2C900"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x58B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2C3A0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x38150"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x5CD50"}, "fprintf": {"offset": "0xFA40"}, "fwEvent<>::ConnectInternal": {"offset": "0x1ED40"}, "fwEvent<>::callback::~callback": {"offset": "0x17620"}, "fwEvent<>::~fwEvent<>": {"offset": "0x38DA0"}, "fwEvent<bool *>::callback::~callback": {"offset": "0x17620"}, "fwEvent<bool *>::~fwEvent<bool *>": {"offset": "0x38DA0"}, "fwPlatformString::fwPlatformString": {"offset": "0x31670"}, "fwPlatformString::~fwPlatformString": {"offset": "0xBCE0"}, "fwRefCountable::AddRef": {"offset": "0x5E750"}, "fwRefCountable::Release": {"offset": "0x5E760"}, "fwRefCountable::~fwRefCountable": {"offset": "0x5E740"}, "grcTextureDtorHook": {"offset": "0x40EA0"}, "hde64_disasm": {"offset": "0x60090"}, "hook::AllocateFunctionStub": {"offset": "0x5DED0"}, "hook::TransformPattern": {"offset": "0x5E590"}, "hook::`anonymous namespace'::iat_matches_ordinal<char const *>": {"offset": "0x38110"}, "hook::call<long (__cdecl*)(IDXGIAdapter *,enum D3D_DRIVER_TYPE,HINSTANCE__ *,unsigned int,enum D3D_FEATURE_LEVEL const *,unsigned int,unsigned int,DXGI_SWAP_CHAIN_DESC const *,IDXGISwapChain * *,ID3D11Device * *,enum D3D_FEATURE_LEVEL *,ID3D11DeviceContext * *),void *>": {"offset": "0x37C60"}, "hook::details::StubInitFunction::Run": {"offset": "0x10810"}, "hook::get_pattern<bool __cdecl(VideoModeInfo *),42>": {"offset": "0x15C10"}, "hook::get_pattern<char,18>": {"offset": "0x15C10"}, "hook::get_pattern<char,24>": {"offset": "0x15C10"}, "hook::get_pattern<char,30>": {"offset": "0x15C10"}, "hook::get_pattern<char,38>": {"offset": "0x15C10"}, "hook::get_pattern<char,40>": {"offset": "0x15C10"}, "hook::get_pattern<char,50>": {"offset": "0x15C10"}, "hook::get_pattern<char,54>": {"offset": "0x15C10"}, "hook::get_pattern<void,18>": {"offset": "0x15C10"}, "hook::get_pattern<void,21>": {"offset": "0x15C10"}, "hook::get_pattern<void,24>": {"offset": "0x15C10"}, "hook::get_pattern<void,27>": {"offset": "0x15C10"}, "hook::get_pattern<void,29>": {"offset": "0x15C10"}, "hook::get_pattern<void,30>": {"offset": "0x15C10"}, "hook::get_pattern<void,31>": {"offset": "0x15C10"}, "hook::get_pattern<void,32>": {"offset": "0x15C10"}, "hook::get_pattern<void,33>": {"offset": "0x15C10"}, "hook::get_pattern<void,35>": {"offset": "0x15C10"}, "hook::get_pattern<void,36>": {"offset": "0x15C10"}, "hook::get_pattern<void,38>": {"offset": "0x15C10"}, "hook::get_pattern<void,39>": {"offset": "0x15C10"}, "hook::get_pattern<void,41>": {"offset": "0x15C10"}, "hook::get_pattern<void,42>": {"offset": "0x15C10"}, "hook::get_pattern<void,43>": {"offset": "0x15C10"}, "hook::get_pattern<void,44>": {"offset": "0x15C10"}, "hook::get_pattern<void,45>": {"offset": "0x15C10"}, "hook::get_pattern<void,46>": {"offset": "0x15C10"}, "hook::get_pattern<void,47>": {"offset": "0x15C10"}, "hook::get_pattern<void,48>": {"offset": "0x15C10"}, "hook::get_pattern<void,54>": {"offset": "0x15C10"}, "hook::get_pattern<void,59>": {"offset": "0x15C10"}, "hook::get_tls<char *>": {"offset": "0x15DF0"}, "hook::iat<HWND__ * (__cdecl*)(HWND__ *),char const *>": {"offset": "0x37FF0"}, "hook::module_pattern::~module_pattern": {"offset": "0xFB30"}, "hook::nop<char *>": {"offset": "0x15F60"}, "hook::nop<void *>": {"offset": "0x15F60"}, "hook::pattern::EnsureMatches": {"offset": "0x5DF30"}, "hook::pattern::Initialize": {"offset": "0x5E290"}, "hook::pattern::count": {"offset": "0x27950"}, "hook::pattern::~pattern": {"offset": "0xFB40"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x32A50"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x2A1F0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x32C80"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x31230"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0x337C0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0x33820"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0x34AB0"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x356C0"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0x359B0"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0x35AB0"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x32EE0"}, "jitasm::Backend::Assemble": {"offset": "0x1C600"}, "jitasm::Backend::Encode": {"offset": "0x1F4B0"}, "jitasm::Backend::EncodeALU": {"offset": "0x1F850"}, "jitasm::Backend::EncodeImm": {"offset": "0x1F940"}, "jitasm::Backend::EncodeJMP": {"offset": "0x1FAC0"}, "jitasm::Backend::EncodeModRM": {"offset": "0x1FCE0"}, "jitasm::Backend::EncodeOpcode": {"offset": "0x1FF60"}, "jitasm::Backend::EncodePrefixes": {"offset": "0x1FFF0"}, "jitasm::Backend::GetWRXB": {"offset": "0x20FF0"}, "jitasm::Backend::dd": {"offset": "0x279A0"}, "jitasm::Frontend::AppendInstr": {"offset": "0x1C4D0"}, "jitasm::Frontend::Assemble": {"offset": "0x1C900"}, "jitasm::Frontend::Label::~Label": {"offset": "0xBB90"}, "jitasm::Frontend::ResolveJump": {"offset": "0x23D70"}, "jitasm::Frontend::add": {"offset": "0x27420"}, "jitasm::Frontend::lea<jitasm::detail::OpdT<64> >": {"offset": "0x15E80"}, "jitasm::Frontend::mov": {"offset": "0x281D0"}, "jitasm::Frontend::movaps": {"offset": "0x283B0"}, "jitasm::Frontend::movss": {"offset": "0x28490"}, "jitasm::Frontend::pop": {"offset": "0x285D0"}, "jitasm::Frontend::push": {"offset": "0x286D0"}, "jitasm::Frontend::pxor": {"offset": "0x28950"}, "jitasm::Frontend::sub": {"offset": "0x28D20"}, "jitasm::Frontend::vxorps": {"offset": "0x28E70"}, "jitasm::Frontend::xorps": {"offset": "0x28F70"}, "jitasm::Frontend::~Frontend": {"offset": "0x17220"}, "jitasm::Instr::Instr": {"offset": "0x166E0"}, "jitasm::compiler::BasicBlock::BasicBlock": {"offset": "0x16650"}, "jitasm::compiler::BasicBlock::GetLifetime": {"offset": "0x20F10"}, "jitasm::compiler::BasicBlock::IsDominated": {"offset": "0x21080"}, "jitasm::compiler::Compile": {"offset": "0x1E960"}, "jitasm::compiler::ControlFlowGraph::Build": {"offset": "0x1D140"}, "jitasm::compiler::ControlFlowGraph::DetectLoops": {"offset": "0x1EF30"}, "jitasm::compiler::ControlFlowGraph::MakeDepthFirstBlocks": {"offset": "0x23470"}, "jitasm::compiler::ControlFlowGraph::clear": {"offset": "0x27820"}, "jitasm::compiler::ControlFlowGraph::get_block": {"offset": "0x27C40"}, "jitasm::compiler::ControlFlowGraph::initialize": {"offset": "0x27DF0"}, "jitasm::compiler::ControlFlowGraph::~ControlFlowGraph": {"offset": "0x170B0"}, "jitasm::compiler::DominatorFinder::Compress": {"offset": "0x1ECD0"}, "jitasm::compiler::DominatorFinder::~DominatorFinder": {"offset": "0x17120"}, "jitasm::compiler::GenerateEpilog": {"offset": "0x20450"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::GpRegOperator>": {"offset": "0x11420"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::MmxRegOperator>": {"offset": "0x11940"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::XmmRegOperator>": {"offset": "0x11FC0"}, "jitasm::compiler::GenerateProlog": {"offset": "0x209C0"}, "jitasm::compiler::Lifetime::AddUsePoint": {"offset": "0x1BFD0"}, "jitasm::compiler::Lifetime::AssignRegister": {"offset": "0x1CCB0"}, "jitasm::compiler::Lifetime::BuildIntervals": {"offset": "0x1DA90"}, "jitasm::compiler::Lifetime::Interval::Interval": {"offset": "0x16820"}, "jitasm::compiler::Lifetime::Interval::~Interval": {"offset": "0x172A0"}, "jitasm::compiler::Lifetime::LessAssignOrder::num_of_assignable": {"offset": "0x28570"}, "jitasm::compiler::Lifetime::Lifetime": {"offset": "0x168F0"}, "jitasm::compiler::Lifetime::SpillIdentification": {"offset": "0x25D90"}, "jitasm::compiler::Lifetime::~Lifetime": {"offset": "0x17430"}, "jitasm::compiler::LinearScanRegisterAlloc": {"offset": "0x210C0"}, "jitasm::compiler::LiveVariableAnalysis": {"offset": "0x21520"}, "jitasm::compiler::Operations::Operations": {"offset": "0x16AC0"}, "jitasm::compiler::PrepareCompile": {"offset": "0x235A0"}, "jitasm::compiler::RewriteInstructions": {"offset": "0x249C0"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::GpRegOperator> >": {"offset": "0x10CF0"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::MmxRegOperator> >": {"offset": "0x10FA0"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::XmmRegOperator> >": {"offset": "0x112E0"}, "jitasm::compiler::VariableManager::AllocSpillSlots": {"offset": "0x1C1F0"}, "jitasm::compiler::VariableManager::UpdateVarSize": {"offset": "0x266F0"}, "jitasm::compiler::VariableManager::~VariableManager": {"offset": "0x175F0"}, "jitasm::detail::CodeBuffer::Reset": {"offset": "0x23CC0"}, "jitasm::detail::CodeBuffer::~CodeBuffer": {"offset": "0x17070"}, "jitasm::detail::ScopedLock<jitasm::detail::SpinLock>::~ScopedLock<jitasm::detail::SpinLock>": {"offset": "0x16C10"}, "launch::GetLaunchModeKey": {"offset": "0x3E050"}, "launch::GetProductKey": {"offset": "0x3E0F0"}, "launch::IsSDKGuest": {"offset": "0x3EBA0"}, "rage::`dynamic initializer for '_grmShaderDef_GetParameter''": {"offset": "0x12B0"}, "rage::`dynamic initializer for '_grmShaderDef_GetTechnique''": {"offset": "0x12F0"}, "rage::`dynamic initializer for '_grmShaderDef_PopPass''": {"offset": "0x1330"}, "rage::`dynamic initializer for '_grmShaderDef_PushPass''": {"offset": "0x1370"}, "rage::`dynamic initializer for '_grmShaderDef_SetParameter''": {"offset": "0x13B0"}, "rage::`dynamic initializer for '_grmShaderDef_SetSampler''": {"offset": "0x13F0"}, "rage::`dynamic initializer for '_grmShaderFx_LoadTechnique''": {"offset": "0x1430"}, "rage::`dynamic initializer for '_grmShaderFx_PushTechnique''": {"offset": "0x1470"}, "rage::dlDrawCommandBuffer::AlignBuffer": {"offset": "0x1C1E0"}, "rage::dlDrawCommandBuffer::AllocateDrawCommand": {"offset": "0x1C4C0"}, "rage::dlDrawCommandBuffer::GetInstance": {"offset": "0x20EF0"}, "rage::grcBegin": {"offset": "0x27D40"}, "rage::grcEnd": {"offset": "0x27D50"}, "rage::grcResourceCache::FlushQueue": {"offset": "0x54E40"}, "rage::grcResourceCache::GetInstance": {"offset": "0x54E50"}, "rage::grcResourceCache::GetTotalPhysicalMemory": {"offset": "0x54E70"}, "rage::grcResourceCache::GetUsedPhysicalMemory": {"offset": "0x54EA0"}, "rage::grcResourceCache::QueueDelete": {"offset": "0x54EB0"}, "rage::grcResourceCache::_getAndUpdateAvailableMemory": {"offset": "0x54EC0"}, "rage::grcTexture::IsRenderSystemColorSwapped": {"offset": "0x10100"}, "rage::grcVertex": {"offset": "0x27D60"}, "rage::grmShaderDef::GetParameter": {"offset": "0x20F80"}, "rage::grmShaderDef::GetTechnique": {"offset": "0x20FD0"}, "rage::grmShaderDef::PopPass": {"offset": "0x23570"}, "rage::grmShaderDef::PushPass": {"offset": "0x23C90"}, "rage::grmShaderDef::SetParameter": {"offset": "0x25CA0"}, "rage::grmShaderDef::SetSampler": {"offset": "0x25CF0"}, "rage::grmShaderFx::GetParameter": {"offset": "0x20F90"}, "rage::grmShaderFx::GetTechnique": {"offset": "0x20FE0"}, "rage::grmShaderFx::LoadTechnique": {"offset": "0x23460"}, "rage::grmShaderFx::PopPass": {"offset": "0x23580"}, "rage::grmShaderFx::PopTechnique": {"offset": "0x23590"}, "rage::grmShaderFx::PushPass": {"offset": "0x23CA0"}, "rage::grmShaderFx::PushTechnique": {"offset": "0x23CB0"}, "rage::grmShaderFx::SetParameter": {"offset": "0x25CB0"}, "rage::grmShaderFx::SetSampler": {"offset": "0x25D00"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xB9A0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xBA40"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x32B0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xCB10"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xBA70"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xDC60"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xDD20"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xDF90"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xE430"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xBA80"}, "rapidjson::internal::DigitGen": {"offset": "0xCDC0"}, "rapidjson::internal::Grisu2": {"offset": "0xD870"}, "rapidjson::internal::Prettify": {"offset": "0xDDD0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x3D40"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x3E10"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xBA40"}, "rapidjson::internal::WriteExponent": {"offset": "0xE3A0"}, "rapidjson::internal::u32toa": {"offset": "0xEF00"}, "rapidjson::internal::u64toa": {"offset": "0xF170"}, "se::Object::~Object": {"offset": "0xBB90"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<rage::grcTexture * const,rage::grcTexture *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<rage::grcTexture * const,rage::grcTexture *>,void *> > >": {"offset": "0x38BA0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xBAB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x16C20"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xBAB0"}, "std::_Default_allocator_traits<std::allocator<jitasm::compiler::Lifetime::Interval> >::construct<jitasm::compiler::Lifetime::Interval,jitasm::compiler::Lifetime::Interval const &>": {"offset": "0x15940"}, "std::_Destroy_range<std::allocator<jitasm::compiler::Lifetime::Interval> >": {"offset": "0x12510"}, "std::_Facet_Register": {"offset": "0x60B1C"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x16C40"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x16C40"}, "std::_Func_class<void,__int64>::_Reset_move": {"offset": "0x431E0"}, "std::_Func_class<void,__int64>::~_Func_class<void,__int64>": {"offset": "0x16C40"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x16C40"}, "std::_Func_class<void>::_Reset_move": {"offset": "0x431E0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x16C40"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0x35AD0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0x35E00"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0x35AD0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0x35F90"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Copy": {"offset": "0x36CF0"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Do_call": {"offset": "0x36D10"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Move": {"offset": "0x36CF0"}, "std::_Func_impl_no_alloc<<lambda_44b797ceb096d645f9d316a603220f9c>,bool>::_Target_type": {"offset": "0x36D40"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Copy": {"offset": "0x410A0"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Do_call": {"offset": "0x410B0"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Move": {"offset": "0x410A0"}, "std::_Func_impl_no_alloc<<lambda_4b1b75654ad0c45a5c3ce0ab31d385d2>,void>::_Target_type": {"offset": "0x410D0"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Copy": {"offset": "0x36BA0"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Delete_this": {"offset": "0x36CE0"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Do_call": {"offset": "0x36C00"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Move": {"offset": "0x10030"}, "std::_Func_impl_no_alloc<<lambda_6f3d5776d9fae5481db55e3691617bf7>,void>::_Target_type": {"offset": "0x36CD0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x35AF0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x35C50"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x35EE0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x10030"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35FA0"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Copy": {"offset": "0x41050"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Delete_this": {"offset": "0x41090"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Do_call": {"offset": "0x41070"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Move": {"offset": "0x41050"}, "std::_Func_impl_no_alloc<<lambda_78683eb43e604dbd10dc94a2fe3c724a>,void>::_Target_type": {"offset": "0x41080"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Copy": {"offset": "0x414F0"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Do_call": {"offset": "0x41510"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Move": {"offset": "0x414F0"}, "std::_Func_impl_no_alloc<<lambda_7eb157a9acc084703ceb1e6a10abbcc3>,void,__int64>::_Target_type": {"offset": "0x41550"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x35B70"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x35C50"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x35F30"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x10030"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35FB0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0x35BF0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0x35CB0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0x35F80"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0x10030"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0x35FC0"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Copy": {"offset": "0x10830"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Do_call": {"offset": "0x10850"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Move": {"offset": "0x10830"}, "std::_Func_impl_no_alloc<<lambda_e51e092ca2165797cf98fc48afb5c311>,bool>::_Target_type": {"offset": "0x10870"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Copy": {"offset": "0x410E0"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Delete_this": {"offset": "0x10890"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Do_call": {"offset": "0x410F0"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Get": {"offset": "0x10880"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Move": {"offset": "0x410E0"}, "std::_Func_impl_no_alloc<<lambda_f559a89d0cc77b10c8b700cb4facb952>,void>::_Target_type": {"offset": "0x41110"}, "std::_Guess_median_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x13060"}, "std::_Hash<std::_Umap_traits<rage::grcTexture *,rage::grcTexture *,std::_Uhash_compare<rage::grcTexture *,std::hash<rage::grcTexture *>,std::equal_to<rage::grcTexture *> >,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> >,0> >::_Forced_rehash": {"offset": "0x408D0"}, "std::_Hash<std::_Umap_traits<rage::grcTexture *,rage::grcTexture *,std::_Uhash_compare<rage::grcTexture *,std::hash<rage::grcTexture *>,std::equal_to<rage::grcTexture *> >,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> >,0> >::_Try_emplace<rage::grcTexture * const &>": {"offset": "0x37960"}, "std::_Hash<std::_Umap_traits<rage::grcTexture *,rage::grcTexture *,std::_Uhash_compare<rage::grcTexture *,std::hash<rage::grcTexture *>,std::equal_to<rage::grcTexture *> >,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> >,0> >::~_Hash<std::_Umap_traits<rage::grcTexture *,rage::grcTexture *,std::_Uhash_compare<rage::grcTexture *,std::hash<rage::grcTexture *>,std::equal_to<rage::grcTexture *> >,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> >,0> >": {"offset": "0x38C90"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<rage::grcTexture * const,rage::grcTexture *> > > > > >::_Assign_grow": {"offset": "0x40440"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<rage::grcTexture * const,rage::grcTexture *> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<rage::grcTexture * const,rage::grcTexture *> > > > > >": {"offset": "0x16E10"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x38F70"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x2AC70"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<rage::grcTexture * const,rage::grcTexture *>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<rage::grcTexture * const,rage::grcTexture *>,void *> > >": {"offset": "0x38BA0"}, "std::_Make_heap_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x134D0"}, "std::_Maklocstr<char>": {"offset": "0xFA90"}, "std::_Maklocstr<wchar_t>": {"offset": "0x552C0"}, "std::_Med3_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x13650"}, "std::_Med3_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x13780"}, "std::_Med3_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x136F0"}, "std::_Move_unchecked<jitasm::Instr *,jitasm::Instr *>": {"offset": "0x13850"}, "std::_Partition_by_median_guess_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x13880"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x13E40"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x13BB0"}, "std::_Pop_heap_hole_by_index<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64>,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x141A0"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x143D0"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x142D0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0x10030"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0x35D30"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0x35D50"}, "std::_Sort_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x14DB0"}, "std::_Sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x14B90"}, "std::_Sort_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x15560"}, "std::_Sort_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x15270"}, "std::_Throw_bad_array_new_length": {"offset": "0xE7D0"}, "std::_Throw_bad_cast": {"offset": "0x58FC0"}, "std::_Throw_tree_length_error": {"offset": "0xE7F0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x58B60"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x58B60"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3FE0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x4260"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xBAD0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x37160"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xBAB0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x37450"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xE570"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x13000"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Insert_node": {"offset": "0xE570"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x3F80"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xE570"}, "std::_Uninitialized_move<jitasm::Instr *,std::allocator<jitasm::Instr> >": {"offset": "0x158A0"}, "std::_Xlen_string": {"offset": "0xE810"}, "std::allocator<Concurrency::details::_Split_order_list_node<std::pair<void * const,bool>,std::allocator<std::pair<void * const,bool> > >::_Node>::deallocate": {"offset": "0x40E50"}, "std::allocator<ID3D11Resource * *>::allocate": {"offset": "0x275E0"}, "std::allocator<ID3D11Resource * *>::deallocate": {"offset": "0x27A70"}, "std::allocator<char>::allocate": {"offset": "0xE830"}, "std::allocator<char>::deallocate": {"offset": "0x41010"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x275E0"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x27A70"}, "std::allocator<int>::allocate": {"offset": "0x27570"}, "std::allocator<int>::deallocate": {"offset": "0x27A20"}, "std::allocator<jitasm::Instr>::allocate": {"offset": "0x276C0"}, "std::allocator<jitasm::Instr>::deallocate": {"offset": "0x27B10"}, "std::allocator<jitasm::compiler::BasicBlock *>::allocate": {"offset": "0x275E0"}, "std::allocator<jitasm::compiler::BasicBlock *>::deallocate": {"offset": "0x27A70"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::allocate": {"offset": "0x27730"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::deallocate": {"offset": "0x27B60"}, "std::allocator<jitasm::compiler::OrderedLabel>::allocate": {"offset": "0x27650"}, "std::allocator<jitasm::compiler::OrderedLabel>::deallocate": {"offset": "0x27AC0"}, "std::allocator<jitasm::compiler::RegUsePoint>::deallocate": {"offset": "0x27AC0"}, "std::allocator<jitasm::compiler::VarAttribute>::deallocate": {"offset": "0x27BB0"}, "std::allocator<std::pair<unsigned __int64,unsigned __int64> >::deallocate": {"offset": "0x27AC0"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x275E0"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x27A70"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x41010"}, "std::allocator<unsigned int>::allocate": {"offset": "0x27570"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x27A20"}, "std::allocator<wchar_t>::allocate": {"offset": "0xE890"}, "std::allocator<wchar_t>::deallocate": {"offset": "0x5DB00"}, "std::bad_alloc::bad_alloc": {"offset": "0x61F1C"}, "std::bad_alloc::~bad_alloc": {"offset": "0xBCC0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xB8B0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xBCC0"}, "std::bad_cast::bad_cast": {"offset": "0x58B30"}, "std::bad_cast::~bad_cast": {"offset": "0xBCC0"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x31950"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x31990"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x3EC0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x5B850"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x2AEE0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x5B9C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x40DC0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xEA80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xB650"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xBB90"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x2ABA0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xBBF0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xE900"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x31670"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x5DB40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x5DCA0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xBBF0"}, "std::call_once<<lambda_0b34211bdb41de6480deccf071b8412e> >": {"offset": "0x37CE0"}, "std::deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >::~deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >": {"offset": "0x16CA0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Growmap": {"offset": "0x26CE0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Tidy": {"offset": "0x27160"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Xlen": {"offset": "0x273C0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x163F0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::~deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x16D70"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Growmap": {"offset": "0x26B00"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Tidy": {"offset": "0x270A0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Xlen": {"offset": "0x273C0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_back": {"offset": "0x287D0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_front": {"offset": "0x28890"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::~deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >": {"offset": "0x16C70"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x2B1C0"}, "std::exception::exception": {"offset": "0xB8E0"}, "std::exception::what": {"offset": "0xF960"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<rage::grcTexture * const,rage::grcTexture *> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<rage::grcTexture * const,rage::grcTexture *> > > > >": {"offset": "0x37F60"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x16C40"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x16C40"}, "std::function<void __cdecl(__int64)>::~function<void __cdecl(__int64)>": {"offset": "0x16C40"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x16C40"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x16C40"}, "std::get<0,<lambda_9fb9bc506750150637e38322cd6a6162> >": {"offset": "0x16C00"}, "std::invoke<<lambda_9fb9bc506750150637e38322cd6a6162> >": {"offset": "0x415C0"}, "std::list<std::pair<rage::grcTexture * const,rage::grcTexture *>,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> > >::~list<std::pair<rage::grcTexture * const,rage::grcTexture *>,std::allocator<std::pair<rage::grcTexture * const,rage::grcTexture *> > >": {"offset": "0x38E50"}, "std::locale::~locale": {"offset": "0x58C20"}, "std::move<<lambda_9fb9bc506750150637e38322cd6a6162> &>": {"offset": "0x16C00"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x59550"}, "std::numpunct<char>::do_falsename": {"offset": "0x59570"}, "std::numpunct<char>::do_grouping": {"offset": "0x595F0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x59630"}, "std::numpunct<char>::do_truename": {"offset": "0x59650"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x58940"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x58DD0"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x59560"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x595B0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x595F0"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x59640"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x59690"}, "std::rotate<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<jitasm::compiler::BasicBlock *> > > >": {"offset": "0x161E0"}, "std::runtime_error::runtime_error": {"offset": "0xB960"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x38EB0"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x317A0"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x317A0"}, "std::thread::_Invoke<std::tuple<<lambda_3cf43aa4044dcba2bfd51ecbef95a5bc> >,0>": {"offset": "0x37920"}, "std::thread::_Invoke<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,0>": {"offset": "0x41580"}, "std::thread::~thread": {"offset": "0x38FA0"}, "std::to_string": {"offset": "0x360F0"}, "std::tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >::~tuple<HWND__ *,unsigned int,unsigned __int64,__int64,std::function<void __cdecl(__int64)> >": {"offset": "0x16C40"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x317F0"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0x16DA0"}, "std::unique_ptr<fwEvent<bool *>::callback,std::default_delete<fwEvent<bool *>::callback> >::~unique_ptr<fwEvent<bool *>::callback,std::default_delete<fwEvent<bool *>::callback> >": {"offset": "0x16DA0"}, "std::unique_ptr<fwEvent<unsigned char const *,int,int>::callback,std::default_delete<fwEvent<unsigned char const *,int,int>::callback> >::~unique_ptr<fwEvent<unsigned char const *,int,int>::callback,std::default_delete<fwEvent<unsigned char const *,int,int>::callback> >": {"offset": "0x16DA0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x58C00"}, "std::unique_ptr<std::tuple<<lambda_3cf43aa4044dcba2bfd51ecbef95a5bc> >,std::default_delete<std::tuple<<lambda_3cf43aa4044dcba2bfd51ecbef95a5bc> > > >::~unique_ptr<std::tuple<<lambda_3cf43aa4044dcba2bfd51ecbef95a5bc> >,std::default_delete<std::tuple<<lambda_3cf43aa4044dcba2bfd51ecbef95a5bc> > > >": {"offset": "0x38B70"}, "std::unique_ptr<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > > >::get": {"offset": "0x43250"}, "std::unique_ptr<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > > >::unique_ptr<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > > ><std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > >,0>": {"offset": "0x41570"}, "std::unique_ptr<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > > >::~unique_ptr<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> >,std::default_delete<std::tuple<<lambda_9fb9bc506750150637e38322cd6a6162> > > >": {"offset": "0x416B0"}, "std::unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >::~unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >": {"offset": "0x38EE0"}, "std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> >::~unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> >": {"offset": "0x38EE0"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x58520"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x58690"}, "utf8::exception::exception": {"offset": "0x5CEA0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x5BC20"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x5C850"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x5CF30"}, "utf8::invalid_code_point::what": {"offset": "0x5DE70"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xBCC0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x5CFA0"}, "utf8::invalid_utf8::what": {"offset": "0x5DE80"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xBCC0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x5D000"}, "utf8::not_enough_room::what": {"offset": "0x5DE90"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xBCC0"}, "va<unsigned short,unsigned short>": {"offset": "0x2B820"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x5C330"}, "va_impl<wchar_t,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> > >": {"offset": "0x5C550"}, "vva": {"offset": "0x5DE50"}, "xbr::GetGameBuild": {"offset": "0x20D60"}, "xbr::GetReplaceExecutableInit": {"offset": "0x5E7E0"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x5EA00"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x2FB0"}}}