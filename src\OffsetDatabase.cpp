#include "OffsetDatabase.h"
#include <iostream>
#include <fstream>

OffsetDatabase::OffsetDatabase() {
}

OffsetDatabase::~OffsetDatabase() {
}

void OffsetDatabase::Initialize() {
    m_patterns.clear();
    m_patternMap.clear();
    AddDefaultPatterns();
}

const OffsetPattern* OffsetDatabase::GetPattern(const std::string& name) const {
    auto it = m_patternMap.find(name);
    if (it != m_patternMap.end()) {
        return &m_patterns[it->second];
    }
    return nullptr;
}

void OffsetDatabase::AddPattern(const OffsetPattern& pattern) {
    m_patternMap[pattern.name] = m_patterns.size();
    m_patterns.push_back(pattern);
}

void OffsetDatabase::RemovePattern(const std::string& name) {
    auto it = m_patternMap.find(name);
    if (it != m_patternMap.end()) {
        size_t index = it->second;
        m_patterns.erase(m_patterns.begin() + index);
        m_patternMap.erase(it);
        
        // Update indices in map
        for (auto& pair : m_patternMap) {
            if (pair.second > index) {
                pair.second--;
            }
        }
    }
}

void OffsetDatabase::AddDefaultPatterns() {
    AddPlayerPatterns();
    AddVehiclePatterns();
    AddWorldPatterns();
    AddWeaponPatterns();
    AddNetworkPatterns();
    AddMiscPatterns();
}

void OffsetDatabase::AddPlayerPatterns() {
    // Player-related offsets
    AddPattern({
        "LocalPlayer",
        "Pointer to local player entity",
        "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 ? 8B 81",
        "Player",
        3,
        true
    });
    
    AddPattern({
        "PlayerList",
        "Array of all players",
        "48 8B 0D ? ? ? ? E8 ? ? ? ? 48 8B D8 48 85 C0 74 ? 48 8B 48",
        "Player",
        3,
        true
    });
    
    AddPattern({
        "PlayerInfo",
        "Player info structure",
        "48 8B 05 ? ? ? ? 48 8B 14 D0 48 85 D2 74 ? 48 8B 42",
        "Player",
        3,
        true
    });
    
    AddPattern({
        "PlayerCoords",
        "Player coordinates offset",
        "F3 0F 10 83 ? ? ? ? F3 0F 11 44 24 ? F3 0F 10 8B",
        "Player",
        4,
        false
    });
    
    AddPattern({
        "PlayerHealth",
        "Player health offset",
        "F3 0F 10 81 ? ? ? ? F3 0F 11 44 24 ? 48 8B 81",
        "Player",
        4,
        false
    });
    
    AddPattern({
        "PlayerArmor",
        "Player armor offset",
        "F3 0F 10 89 ? ? ? ? F3 0F 11 4C 24 ? 48 8B 89",
        "Player",
        4,
        false
    });
}

void OffsetDatabase::AddVehiclePatterns() {
    // Vehicle-related offsets
    AddPattern({
        "VehiclePool",
        "Vehicle pool pointer",
        "48 8B 05 ? ? ? ? F3 0F 59 F6 48 8B 40 08 48 85 C0 74 ? F3 0F 10 78",
        "Vehicle",
        3,
        true
    });
    
    AddPattern({
        "VehicleCoords",
        "Vehicle coordinates offset",
        "F3 0F 10 83 ? ? ? ? F3 0F 11 45 ? F3 0F 10 8B ? ? ? ? F3 0F 11 4D",
        "Vehicle",
        4,
        false
    });
    
    AddPattern({
        "VehicleSpeed",
        "Vehicle speed offset",
        "F3 0F 10 81 ? ? ? ? F3 0F 11 44 24 ? F3 0F 10 89 ? ? ? ? F3 0F 11 4C 24",
        "Vehicle",
        4,
        false
    });
    
    AddPattern({
        "VehicleHealth",
        "Vehicle health offset",
        "F3 0F 10 89 ? ? ? ? F3 0F 11 4C 24 ? F3 0F 10 81 ? ? ? ? F3 0F 11 44 24",
        "Vehicle",
        4,
        false
    });
    
    AddPattern({
        "VehicleGravity",
        "Vehicle gravity offset",
        "F3 0F 10 83 ? ? ? ? F3 0F 11 44 24 ? F3 0F 10 8B ? ? ? ? F3 0F 11 4C 24 ? F3 0F 10 93",
        "Vehicle",
        4,
        false
    });
}

void OffsetDatabase::AddWorldPatterns() {
    // World-related offsets
    AddPattern({
        "WorldPtr",
        "Pointer to world/game state",
        "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 ? 8B 81 ? ? ? ?",
        "World",
        3,
        true
    });

    AddPattern({
        "GameState",
        "Current game state",
        "83 3D ? ? ? ? ? 75 ? 48 8B 0D ? ? ? ? 48 85 C9 74 ?",
        "World",
        2,
        true
    });

    AddPattern({
        "TimeScale",
        "Game time scale",
        "F3 0F 10 05 ? ? ? ? F3 0F 11 44 24 ? 48 8B 05",
        "World",
        4,
        true
    });

    AddPattern({
        "Weather",
        "Current weather",
        "8B 05 ? ? ? ? 89 44 24 ? 48 8B 05 ? ? ? ?",
        "World",
        2,
        true
    });
}

void OffsetDatabase::AddWeaponPatterns() {
    // Weapon-related offsets
    AddPattern({
        "WeaponManager",
        "Weapon manager pointer",
        "48 8B 0D ? ? ? ? 48 85 C9 74 ? E8 ? ? ? ? 48 8B D8",
        "Weapon",
        3,
        true
    });

    AddPattern({
        "CurrentWeapon",
        "Current weapon offset",
        "8B 81 ? ? ? ? 89 44 24 ? 48 8B 81 ? ? ? ?",
        "Weapon",
        2,
        false
    });

    AddPattern({
        "WeaponAmmo",
        "Weapon ammo offset",
        "8B 89 ? ? ? ? 89 4C 24 ? 8B 89 ? ? ? ?",
        "Weapon",
        2,
        false
    });

    AddPattern({
        "WeaponDamage",
        "Weapon damage multiplier",
        "F3 0F 10 81 ? ? ? ? F3 0F 11 44 24 ? F3 0F 10 89 ? ? ? ?",
        "Weapon",
        4,
        false
    });
}

void OffsetDatabase::AddNetworkPatterns() {
    // Network-related offsets
    AddPattern({
        "NetworkManager",
        "Network manager pointer",
        "48 8B 0D ? ? ? ? 48 85 C9 74 ? E8 ? ? ? ? 84 C0 74 ?",
        "Network",
        3,
        true
    });

    AddPattern({
        "SessionInfo",
        "Session information",
        "48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 48 ? 48 85 C9",
        "Network",
        3,
        true
    });

    AddPattern({
        "PlayerCount",
        "Current player count",
        "8B 05 ? ? ? ? 89 44 24 ? 48 8B 05 ? ? ? ? 48 85 C0",
        "Network",
        2,
        true
    });
}

void OffsetDatabase::AddMiscPatterns() {
    // Miscellaneous offsets
    AddPattern({
        "FrameCount",
        "Current frame count",
        "8B 05 ? ? ? ? FF C0 89 05 ? ? ? ? 48 8B 05",
        "Misc",
        2,
        true
    });

    AddPattern({
        "GameVersion",
        "Game version string",
        "48 8D 05 ? ? ? ? 48 89 44 24 ? 48 8D 05 ? ? ? ?",
        "Misc",
        3,
        true
    });

    AddPattern({
        "FPS",
        "Current FPS value",
        "F3 0F 10 05 ? ? ? ? F3 0F 11 44 24 ? F3 0F 10 05 ? ? ? ?",
        "Misc",
        4,
        true
    });

    AddPattern({
        "MenuState",
        "Current menu state",
        "83 3D ? ? ? ? ? 74 ? 48 8B 0D ? ? ? ?",
        "Misc",
        2,
        true
    });
}
