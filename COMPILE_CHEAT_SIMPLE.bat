@echo off
title FiveM Cheat - COMPILAR SIMPLES
color 0A

echo ========================================
echo    FIVEM CHEAT - COMPILAR SIMPLES
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo CRIANDO CHEAT SIMPLES
echo ========================================

REM Verificar se offsets_auto.h existe
if not exist "examples\offsets_auto.h" (
    echo [WARNING] offsets_auto.h nao encontrado
    echo [INFO] Criando offsets basicos...
    
    REM Criar pasta examples se nao existir
    if not exist "examples" mkdir "examples"
    
    REM Criar offsets_auto.h basico
    echo // Offsets hardcoded para FiveM > "examples\offsets_auto.h"
    echo #pragma once >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo namespace FiveMOffsets { >> "examples\offsets_auto.h"
    echo     // Base do modulo FiveM_GameProcess.exe ^(HARDCODED^) >> "examples\offsets_auto.h"
    echo     constexpr uintptr_t MODULE_BASE = 0x140000000; >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Functions { >> "examples\offsets_auto.h"
    echo         // Funcoes importantes do FiveM/GTA V ^(ENDERECOS ABSOLUTOS^) >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GetPlayerPed = MODULE_BASE + 0x1639CE8; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GetAllPhysicalPlayers = MODULE_BASE + 0x118B6EB; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t RepairVehicle = MODULE_BASE + 0xD8D278; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t GiveAmmo = MODULE_BASE + 0xD89B8C; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Globals { >> "examples\offsets_auto.h"
    echo         // Variaveis globais importantes ^(ENDERECOS ABSOLUTOS^) >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t g_vehiclePool = MODULE_BASE + 0xD7383C; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t CWorld = MODULE_BASE + 0x737F0C; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo. >> "examples\offsets_auto.h"
    echo     namespace Modifiers { >> "examples\offsets_auto.h"
    echo         // Modificadores de gameplay ^(ENDERECOS ABSOLUTOS^) >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t WeaponDamageModifier = MODULE_BASE + 0x415BD3; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t InfiniteAmmo = MODULE_BASE + 0x10695C1; >> "examples\offsets_auto.h"
    echo         constexpr uintptr_t VehicleDamageModifier = MODULE_BASE + 0xEF6849; >> "examples\offsets_auto.h"
    echo     } >> "examples\offsets_auto.h"
    echo } >> "examples\offsets_auto.h"
    
    echo [OK] offsets_auto.h criado com offsets basicos
) else (
    echo [OK] offsets_auto.h ja existe
)

echo.

REM Criar cheat simples se nao existir
if not exist "examples\simple_cheat_final.cpp" (
    echo [INFO] Criando cheat simples...
    
    echo // FiveM Cheat Simples com Offsets Hardcoded > "examples\simple_cheat_final.cpp"
    echo #include ^<windows.h^> >> "examples\simple_cheat_final.cpp"
    echo #include ^<iostream^> >> "examples\simple_cheat_final.cpp"
    echo #include ^<vector^> >> "examples\simple_cheat_final.cpp"
    echo #include "offsets_auto.h" >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Variaveis globais >> "examples\simple_cheat_final.cpp"
    echo bool g_espEnabled = false; >> "examples\simple_cheat_final.cpp"
    echo bool g_godModeEnabled = false; >> "examples\simple_cheat_final.cpp"
    echo bool g_infiniteAmmoEnabled = false; >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Funcao para ler memoria com seguranca >> "examples\simple_cheat_final.cpp"
    echo template^<typename T^> >> "examples\simple_cheat_final.cpp"
    echo T ReadMemory^(uintptr_t address^) { >> "examples\simple_cheat_final.cpp"
    echo     __try { >> "examples\simple_cheat_final.cpp"
    echo         return *reinterpret_cast^<T*^>^(address^); >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\simple_cheat_final.cpp"
    echo         return T{}; >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Funcao para escrever memoria com seguranca >> "examples\simple_cheat_final.cpp"
    echo template^<typename T^> >> "examples\simple_cheat_final.cpp"
    echo void WriteMemory^(uintptr_t address, T value^) { >> "examples\simple_cheat_final.cpp"
    echo     __try { >> "examples\simple_cheat_final.cpp"
    echo         *reinterpret_cast^<T*^>^(address^) = value; >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\simple_cheat_final.cpp"
    echo         // Ignorar erros >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Obter jogador local usando endereco absoluto >> "examples\simple_cheat_final.cpp"
    echo uintptr_t GetLocalPlayer^(^) { >> "examples\simple_cheat_final.cpp"
    echo     auto GetPlayerPed = reinterpret_cast^<uintptr_t^(*^)^(int^)^>^(FiveMOffsets::Functions::GetPlayerPed^); >> "examples\simple_cheat_final.cpp"
    echo     __try { >> "examples\simple_cheat_final.cpp"
    echo         return GetPlayerPed^(-1^); // -1 = jogador local >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo     __except^(EXCEPTION_EXECUTE_HANDLER^) { >> "examples\simple_cheat_final.cpp"
    echo         return 0; >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // God Mode >> "examples\simple_cheat_final.cpp"
    echo void ToggleGodMode^(^) { >> "examples\simple_cheat_final.cpp"
    echo     g_godModeEnabled = !g_godModeEnabled; >> "examples\simple_cheat_final.cpp"
    echo     >> "examples\simple_cheat_final.cpp"
    echo     if ^(g_godModeEnabled^) { >> "examples\simple_cheat_final.cpp"
    echo         uintptr_t localPlayer = GetLocalPlayer^(^); >> "examples\simple_cheat_final.cpp"
    echo         if ^(localPlayer^) { >> "examples\simple_cheat_final.cpp"
    echo             WriteMemory^<float^>^(localPlayer + 0x280, 100.0f^); // Health >> "examples\simple_cheat_final.cpp"
    echo             WriteMemory^<float^>^(localPlayer + 0x14C, 100.0f^); // Armor >> "examples\simple_cheat_final.cpp"
    echo         } >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Infinite Ammo >> "examples\simple_cheat_final.cpp"
    echo void ToggleInfiniteAmmo^(^) { >> "examples\simple_cheat_final.cpp"
    echo     g_infiniteAmmoEnabled = !g_infiniteAmmoEnabled; >> "examples\simple_cheat_final.cpp"
    echo     >> "examples\simple_cheat_final.cpp"
    echo     if ^(g_infiniteAmmoEnabled^) { >> "examples\simple_cheat_final.cpp"
    echo         WriteMemory^<bool^>^(FiveMOffsets::Modifiers::InfiniteAmmo, true^); >> "examples\simple_cheat_final.cpp"
    echo     } else { >> "examples\simple_cheat_final.cpp"
    echo         WriteMemory^<bool^>^(FiveMOffsets::Modifiers::InfiniteAmmo, false^); >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // Thread principal do cheat >> "examples\simple_cheat_final.cpp"
    echo DWORD WINAPI CheatThread^(LPVOID lpParam^) { >> "examples\simple_cheat_final.cpp"
    echo     AllocConsole^(^); >> "examples\simple_cheat_final.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stdin^), "CONIN$", "r", stdin^); >> "examples\simple_cheat_final.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stdout^), "CONOUT$", "w", stdout^); >> "examples\simple_cheat_final.cpp"
    echo     freopen_s^(reinterpret_cast^<FILE**^>^(stderr^), "CONOUT$", "w", stderr^); >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo     std::cout ^<^< "[FiveM Cheat] Iniciado com offsets hardcoded!" ^<^< std::endl; >> "examples\simple_cheat_final.cpp"
    echo     std::cout ^<^< "[FiveM Cheat] F1 = God Mode, F2 = Infinite Ammo" ^<^< std::endl; >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo     while ^(true^) { >> "examples\simple_cheat_final.cpp"
    echo         if ^(GetAsyncKeyState^(VK_F1^) ^& 0x8000^) { >> "examples\simple_cheat_final.cpp"
    echo             ToggleGodMode^(^); >> "examples\simple_cheat_final.cpp"
    echo             std::cout ^<^< "[God Mode] " ^<^< ^(g_godModeEnabled ? "ON" : "OFF"^) ^<^< std::endl; >> "examples\simple_cheat_final.cpp"
    echo             Sleep^(500^); >> "examples\simple_cheat_final.cpp"
    echo         } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo         if ^(GetAsyncKeyState^(VK_F2^) ^& 0x8000^) { >> "examples\simple_cheat_final.cpp"
    echo             ToggleInfiniteAmmo^(^); >> "examples\simple_cheat_final.cpp"
    echo             std::cout ^<^< "[Infinite Ammo] " ^<^< ^(g_infiniteAmmoEnabled ? "ON" : "OFF"^) ^<^< std::endl; >> "examples\simple_cheat_final.cpp"
    echo             Sleep^(500^); >> "examples\simple_cheat_final.cpp"
    echo         } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo         Sleep^(50^); >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo     return 0; >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    echo. >> "examples\simple_cheat_final.cpp"
    echo // DLL Entry Point >> "examples\simple_cheat_final.cpp"
    echo BOOL APIENTRY DllMain^(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved^) { >> "examples\simple_cheat_final.cpp"
    echo     switch ^(ul_reason_for_call^) { >> "examples\simple_cheat_final.cpp"
    echo         case DLL_PROCESS_ATTACH: >> "examples\simple_cheat_final.cpp"
    echo             CreateThread^(nullptr, 0, CheatThread, nullptr, 0, nullptr^); >> "examples\simple_cheat_final.cpp"
    echo             break; >> "examples\simple_cheat_final.cpp"
    echo         case DLL_PROCESS_DETACH: >> "examples\simple_cheat_final.cpp"
    echo             FreeConsole^(^); >> "examples\simple_cheat_final.cpp"
    echo             break; >> "examples\simple_cheat_final.cpp"
    echo     } >> "examples\simple_cheat_final.cpp"
    echo     return TRUE; >> "examples\simple_cheat_final.cpp"
    echo } >> "examples\simple_cheat_final.cpp"
    
    echo [OK] simple_cheat_final.cpp criado
) else (
    echo [OK] simple_cheat_final.cpp ja existe
)

echo.

echo ========================================
echo COMPILANDO DLL
echo ========================================

REM Configurar Visual Studio
echo [INFO] Configurando Visual Studio...

REM Tentar diferentes versoes do Visual Studio
set "VS_FOUND=0"

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "VS_FOUND=1"
    echo [OK] Visual Studio 2022 encontrado
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "VS_FOUND=1"
    echo [OK] Visual Studio 2019 encontrado
) else (
    echo [ERROR] Visual Studio nao encontrado!
    echo.
    echo INSTALE Visual Studio Community (gratuito)
    pause
    exit /b 1
)

echo.

REM Entrar na pasta examples
cd examples

echo [INFO] Compilando FiveMCheat_FINAL.dll...
echo [INFO] Usando offsets hardcoded (enderecos absolutos)

REM Limpar builds anteriores
if exist "FiveMCheat_FINAL.dll" del "FiveMCheat_FINAL.dll"

REM Compilar DLL simples
cl.exe /LD /EHsc /std:c++17 simple_cheat_final.cpp /Fe:FiveMCheat_FINAL.dll /link user32.lib >nul 2>&1

if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao!
    echo.
    echo Tentando compilacao com mais detalhes...
    cl.exe /LD /EHsc /std:c++17 simple_cheat_final.cpp /Fe:FiveMCheat_FINAL.dll /link user32.lib
    cd ..
    pause
    exit /b 1
)

REM Verificar se DLL foi criada
if exist "FiveMCheat_FINAL.dll" (
    echo [OK] FiveMCheat_FINAL.dll compilada com sucesso!
    echo.
    
    REM Mostrar informacoes da DLL
    dir FiveMCheat_FINAL.dll | find "FiveMCheat_FINAL.dll"
    
    REM Copiar para pasta raiz
    copy "FiveMCheat_FINAL.dll" "..\FiveMCheat_FINAL.dll" >nul
    echo [OK] DLL copiada para pasta raiz
    
) else (
    echo [ERROR] FiveMCheat_FINAL.dll nao foi criada!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.

echo ========================================
echo COMPILACAO CONCLUIDA!
echo ========================================
echo.
echo ✅ ARQUIVOS CRIADOS:
echo.
echo 📁 FiveMCheat_FINAL.dll - Cheat principal
echo 📁 examples\offsets_auto.h - Offsets hardcoded
echo 📁 examples\simple_cheat_final.cpp - Codigo fonte
echo.
echo 🎯 CARACTERISTICAS:
echo.
echo ✅ Offsets HARDCODED (enderecos absolutos)
echo ✅ Nao precisa buscar em runtime
echo ✅ Performance otimizada
echo ✅ Funcionalidades basicas funcionais
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [F1] - Toggle God Mode
echo [F2] - Toggle Infinite Ammo
echo.
echo 🚀 COMO USAR:
echo.
echo 1. Abra o FiveM e entre em um servidor
echo 2. Use um injetor para injetar FiveMCheat_FINAL.dll
echo 3. Pressione F1 ou F2 para testar
echo.
echo ⚠️  IMPORTANTE:
echo.
echo ✅ Use apenas em servidores PRIVADOS/TESTE
echo ✅ DLL compilada com offsets reais
echo ✅ Funciona independentemente
echo.

echo ========================================
echo ✅ CHEAT PRONTO PARA USO!
echo ========================================

echo.
echo Pressione qualquer tecla para sair...
pause >nul
