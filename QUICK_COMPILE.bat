@echo off
echo Compilando FiveM Cheat...

REM Ir para pasta examples
cd examples

REM Compilar usando o mesmo ambiente do projeto principal
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.41.34120\bin\Hostx64\x64\cl.exe" /LD /EHsc simple_cheat_final.cpp /Fe:FiveMCheat_FINAL.dll /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um" /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared" /I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.41.34120\include" /link /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\um\x64" /LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.41.34120\lib\x64" user32.lib

if exist "FiveMCheat_FINAL.dll" (
    echo [OK] DLL criada com sucesso!
    copy "FiveMCheat_FINAL.dll" "..\FiveMCheat_FINAL.dll"
    echo [OK] DLL copiada para pasta raiz
) else (
    echo [ERROR] Falha na compilacao
)

cd ..
pause
