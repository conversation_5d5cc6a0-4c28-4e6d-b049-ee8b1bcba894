/*
 * Exemplo Simples de Cheat para FiveM
 * Usa os offsets obtidos pelo FiveM Offset Dumper
 * 
 * Funcionalidades:
 * - ESP de jogadores
 * - God Mode
 * - Teleport
 * - Infinite Ammo
 * - Vehicle Repair
 */

#include <Windows.h>
#include <iostream>
#include <vector>
#include <cmath>
#include "imgui/imgui.h"
#include "imgui/backends/imgui_impl_win32.h"
#include "imgui/backends/imgui_impl_dx11.h"
#include <d3d11.h>

// Incluir os offsets exportados pelo dumper
#include "offsets.h"

struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    float Distance(const Vector3& other) const {
        float dx = x - other.x;
        float dy = y - other.y;
        float dz = z - other.z;
        return sqrt(dx*dx + dy*dy + dz*dz);
    }
};

struct PlayerInfo {
    Vector3 position;
    float health;
    float armor;
    bool isValid;
    int id;
};

class FiveMCheat {
private:
    uintptr_t moduleBase;
    bool initialized;
    
    // Configurações do cheat
    bool espEnabled = false;
    bool godModeEnabled = false;
    bool infiniteAmmoEnabled = false;
    bool showMenu = false;
    
    // ESP
    std::vector<PlayerInfo> players;
    
public:
    FiveMCheat() : initialized(false) {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
        if (moduleBase) {
            initialized = true;
            std::cout << "[FiveMCheat] Initialized with base: 0x" << std::hex << moduleBase << std::endl;
        }
    }
    
    bool IsInitialized() const { return initialized; }
    
    // Função para ler memória com segurança
    template<typename T>
    T ReadMemory(uintptr_t address) {
        __try {
            return *reinterpret_cast<T*>(address);
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            return T{};
        }
    }
    
    // Função para escrever memória com segurança
    template<typename T>
    bool WriteMemory(uintptr_t address, T value) {
        __try {
            *reinterpret_cast<T*>(address) = value;
            return true;
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    // Obter jogador local
    uintptr_t GetLocalPlayer() {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        return ReadMemory<uintptr_t>(localPlayerPtr);
    }
    
    // Obter posição do jogador local
    Vector3 GetLocalPlayerPosition() {
        uintptr_t localPlayer = GetLocalPlayer();
        if (!localPlayer) return Vector3();
        
        uintptr_t coordsPtr = localPlayer + FiveMOffsets::Player::PlayerCoords;
        return ReadMemory<Vector3>(coordsPtr);
    }
    
    // ESP - Obter todos os jogadores
    void UpdatePlayerList() {
        players.clear();
        
        uintptr_t playerListPtr = moduleBase + FiveMOffsets::Player::PlayerList;
        uintptr_t playerList = ReadMemory<uintptr_t>(playerListPtr);
        
        if (!playerList) return;
        
        for (int i = 0; i < 32; i++) {
            uintptr_t playerPtr = ReadMemory<uintptr_t>(playerList + (i * 8));
            if (!playerPtr) continue;
            
            PlayerInfo info;
            info.id = i;
            
            // Ler coordenadas
            uintptr_t coordsPtr = playerPtr + FiveMOffsets::Player::PlayerCoords;
            info.position = ReadMemory<Vector3>(coordsPtr);
            
            // Ler vida
            uintptr_t healthPtr = playerPtr + FiveMOffsets::Player::PlayerHealth;
            info.health = ReadMemory<float>(healthPtr);
            
            // Ler armadura
            uintptr_t armorPtr = playerPtr + FiveMOffsets::Player::PlayerArmor;
            info.armor = ReadMemory<float>(armorPtr);
            
            // Verificar se é válido
            info.isValid = (info.health > 0 && info.position.x != 0 && info.position.y != 0);
            
            if (info.isValid) {
                players.push_back(info);
            }
        }
    }
    
    // God Mode
    void SetGodMode(bool enabled) {
        uintptr_t localPlayer = GetLocalPlayer();
        if (!localPlayer) return;
        
        uintptr_t healthPtr = localPlayer + FiveMOffsets::Player::PlayerHealth;
        uintptr_t armorPtr = localPlayer + FiveMOffsets::Player::PlayerArmor;
        
        if (enabled) {
            WriteMemory<float>(healthPtr, 999999.0f);
            WriteMemory<float>(armorPtr, 999999.0f);
        }
        
        godModeEnabled = enabled;
    }
    
    // Teleport
    void TeleportTo(const Vector3& position) {
        uintptr_t localPlayer = GetLocalPlayer();
        if (!localPlayer) return;
        
        uintptr_t coordsPtr = localPlayer + FiveMOffsets::Player::PlayerCoords;
        WriteMemory<Vector3>(coordsPtr, position);
    }
    
    // Infinite Ammo
    void SetInfiniteAmmo(bool enabled) {
        uintptr_t weaponManagerPtr = moduleBase + FiveMOffsets::Weapon::WeaponManager;
        uintptr_t weaponManager = ReadMemory<uintptr_t>(weaponManagerPtr);
        
        if (!weaponManager) return;
        
        uintptr_t currentWeaponPtr = weaponManager + FiveMOffsets::Weapon::CurrentWeapon;
        uintptr_t currentWeapon = ReadMemory<uintptr_t>(currentWeaponPtr);
        
        if (currentWeapon && enabled) {
            uintptr_t ammoPtr = currentWeapon + FiveMOffsets::Weapon::WeaponAmmo;
            WriteMemory<int>(ammoPtr, 999999);
        }
        
        infiniteAmmoEnabled = enabled;
    }
    
    // Reparar veículo
    void RepairVehicle() {
        uintptr_t localPlayer = GetLocalPlayer();
        if (!localPlayer) return;
        
        // Assumindo offset do veículo no jogador (pode precisar ser ajustado)
        uintptr_t vehiclePtr = ReadMemory<uintptr_t>(localPlayer + 0xD30);
        
        if (vehiclePtr) {
            uintptr_t healthPtr = vehiclePtr + FiveMOffsets::Vehicle::VehicleHealth;
            WriteMemory<float>(healthPtr, 1000.0f);
        }
    }
    
    // Renderizar ESP
    void RenderESP() {
        if (!espEnabled) return;
        
        Vector3 localPos = GetLocalPlayerPosition();
        
        for (const auto& player : players) {
            if (!player.isValid) continue;
            
            float distance = localPos.Distance(player.position);
            
            // Converter coordenadas 3D para 2D (simplificado)
            // Em um cheat real, você usaria a matriz de view/projection
            ImVec2 screenPos = ImVec2(400 + player.position.x * 0.1f, 300 + player.position.y * 0.1f);
            
            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            
            // Desenhar caixa
            ImVec2 boxSize = ImVec2(30, 50);
            ImU32 color = IM_COL32(255, 0, 0, 255);
            
            if (player.health <= 0) {
                color = IM_COL32(128, 128, 128, 255); // Cinza para mortos
            }
            
            drawList->AddRect(
                ImVec2(screenPos.x - boxSize.x/2, screenPos.y - boxSize.y/2),
                ImVec2(screenPos.x + boxSize.x/2, screenPos.y + boxSize.y/2),
                color, 0.0f, 0, 2.0f
            );
            
            // Desenhar informações
            char info[256];
            sprintf_s(info, "ID: %d\nHP: %.0f\nArmor: %.0f\nDist: %.1fm", 
                     player.id, player.health, player.armor, distance);
            
            drawList->AddText(
                ImVec2(screenPos.x + boxSize.x/2 + 5, screenPos.y - boxSize.y/2),
                IM_COL32(255, 255, 255, 255),
                info
            );
        }
    }
    
    // Renderizar menu
    void RenderMenu() {
        if (!showMenu) return;
        
        if (ImGui::Begin("FiveM Cheat Menu", &showMenu)) {
            
            ImGui::Text("FiveM Simple Cheat v1.0");
            ImGui::Separator();
            
            // Player Hacks
            if (ImGui::CollapsingHeader("Player Hacks")) {
                if (ImGui::Checkbox("God Mode", &godModeEnabled)) {
                    SetGodMode(godModeEnabled);
                }
                
                if (ImGui::Button("Heal")) {
                    SetGodMode(false);
                    SetGodMode(true);
                }
                
                static float teleportPos[3] = {0, 0, 0};
                ImGui::InputFloat3("Teleport Position", teleportPos);
                if (ImGui::Button("Teleport")) {
                    TeleportTo(Vector3(teleportPos[0], teleportPos[1], teleportPos[2]));
                }
                
                Vector3 currentPos = GetLocalPlayerPosition();
                ImGui::Text("Current Position: (%.1f, %.1f, %.1f)", currentPos.x, currentPos.y, currentPos.z);
            }
            
            // Weapon Hacks
            if (ImGui::CollapsingHeader("Weapon Hacks")) {
                if (ImGui::Checkbox("Infinite Ammo", &infiniteAmmoEnabled)) {
                    SetInfiniteAmmo(infiniteAmmoEnabled);
                }
            }
            
            // Vehicle Hacks
            if (ImGui::CollapsingHeader("Vehicle Hacks")) {
                if (ImGui::Button("Repair Vehicle")) {
                    RepairVehicle();
                }
            }
            
            // ESP
            if (ImGui::CollapsingHeader("ESP")) {
                ImGui::Checkbox("Enable ESP", &espEnabled);
                
                if (ImGui::Button("Update Player List")) {
                    UpdatePlayerList();
                }
                
                ImGui::Text("Players Found: %d", (int)players.size());
                
                if (ImGui::BeginChild("PlayerList", ImVec2(0, 200))) {
                    for (const auto& player : players) {
                        if (player.isValid) {
                            ImGui::Text("Player %d: (%.1f, %.1f, %.1f) HP: %.1f Armor: %.1f", 
                                       player.id, player.position.x, player.position.y, 
                                       player.position.z, player.health, player.armor);
                        }
                    }
                }
                ImGui::EndChild();
            }
            
            ImGui::Separator();
            ImGui::Text("Controls:");
            ImGui::Text("INSERT - Toggle Menu");
            ImGui::Text("F1 - Toggle ESP");
            ImGui::Text("F2 - Toggle God Mode");
        }
        ImGui::End();
    }
    
    // Loop principal
    void Update() {
        // Controles de teclado
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            showMenu = !showMenu;
        }
        
        if (GetAsyncKeyState(VK_F1) & 1) {
            espEnabled = !espEnabled;
        }
        
        if (GetAsyncKeyState(VK_F2) & 1) {
            SetGodMode(!godModeEnabled);
        }
        
        // Atualizar lista de jogadores para ESP
        if (espEnabled) {
            static DWORD lastUpdate = 0;
            DWORD currentTime = GetTickCount();
            
            if (currentTime - lastUpdate > 1000) { // Atualizar a cada 1 segundo
                UpdatePlayerList();
                lastUpdate = currentTime;
            }
        }
        
        // Manter infinite ammo ativo
        if (infiniteAmmoEnabled) {
            SetInfiniteAmmo(true);
        }
        
        // Renderizar
        RenderMenu();
        RenderESP();
    }
};

// Instância global do cheat
FiveMCheat* g_cheat = nullptr;

// Thread principal do cheat
DWORD WINAPI CheatThread(LPVOID lpParam) {
    // Aguardar o jogo carregar
    Sleep(5000);
    
    g_cheat = new FiveMCheat();
    
    if (!g_cheat->IsInitialized()) {
        std::cout << "[FiveMCheat] Failed to initialize!" << std::endl;
        return 1;
    }
    
    std::cout << "[FiveMCheat] Cheat initialized successfully!" << std::endl;
    std::cout << "[FiveMCheat] Controls:" << std::endl;
    std::cout << "[FiveMCheat] INSERT - Toggle Menu" << std::endl;
    std::cout << "[FiveMCheat] F1 - Toggle ESP" << std::endl;
    std::cout << "[FiveMCheat] F2 - Toggle God Mode" << std::endl;
    
    // Loop principal
    while (true) {
        g_cheat->Update();
        Sleep(16); // ~60 FPS
    }
    
    return 0;
}

// Ponto de entrada da DLL
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, CheatThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        if (g_cheat) {
            delete g_cheat;
            g_cheat = nullptr;
        }
        break;
    }
    return TRUE;
}
