# 🎮 Exemplos de Cheats para FiveM

Esta pasta contém exemplos práticos de como usar os offsets obtidos pelo **FiveM Offset Dumper** para criar cheats funcionais.

## 📁 Arquivos Incluídos

- **`simple_cheat.cpp`** - Cheat completo com ESP, God Mode, Teleport, etc.
- **`offsets.h`** - Header com offsets de exemplo (DEVE ser atualizado!)
- **`CMakeLists.txt`** - Configuração de build
- **`build_cheat.bat`** - Script automático de compilação
- **`README.md`** - Este arquivo

## 🚀 Como Usar

### 1. Obter Offsets Reais
```bash
# Na pasta principal do projeto
.\build.bat                    # Compilar o offset dumper
.\FiveMOffsetDumper.dll       # Injetar no FiveM
# Usar a interface para escanear offsets
# Exportar para C++ Header
```

### 2. Atualizar Offsets
```bash
# Copiar o arquivo offsets.h gerado pelo dumper para examples/
copy offsets.h examples/offsets.h
```

### 3. Compilar o Cheat
```bash
cd examples
.\build_cheat.bat
```

### 4. Usar o Cheat
```bash
# Injetar FiveMSimpleCheat.dll no processo do FiveM
# Controles:
# INSERT - Abrir/fechar menu
# F1 - Toggle ESP
# F2 - Toggle God Mode
```

## 🎯 Funcionalidades do Cheat de Exemplo

### 👤 Player Hacks
- **God Mode** - Vida e armadura infinitas
- **Teleport** - Teletransporte para coordenadas específicas
- **Position Display** - Mostra posição atual do jogador

### 🔫 Weapon Hacks
- **Infinite Ammo** - Munição infinita
- **Damage Multiplier** - Multiplicador de dano (via offsets)

### 🚗 Vehicle Hacks
- **Repair Vehicle** - Reparar veículo instantaneamente
- **Speed Modification** - Modificar velocidade do veículo
- **Gravity Control** - Controlar gravidade do veículo

### 👁️ ESP (Extra Sensory Perception)
- **Player ESP** - Mostrar todos os jogadores na tela
- **Health/Armor Display** - Mostrar vida e armadura dos jogadores
- **Distance Calculation** - Calcular distância para outros jogadores
- **Real-time Updates** - Atualização em tempo real

### 🌍 World Hacks
- **Time Scale** - Modificar velocidade do tempo
- **Weather Control** - Controlar clima
- **Player Count** - Mostrar número de jogadores

## 🛠️ Estrutura do Código

### Classe Principal: `FiveMCheat`
```cpp
class FiveMCheat {
    // Funcionalidades principais
    void SetGodMode(bool enabled);
    void TeleportTo(const Vector3& position);
    void SetInfiniteAmmo(bool enabled);
    void RepairVehicle();
    
    // ESP
    void UpdatePlayerList();
    void RenderESP();
    
    // Interface
    void RenderMenu();
    void Update();
};
```

### Estruturas de Dados
```cpp
struct Vector3 {
    float x, y, z;
    float Distance(const Vector3& other) const;
};

struct PlayerInfo {
    Vector3 position;
    float health;
    float armor;
    bool isValid;
    int id;
};
```

## 🔧 Personalização

### Adicionar Novos Hacks
1. Adicione novos offsets no `OffsetDatabase.cpp` do dumper
2. Re-escaneie e exporte novos offsets
3. Implemente a funcionalidade no `simple_cheat.cpp`

### Modificar Interface
- O menu usa **Dear ImGui** para interface gráfica
- Fácil de personalizar e adicionar novos controles
- Suporte a temas e estilos customizados

### Adicionar Novos Controles
```cpp
// No método Update()
if (GetAsyncKeyState(VK_F3) & 1) {
    // Sua funcionalidade aqui
}
```

## ⚠️ Avisos Importantes

### 🚨 Segurança
- **SEMPRE** teste em servidores privados primeiro
- Use apenas para fins educacionais
- Respeite os termos de serviço do FiveM

### 🔄 Atualizações
- Offsets mudam com atualizações do FiveM
- Re-escaneie após cada update
- Mantenha backups dos offsets funcionais

### 🛡️ Anti-Cheat
- Alguns servidores têm anti-cheat
- Use com moderação para evitar detecção
- Implemente delays e randomização

## 🎨 Exemplos de Uso Avançado

### ESP Customizado
```cpp
// Adicionar filtros por distância
if (distance > maxESPDistance) continue;

// Cores diferentes por status
ImU32 color = player.health > 50 ? 
    IM_COL32(0, 255, 0, 255) :  // Verde para saudável
    IM_COL32(255, 0, 0, 255);   // Vermelho para ferido
```

### Aimbot Básico
```cpp
Vector3 FindClosestEnemy() {
    Vector3 localPos = GetLocalPlayerPosition();
    Vector3 closest = {0, 0, 0};
    float minDistance = 999999.0f;
    
    for (const auto& player : players) {
        float dist = localPos.Distance(player.position);
        if (dist < minDistance && dist > 1.0f) {
            minDistance = dist;
            closest = player.position;
        }
    }
    
    return closest;
}
```

### Triggerbot
```cpp
bool IsEnemyInCrosshair() {
    // Implementar detecção de inimigo na mira
    // Usar raycasting ou verificação de ângulo
    return false; // Placeholder
}
```

## 📚 Recursos Adicionais

### Documentação
- [Dear ImGui Documentation](https://github.com/ocornut/imgui)
- [Windows API Reference](https://docs.microsoft.com/en-us/windows/win32/api/)
- [FiveM Native Reference](https://docs.fivem.net/natives/)

### Ferramentas Úteis
- **Cheat Engine** - Para análise de memória
- **x64dbg** - Para debugging
- **Process Hacker** - Para injeção de DLL

### Comunidade
- Fóruns de desenvolvimento de cheats
- Discord servers de modding
- GitHub repositories com exemplos

## 🔍 Troubleshooting

### Problemas Comuns

**Cheat não funciona:**
- Verifique se os offsets estão atualizados
- Confirme que a DLL foi injetada corretamente
- Verifique se o FiveM está rodando como administrador

**Crashes:**
- Adicione mais verificações de ponteiros nulos
- Use try-catch em operações de memória
- Verifique se os offsets estão corretos

**ESP não aparece:**
- Verifique se a renderização está funcionando
- Confirme que os jogadores estão sendo detectados
- Teste com coordenadas fixas primeiro

**Menu não abre:**
- Verifique se a tecla INSERT está funcionando
- Confirme que o ImGui foi inicializado
- Teste com outras teclas

### Debug
```cpp
// Adicionar logs para debug
std::cout << "[DEBUG] LocalPlayer: 0x" << std::hex << localPlayer << std::endl;
std::cout << "[DEBUG] Position: " << pos.x << ", " << pos.y << ", " << pos.z << std::endl;
```

## 📝 Licença e Responsabilidade

Este código é fornecido apenas para fins educacionais. O uso deste código para trapacear em servidores online pode resultar em banimento. Use por sua própria conta e risco.

**Lembre-se**: Sempre jogue de forma justa e respeitosa! 🎮
