{"rage-input-five.dll": {"<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x12BD0"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x12BD0"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xA4C0"}, "ActivateKeyboardLayoutWrap": {"offset": "0x15290"}, "CfxState::CfxState": {"offset": "0x12A10"}, "ClipCursorWrap": {"offset": "0x15730"}, "Component::As": {"offset": "0xE480"}, "Component::IsA": {"offset": "0xE540"}, "Component::SetCommandLine": {"offset": "0xA3A0"}, "Component::SetUserData": {"offset": "0xE550"}, "ComponentInstance::DoGameLoad": {"offset": "0xE520"}, "ComponentInstance::Initialize": {"offset": "0xE530"}, "ComponentInstance::Shutdown": {"offset": "0xE550"}, "ConVar<bool>::ConVar<bool>": {"offset": "0x12160"}, "ConVar<bool>::~ConVar<bool>": {"offset": "0x12C00"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x16740"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0x10330"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0x10510"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x12D00"}, "ConsoleFlagsToString": {"offset": "0x15E90"}, "CoreGetComponentRegistry": {"offset": "0x161B0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x16240"}, "CreateComponent": {"offset": "0xE560"}, "CreateGlobalInputHandler": {"offset": "0xF370"}, "CreateVariableEntry<bool>": {"offset": "0x10C50"}, "DisableFocus": {"offset": "0x162D0"}, "DllMain": {"offset": "0x3A5AC"}, "DoNtRaiseException": {"offset": "0x36DF0"}, "DoPatchMouseScrollDelta": {"offset": "0x23720"}, "EnableFocus": {"offset": "0x162F0"}, "FatalErrorNoExceptRealV": {"offset": "0xBA70"}, "FatalErrorRealV": {"offset": "0xBAA0"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x21D0"}, "GetAbsoluteCitPath": {"offset": "0x35380"}, "GetGlobalInput": {"offset": "0x16460"}, "GlobalErrorHandler": {"offset": "0xBCE0"}, "GlobalInputHandler::~GlobalInputHandler": {"offset": "0xEDE0"}, "GlobalInputHandlerLocal::WndProc": {"offset": "0xF480"}, "HookFunction::Run": {"offset": "0x10090"}, "HookFunctionBase::Register": {"offset": "0x37B80"}, "HookFunctionBase::RunAll": {"offset": "0x37BA0"}, "HookShowCursor": {"offset": "0x16650"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x12570"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x34BA0"}, "HostSharedData<ReverseGameData>::HostSharedData<ReverseGameData>": {"offset": "0x127C0"}, "InitFunction::Run": {"offset": "0x38070"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x36BF0"}, "InitFunctionBase::Register": {"offset": "0x36F60"}, "InitFunctionBase::RunAll": {"offset": "0x36FB0"}, "InputHook::EnableSetCursorPos": {"offset": "0x16310"}, "InputHook::IsKeyDown": {"offset": "0x16670"}, "InputHook::IsMouseButtonDown": {"offset": "0x16730"}, "InputHook::SetControlBypasses": {"offset": "0x16AE0"}, "InputHook::SetGameMouseFocus": {"offset": "0x16C20"}, "InputHook::SetHostCursorEnabled": {"offset": "0x16D80"}, "InputHook::`dynamic initializer for 'DeprecatedOnWndProc''": {"offset": "0x1220"}, "InputHook::`dynamic initializer for 'QueryInputTarget''": {"offset": "0x1270"}, "InputHook::`dynamic initializer for 'QueryMayLockCursor''": {"offset": "0x1280"}, "Instance<ICoreGameInit>::Get": {"offset": "0x16320"}, "MakeRelativeCitPath": {"offset": "0xC380"}, "MapGameKey": {"offset": "0x37BD0"}, "MapScanCodeToKey": {"offset": "0x37F70"}, "RaiseDebugException": {"offset": "0x36ED0"}, "RecaptureLostDevices": {"offset": "0x169F0"}, "RegisterRawInputDevicesWrap": {"offset": "0x16A10"}, "Return0": {"offset": "0xE480"}, "ReverseGameData::ReverseGameData": {"offset": "0x12AD0"}, "ScopedError::~ScopedError": {"offset": "0xA590"}, "SetCursorPosWrap": {"offset": "0x16C00"}, "SetInputWrap": {"offset": "0x16DE0"}, "StringCchPrintfW": {"offset": "0x2FC40"}, "StringVPrintfWorkerW": {"offset": "0x2FC90"}, "SysError": {"offset": "0xC900"}, "ToNarrow": {"offset": "0x36FE0"}, "ToWide": {"offset": "0x370D0"}, "TraceRealV": {"offset": "0x373E0"}, "Win32TrapAndJump64": {"offset": "0x38EC0"}, "_DllMainCRTStartup": {"offset": "0x39F60"}, "_Init_thread_abort": {"offset": "0x391FC"}, "_Init_thread_footer": {"offset": "0x3922C"}, "_Init_thread_header": {"offset": "0x3928C"}, "_Init_thread_notify": {"offset": "0x392F4"}, "_Init_thread_wait": {"offset": "0x39338"}, "_RTC_Initialize": {"offset": "0x3A5F4"}, "_RTC_Terminate": {"offset": "0x3A630"}, "__ArrayUnwind": {"offset": "0x39B7C"}, "__GSHandlerCheck": {"offset": "0x39918"}, "__GSHandlerCheckCommon": {"offset": "0x39938"}, "__GSHandlerCheck_EH": {"offset": "0x39994"}, "__GSHandlerCheck_SEH": {"offset": "0x3A11C"}, "__chkstk": {"offset": "0x39BF0"}, "__crt_debugger_hook": {"offset": "0x3A350"}, "__dyn_tls_init": {"offset": "0x39760"}, "__dyn_tls_on_demand_init": {"offset": "0x397C8"}, "__isa_available_init": {"offset": "0x3A1A4"}, "__local_stdio_printf_options": {"offset": "0xE360"}, "__local_stdio_scanf_options": {"offset": "0x2FFC0"}, "__raise_securityfailure": {"offset": "0x39FA0"}, "__report_gsfailure": {"offset": "0x39FD4"}, "__scrt_acquire_startup_lock": {"offset": "0x393E0"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x3941C"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x39450"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x39468"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x39490"}, "__scrt_dllmain_exception_filter": {"offset": "0x394A8"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x39508"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x39538"}, "__scrt_fastfail": {"offset": "0x3A358"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x3A5EC"}, "__scrt_initialize_crt": {"offset": "0x3954C"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x3A5D0"}, "__scrt_initialize_onexit_tables": {"offset": "0x39598"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x39104"}, "__scrt_initialize_type_info": {"offset": "0x39A84"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x39624"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x3C01C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x3A4F4"}, "__scrt_release_startup_lock": {"offset": "0x396BC"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE550"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE550"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE550"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE550"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE550"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE480"}, "__scrt_throw_std_bad_alloc": {"offset": "0x3A4C4"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD100"}, "__scrt_uninitialize_crt": {"offset": "0x396E0"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x391D4"}, "__scrt_uninitialize_type_info": {"offset": "0x39A94"}, "__security_check_cookie": {"offset": "0x39A30"}, "__security_init_cookie": {"offset": "0x3A500"}, "__std_find_trivial_1": {"offset": "0x38EE0"}, "__std_find_trivial_8": {"offset": "0x38FB0"}, "_get_startup_argv_mode": {"offset": "0x3A4EC"}, "_guard_check_icall_nop": {"offset": "0xA3A0"}, "_guard_dispatch_icall_nop": {"offset": "0x3A780"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x3A7A0"}, "_onexit": {"offset": "0x3970C"}, "_wwassert": {"offset": "0x35680"}, "`DoPatchMouseScrollDelta'::`10'::<unnamed-type-patch>::InternalMain": {"offset": "0x26590"}, "`DoPatchMouseScrollDelta'::`11'::<unnamed-type-patch>::InternalMain": {"offset": "0x26680"}, "`DoPatchMouseScrollDelta'::`6'::<unnamed-type-patch>::InternalMain": {"offset": "0x25DD0"}, "`DoPatchMouseScrollDelta'::`7'::<unnamed-type-patch>::InternalMain": {"offset": "0x25EA0"}, "`DoPatchMouseScrollDelta'::`8'::<unnamed-type-patch>::InternalMain": {"offset": "0x26370"}, "`DoPatchMouseScrollDelta'::`9'::<unnamed-type-patch>::InternalMain": {"offset": "0x26480"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x3C08C"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x3C0EB"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x3C102"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x3C11B"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x3C12F"}, "`dynamic initializer for 'HostSharedData<ReverseGameData>::m_fakeData''": {"offset": "0x1210"}, "`dynamic initializer for 'ProcessWMChar''": {"offset": "0x1230"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1290"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x12C0"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x12F0"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x1320"}, "`dynamic initializer for 'g_controlBypasses''": {"offset": "0x1350"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1390"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x1710"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x16C0"}, "`dynamic initializer for 'setOffsetsHookFunction''": {"offset": "0x1550"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::RegIDMap": {"offset": "0x1E920"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::~RegIDMap": {"offset": "0x1F1F0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x12D90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x12D90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x12DB0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x12DB0"}, "atexit": {"offset": "0x39748"}, "capture_previous_context": {"offset": "0x3A0A8"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10F30"}, "console::Printfv": {"offset": "0x16830"}, "dllmain_crt_dispatch": {"offset": "0x39C40"}, "dllmain_crt_process_attach": {"offset": "0x39C90"}, "dllmain_crt_process_detach": {"offset": "0x39DA8"}, "dllmain_dispatch": {"offset": "0x39E2C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD740"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA460"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x342F0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x33930"}, "fmt::v8::detail::add_compare": {"offset": "0x33B00"}, "fmt::v8::detail::assert_fail": {"offset": "0x33C40"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x33C90"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x33E60"}, "fmt::v8::detail::bigint::square": {"offset": "0x346C0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x33930"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2C70"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x34980"}, "fmt::v8::detail::compare": {"offset": "0x33DC0"}, "fmt::v8::detail::count_digits": {"offset": "0xD520"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x30480"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2D20"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x341C0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x32350"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x344A0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x32380"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x33040"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x32C10"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x34420"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x30590"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x30590"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x34150"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2D50"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x31A40"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3020"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x31920"}, "fmt::v8::detail::format_float<double>": {"offset": "0x2FFD0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x31B80"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x3100"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x31EE0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3220"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x3380"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x35E0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE2B0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x32590"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x32810"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x32AA0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA4C0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x36B0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE0F0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4CB0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5CB0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5860"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x60F0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x336A0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x33580"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5790"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6530"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6570"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6700"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x70C0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6AD0"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA050"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x77F0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7C10"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7DE0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7F80"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7F80"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8110"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x8330"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x84B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9C40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8640"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8860"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8B00"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8D20"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8EA0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x90C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x92E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9460"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9680"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9800"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9A20"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9DD0"}, "fmt::v8::format_error::format_error": {"offset": "0xA250"}, "fmt::v8::format_error::~format_error": {"offset": "0xA5F0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x36220"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B20"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3900"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x37D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x36E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B20"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x39F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x47B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x41E0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x12040"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x36AC0"}, "fprintf": {"offset": "0xE370"}, "fwEvent<>::ConnectInternal": {"offset": "0x15CC0"}, "fwEvent<int &>::ConnectInternal": {"offset": "0x15CC0"}, "fwEvent<tagRAWMOUSE const &>::ConnectInternal": {"offset": "0x15CC0"}, "fwEvent<tagRAWMOUSE const &>::callback::~callback": {"offset": "0xEE00"}, "fwEvent<tagRAWMOUSE const &>::~fwEvent<tagRAWMOUSE const &>": {"offset": "0xEC40"}, "fwEvent<unsigned long,bool>::ConnectInternal": {"offset": "0x15CC0"}, "fwEvent<unsigned long,bool>::callback::~callback": {"offset": "0xEE00"}, "fwEvent<unsigned long,bool>::~fwEvent<unsigned long,bool>": {"offset": "0xEC40"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA610"}, "fwRefCountable::AddRef": {"offset": "0x37B40"}, "fwRefCountable::Release": {"offset": "0x37B50"}, "fwRefCountable::~fwRefCountable": {"offset": "0x37B30"}, "grcWindowProcedure": {"offset": "0x18420"}, "hook::AllocateFunctionStub": {"offset": "0x380B0"}, "hook::TransformPattern": {"offset": "0x38770"}, "hook::`anonymous namespace'::iat_matches_ordinal<char const *>": {"offset": "0x11DF0"}, "hook::details::StubInitFunction::Run": {"offset": "0xFB40"}, "hook::get_address<int *,void *>": {"offset": "0x11BD0"}, "hook::get_pattern<char,21>": {"offset": "0x11C10"}, "hook::get_pattern<char,24>": {"offset": "0x11C10"}, "hook::get_pattern<char,26>": {"offset": "0x11C10"}, "hook::get_pattern<char,30>": {"offset": "0x11C10"}, "hook::get_pattern<char,36>": {"offset": "0x11C10"}, "hook::get_pattern<void,21>": {"offset": "0x11C10"}, "hook::get_pattern<void,24>": {"offset": "0x11C10"}, "hook::get_pattern<void,29>": {"offset": "0x11C10"}, "hook::get_pattern<void,31>": {"offset": "0x11C10"}, "hook::get_pattern<void,32>": {"offset": "0x11C10"}, "hook::get_pattern<void,34>": {"offset": "0x11C10"}, "hook::get_pattern<void,36>": {"offset": "0x11C10"}, "hook::get_pattern<void,37>": {"offset": "0x11C10"}, "hook::get_pattern<void,40>": {"offset": "0x11C10"}, "hook::get_pattern<void,43>": {"offset": "0x11C10"}, "hook::get_pattern<void,45>": {"offset": "0x11C10"}, "hook::get_pattern<void,47>": {"offset": "0x11C10"}, "hook::get_pattern<void,49>": {"offset": "0x11C10"}, "hook::get_pattern<void,52>": {"offset": "0x11C10"}, "hook::get_pattern<void,56>": {"offset": "0x11C10"}, "hook::get_pattern<void,65>": {"offset": "0x11C10"}, "hook::jump<void *,char *>": {"offset": "0x1DC50"}, "hook::nop<char *>": {"offset": "0x11FC0"}, "hook::pattern::EnsureMatches": {"offset": "0x38110"}, "hook::pattern::Initialize": {"offset": "0x38470"}, "hook::pattern::count": {"offset": "0x183D0"}, "hook::pattern::~pattern": {"offset": "0x12E10"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x152A0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x109E0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x154D0"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x122F0"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0xFF00"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0xFD80"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0xFF60"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x17910"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0xFE00"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0xFF70"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x15820"}, "jitasm::Backend::Assemble": {"offset": "0x20930"}, "jitasm::Backend::Encode": {"offset": "0x23F40"}, "jitasm::Backend::EncodeALU": {"offset": "0x243B0"}, "jitasm::Backend::EncodeImm": {"offset": "0x24520"}, "jitasm::Backend::EncodeJMP": {"offset": "0x246D0"}, "jitasm::Backend::EncodeModRM": {"offset": "0x24920"}, "jitasm::Backend::EncodeOpcode": {"offset": "0x24ED0"}, "jitasm::Backend::EncodePrefixes": {"offset": "0x24F80"}, "jitasm::Backend::GetWRXB": {"offset": "0x25D00"}, "jitasm::Backend::db": {"offset": "0x2CBC0"}, "jitasm::Backend::dd": {"offset": "0x2CC30"}, "jitasm::Frontend::AppendInstr": {"offset": "0x20800"}, "jitasm::Frontend::Assemble": {"offset": "0x20D30"}, "jitasm::Frontend::Frontend": {"offset": "0x1E380"}, "jitasm::Frontend::Label::~Label": {"offset": "0xA4C0"}, "jitasm::Frontend::ResolveJump": {"offset": "0x292E0"}, "jitasm::Frontend::cvtdq2ps": {"offset": "0x2CAE0"}, "jitasm::Frontend::jmp": {"offset": "0x2D1A0"}, "jitasm::Frontend::mov": {"offset": "0x2D500"}, "jitasm::Frontend::movaps": {"offset": "0x2D770"}, "jitasm::Frontend::mulps": {"offset": "0x2D850"}, "jitasm::Frontend::pop": {"offset": "0x2D990"}, "jitasm::Frontend::push": {"offset": "0x2DA90"}, "jitasm::Frontend::pxor": {"offset": "0x2DD10"}, "jitasm::Frontend::ret": {"offset": "0x2E050"}, "jitasm::Frontend::vxorps": {"offset": "0x2E1B0"}, "jitasm::Frontend::xorps": {"offset": "0x2E2B0"}, "jitasm::Frontend::~Frontend": {"offset": "0x1EE80"}, "jitasm::Instr::Instr": {"offset": "0x1E450"}, "jitasm::compiler::BasicBlock::BasicBlock": {"offset": "0x1E2F0"}, "jitasm::compiler::BasicBlock::IsDominated": {"offset": "0x267B0"}, "jitasm::compiler::Compile": {"offset": "0x23000"}, "jitasm::compiler::ControlFlowGraph::Build": {"offset": "0x21650"}, "jitasm::compiler::ControlFlowGraph::DetectLoops": {"offset": "0x233E0"}, "jitasm::compiler::ControlFlowGraph::MakeDepthFirstBlocks": {"offset": "0x28A40"}, "jitasm::compiler::ControlFlowGraph::clear": {"offset": "0x2C9B0"}, "jitasm::compiler::ControlFlowGraph::get_block": {"offset": "0x2CE80"}, "jitasm::compiler::ControlFlowGraph::initialize": {"offset": "0x2CF80"}, "jitasm::compiler::ControlFlowGraph::~ControlFlowGraph": {"offset": "0x1ED10"}, "jitasm::compiler::DominatorFinder::Compress": {"offset": "0x23370"}, "jitasm::compiler::DominatorFinder::~DominatorFinder": {"offset": "0x1ED80"}, "jitasm::compiler::GenerateEpilog": {"offset": "0x25290"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::GpRegOperator>": {"offset": "0x197E0"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::MmxRegOperator>": {"offset": "0x19D30"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::XmmRegOperator>": {"offset": "0x1A190"}, "jitasm::compiler::GenerateProlog": {"offset": "0x25700"}, "jitasm::compiler::GetRegFamily": {"offset": "0x25C70"}, "jitasm::compiler::Lifetime::AddUsePoint": {"offset": "0x20320"}, "jitasm::compiler::Lifetime::AssignRegister": {"offset": "0x210E0"}, "jitasm::compiler::Lifetime::BuildIntervals": {"offset": "0x220C0"}, "jitasm::compiler::Lifetime::Interval::Interval": {"offset": "0x1E590"}, "jitasm::compiler::Lifetime::Interval::~Interval": {"offset": "0x1EF00"}, "jitasm::compiler::Lifetime::LessAssignOrder::num_of_assignable": {"offset": "0x2D930"}, "jitasm::compiler::Lifetime::Lifetime": {"offset": "0x1E660"}, "jitasm::compiler::Lifetime::SpillIdentification": {"offset": "0x2B0D0"}, "jitasm::compiler::Lifetime::~Lifetime": {"offset": "0x1F090"}, "jitasm::compiler::LinearScanRegisterAlloc": {"offset": "0x267D0"}, "jitasm::compiler::LiveVariableAnalysis": {"offset": "0x26C30"}, "jitasm::compiler::Operations::Operations": {"offset": "0x1E830"}, "jitasm::compiler::PrepareCompile": {"offset": "0x28B30"}, "jitasm::compiler::RewriteInstructions": {"offset": "0x29D80"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::GpRegOperator> >": {"offset": "0x19270"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::MmxRegOperator> >": {"offset": "0x19560"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::XmmRegOperator> >": {"offset": "0x196A0"}, "jitasm::compiler::VariableManager::AllocSpillSlots": {"offset": "0x20530"}, "jitasm::compiler::VariableManager::UpdateVarSize": {"offset": "0x2BA30"}, "jitasm::compiler::VariableManager::~VariableManager": {"offset": "0x1F250"}, "jitasm::detail::CodeBuffer::Reset": {"offset": "0x29210"}, "jitasm::detail::CodeBuffer::~CodeBuffer": {"offset": "0x1ECD0"}, "jitasm::detail::Opd::GetDisp": {"offset": "0x25BF0"}, "jitasm::detail::Opd::GetImm": {"offset": "0x25C30"}, "jitasm::detail::ScopedLock<jitasm::detail::SpinLock>::~ScopedLock<jitasm::detail::SpinLock>": {"offset": "0x1E970"}, "launch::GetLaunchModeKey": {"offset": "0x16510"}, "launch::GetProductKey": {"offset": "0x165B0"}, "launch::IsSDKGuest": {"offset": "0xF400"}, "rage__ioPad__Update": {"offset": "0x18C90"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA2D0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA370"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1BE0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB440"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA3A0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC590"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC650"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC8C0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xCD60"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA3B0"}, "rapidjson::internal::DigitGen": {"offset": "0xB6F0"}, "rapidjson::internal::Grisu2": {"offset": "0xC1A0"}, "rapidjson::internal::Prettify": {"offset": "0xC700"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2670"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2740"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA370"}, "rapidjson::internal::WriteExponent": {"offset": "0xCCD0"}, "rapidjson::internal::u32toa": {"offset": "0xD830"}, "rapidjson::internal::u64toa": {"offset": "0xDAA0"}, "se::Object::~Object": {"offset": "0xA4C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA3E0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<HWND__ * const,std::weak_ptr<GlobalInputHandlerLocal> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<HWND__ * const,std::weak_ptr<GlobalInputHandlerLocal> >,void *> > >": {"offset": "0xEC00"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x1E980"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA3E0"}, "std::_Default_allocator_traits<std::allocator<jitasm::compiler::Lifetime::Interval> >::construct<jitasm::compiler::Lifetime::Interval,jitasm::compiler::Lifetime::Interval const &>": {"offset": "0x1D980"}, "std::_Destroy_range<std::allocator<jitasm::compiler::Lifetime::Interval> >": {"offset": "0x1A710"}, "std::_Facet_Register": {"offset": "0x390B4"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x12BD0"}, "std::_Func_class<bool,int &>::~_Func_class<bool,int &>": {"offset": "0x12BD0"}, "std::_Func_class<bool,tagRAWMOUSE const &>::~_Func_class<bool,tagRAWMOUSE const &>": {"offset": "0x12BD0"}, "std::_Func_class<bool,unsigned long,bool>::~_Func_class<bool,unsigned long,bool>": {"offset": "0x12BD0"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x12BD0"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x12BD0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x12BD0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0x100A0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0x100C0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0x100A0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0x101A0"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Copy": {"offset": "0xFAF0"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Delete_this": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Do_call": {"offset": "0xFB10"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Move": {"offset": "0xFAF0"}, "std::_Func_impl_no_alloc<<lambda_2940b78bf77630c56f0f1647b4d7bff7>,bool,int &>::_Target_type": {"offset": "0xFB30"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Copy": {"offset": "0xFA80"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Delete_this": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Do_call": {"offset": "0xFAA0"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Move": {"offset": "0xFA80"}, "std::_Func_impl_no_alloc<<lambda_431de91ff99d87d0114bb7812c1e48f9>,bool>::_Target_type": {"offset": "0xFAC0"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Copy": {"offset": "0x17CA0"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Delete_this": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Do_call": {"offset": "0x17D30"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Move": {"offset": "0x17CA0"}, "std::_Func_impl_no_alloc<<lambda_6852ac7a91dd4d8e4814f4b46a2c0469>,bool,tagRAWMOUSE const &>::_Target_type": {"offset": "0x183B0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xFCA0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFC40"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xFD20"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE480"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xFD70"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xFB60"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFC40"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xFBE0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE480"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xFC30"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Copy": {"offset": "0x17CC0"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Delete_this": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Do_call": {"offset": "0x17D60"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Move": {"offset": "0x17CC0"}, "std::_Func_impl_no_alloc<<lambda_85efb90715f16001ea46084abe045efe>,bool,unsigned long,bool>::_Target_type": {"offset": "0x183C0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0xFF90"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0x10010"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0xFFF0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xE480"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0x10000"}, "std::_Guess_median_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x1B0A0"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x11790"}, "std::_Make_heap_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x1B510"}, "std::_Maklocstr<char>": {"offset": "0xE3C0"}, "std::_Med3_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x1B690"}, "std::_Med3_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x1B7C0"}, "std::_Med3_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x1B730"}, "std::_Move_unchecked<jitasm::Instr *,jitasm::Instr *>": {"offset": "0x1B890"}, "std::_Partition_by_median_guess_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x1B8C0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x1BE80"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x1BBF0"}, "std::_Pop_heap_hole_by_index<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64>,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x1C1E0"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x1C410"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x1C310"}, "std::_Ref_count_base::_Decref": {"offset": "0x17CE0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE480"}, "std::_Ref_count_obj2<ConVar<bool> >::_Delete_this": {"offset": "0xF9B0"}, "std::_Ref_count_obj2<ConVar<bool> >::_Destroy": {"offset": "0x101B0"}, "std::_Ref_count_obj2<GlobalInputHandlerLocal>::_Delete_this": {"offset": "0xF9B0"}, "std::_Ref_count_obj2<GlobalInputHandlerLocal>::_Destroy": {"offset": "0xF9D0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0xF9B0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0x10220"}, "std::_Sort_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x1CDF0"}, "std::_Sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x1CBD0"}, "std::_Sort_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x1D5A0"}, "std::_Sort_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x1D2B0"}, "std::_Throw_bad_array_new_length": {"offset": "0xD100"}, "std::_Throw_bad_weak_ptr": {"offset": "0xF9E0"}, "std::_Throw_tree_length_error": {"offset": "0xD120"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x338F0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2910"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2B90"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA400"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x11340"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Erase": {"offset": "0x17D80"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x11650"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x11720"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA3E0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x11590"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x17F20"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xCEA0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x182F0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x18350"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<HWND__ * const,std::weak_ptr<GlobalInputHandlerLocal> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<HWND__ * const,std::weak_ptr<GlobalInputHandlerLocal> >,void *> > >": {"offset": "0xE770"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<HWND__ * const,std::weak_ptr<GlobalInputHandlerLocal> > > >::_Insert_node": {"offset": "0xCEA0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x1B040"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Insert_node": {"offset": "0xCEA0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x28B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCEA0"}, "std::_Uninitialized_move<jitasm::Instr *,std::allocator<jitasm::Instr> >": {"offset": "0x1D8E0"}, "std::_Xlen_string": {"offset": "0xD140"}, "std::allocator<char>::allocate": {"offset": "0xD160"}, "std::allocator<char>::deallocate": {"offset": "0x2FF80"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x2C770"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x2CD00"}, "std::allocator<int>::allocate": {"offset": "0x2C700"}, "std::allocator<int>::deallocate": {"offset": "0x2CCB0"}, "std::allocator<jitasm::Instr>::allocate": {"offset": "0x2C850"}, "std::allocator<jitasm::Instr>::deallocate": {"offset": "0x2CD50"}, "std::allocator<jitasm::compiler::BasicBlock *>::allocate": {"offset": "0x2C770"}, "std::allocator<jitasm::compiler::BasicBlock *>::deallocate": {"offset": "0x2CD00"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::allocate": {"offset": "0x2C8C0"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::deallocate": {"offset": "0x2CDA0"}, "std::allocator<jitasm::compiler::OrderedLabel>::allocate": {"offset": "0x2C7E0"}, "std::allocator<jitasm::compiler::OrderedLabel>::deallocate": {"offset": "0xFA20"}, "std::allocator<jitasm::compiler::RegUsePoint>::deallocate": {"offset": "0xFA20"}, "std::allocator<jitasm::compiler::VarAttribute>::deallocate": {"offset": "0x2CDF0"}, "std::allocator<std::pair<unsigned __int64,unsigned __int64> >::deallocate": {"offset": "0xFA20"}, "std::allocator<tagRAWINPUTDEVICE>::deallocate": {"offset": "0xFA20"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x2C770"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x2CD00"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x2FF80"}, "std::allocator<unsigned int>::allocate": {"offset": "0x2C700"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x2CCB0"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD1C0"}, "std::bad_alloc::bad_alloc": {"offset": "0x3A4A4"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA5F0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA1E0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA5F0"}, "std::bad_weak_ptr::bad_weak_ptr": {"offset": "0xEBE0"}, "std::bad_weak_ptr::what": {"offset": "0xFA70"}, "std::bad_weak_ptr::~bad_weak_ptr": {"offset": "0xA5F0"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x12DD0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x12F10"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x27F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x35880"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x11A00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x359F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD3B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9F80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA4C0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x303B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA520"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD230"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x34AD0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x377F0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x37950"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA520"}, "std::deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >::~deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >": {"offset": "0x1E9D0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Growmap": {"offset": "0x2BFE0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Tidy": {"offset": "0x2C460"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Xlen": {"offset": "0x2C6C0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::~deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x1EAA0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Growmap": {"offset": "0x2BE00"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Tidy": {"offset": "0x2C3A0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Xlen": {"offset": "0x2C6C0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_back": {"offset": "0x2DB90"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_front": {"offset": "0x2DC50"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::~deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >": {"offset": "0x1E9A0"}, "std::enable_shared_from_this<GlobalInputHandlerLocal>::~enable_shared_from_this<GlobalInputHandlerLocal>": {"offset": "0xEC20"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x11B90"}, "std::exception::exception": {"offset": "0xA210"}, "std::exception::what": {"offset": "0xE290"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x12BD0"}, "std::function<bool __cdecl(int &)>::~function<bool __cdecl(int &)>": {"offset": "0x12BD0"}, "std::function<bool __cdecl(tagRAWMOUSE const &)>::~function<bool __cdecl(tagRAWMOUSE const &)>": {"offset": "0x12BD0"}, "std::function<bool __cdecl(unsigned long,bool)>::~function<bool __cdecl(unsigned long,bool)>": {"offset": "0x12BD0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x12BD0"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x12BD0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x12BD0"}, "std::locale::~locale": {"offset": "0x339B0"}, "std::make_shared<ConVar<bool>,char const (&)[19],int,bool>": {"offset": "0x11E30"}, "std::make_shared<ConVar<bool>,char const (&)[23],int,bool>": {"offset": "0x11E30"}, "std::make_shared<GlobalInputHandlerLocal>": {"offset": "0xE9E0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x34070"}, "std::numpunct<char>::do_falsename": {"offset": "0x34080"}, "std::numpunct<char>::do_grouping": {"offset": "0x340C0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x34100"}, "std::numpunct<char>::do_truename": {"offset": "0x34110"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x33740"}, "std::rotate<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<jitasm::compiler::BasicBlock *> > > >": {"offset": "0x1DED0"}, "std::runtime_error::runtime_error": {"offset": "0xA290"}, "std::shared_ptr<GlobalInputHandler>::~shared_ptr<GlobalInputHandler>": {"offset": "0xECF0"}, "std::shared_ptr<GlobalInputHandlerLocal>::~shared_ptr<GlobalInputHandlerLocal>": {"offset": "0xECF0"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0xECF0"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0xECF0"}, "std::thread::_Invoke<std::tuple<<lambda_22e089c922018c960d55ab79b6d6428c> >,0>": {"offset": "0xE800"}, "std::thread::~thread": {"offset": "0xEEA0"}, "std::to_string": {"offset": "0x19110"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x12C70"}, "std::unique_ptr<fwEvent<HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::callback,std::default_delete<fwEvent<HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::callback> >::~unique_ptr<fwEvent<HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::callback,std::default_delete<fwEvent<HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::callback> >": {"offset": "0xED50"}, "std::unique_ptr<fwEvent<int &>::callback,std::default_delete<fwEvent<int &>::callback> >::~unique_ptr<fwEvent<int &>::callback,std::default_delete<fwEvent<int &>::callback> >": {"offset": "0xED50"}, "std::unique_ptr<fwEvent<tagRAWMOUSE const &>::callback,std::default_delete<fwEvent<tagRAWMOUSE const &>::callback> >::~unique_ptr<fwEvent<tagRAWMOUSE const &>::callback,std::default_delete<fwEvent<tagRAWMOUSE const &>::callback> >": {"offset": "0xED50"}, "std::unique_ptr<fwEvent<unsigned long,bool>::callback,std::default_delete<fwEvent<unsigned long,bool>::callback> >::~unique_ptr<fwEvent<unsigned long,bool>::callback,std::default_delete<fwEvent<unsigned long,bool>::callback> >": {"offset": "0xED50"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x33990"}, "std::unique_ptr<std::tuple<<lambda_22e089c922018c960d55ab79b6d6428c> >,std::default_delete<std::tuple<<lambda_22e089c922018c960d55ab79b6d6428c> > > >::~unique_ptr<std::tuple<<lambda_22e089c922018c960d55ab79b6d6428c> >,std::default_delete<std::tuple<<lambda_22e089c922018c960d55ab79b6d6428c> > > >": {"offset": "0xED60"}, "std::unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >::~unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >": {"offset": "0xED40"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x33410"}, "utf8::exception::exception": {"offset": "0x36C10"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x35C90"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x365C0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x36CA0"}, "utf8::invalid_code_point::what": {"offset": "0x37B00"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA5F0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x36D10"}, "utf8::invalid_utf8::what": {"offset": "0x37B10"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA5F0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x36D70"}, "utf8::not_enough_room::what": {"offset": "0x37B20"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA5F0"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x363A0"}, "vva": {"offset": "0x37AE0"}, "wil::GetFailureLogString": {"offset": "0x2EA30"}, "wil::ResultException::ResultException": {"offset": "0x2E520"}, "wil::ResultException::what": {"offset": "0x2E390"}, "wil::ResultException::~ResultException": {"offset": "0x2E450"}, "wil::StoredFailureInfo::SetFailureInfo": {"offset": "0x2F310"}, "wil::details::DebugBreak": {"offset": "0x2E9D0"}, "wil::details::GetCurrentModuleName": {"offset": "0x2E9E0"}, "wil::details::GetModuleInformation": {"offset": "0x2ECF0"}, "wil::details::HrToNtStatus": {"offset": "0x2EE30"}, "wil::details::LogStringPrintf": {"offset": "0x2F0E0"}, "wil::details::RecognizeCaughtExceptionFromCallback": {"offset": "0x2F160"}, "wil::details::RecordFailFast": {"offset": "0x2F1B0"}, "wil::details::ResultFromCaughtExceptionInternal": {"offset": "0x2F1C0"}, "wil::details::Rethrow": {"offset": "0x2F2C0"}, "wil::details::StringCchPrintfA": {"offset": "0x2FB70"}, "wil::details::ThrowResultExceptionInternal": {"offset": "0x2FD40"}, "wil::details::WilDynamicLoadRaiseFailFastException": {"offset": "0x2FD70"}, "wil::details::`dynamic initializer for 'g_header_init_InitializeResultExceptions''": {"offset": "0x1640"}, "wil::details::`dynamic initializer for 'g_header_init_WilInitialize_ResultMacros_DesktopOrSystem_SuppressPrivateApiUse''": {"offset": "0x1680"}, "wil::details::shared_buffer::create": {"offset": "0x2FE60"}, "xbr::GetGameBuild": {"offset": "0x16380"}, "xbr::GetReplaceExecutableInit": {"offset": "0x38920"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x38B40"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x18E0"}}}