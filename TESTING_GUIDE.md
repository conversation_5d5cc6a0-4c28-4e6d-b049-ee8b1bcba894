# 🧪 Guia Completo para Testar o FiveM Cheat

## 🎯 Pré-requisitos

### Software Necessário
- **FiveM** instalado e funcionando
- **Visual Studio 2022** (ou 2019)
- **Injetor de DLL** (recomendações abaixo)
- **Servidor de teste** (local ou privado)

### Injetores Recomendados
1. **Process Hacker** (gratuito, seguro)
2. **Extreme Injector** (popular)
3. **Manual DLL Injector** (simples)
4. **Xenos Injector** (avançado)

## 📋 Passo a Passo Completo

### **Etapa 1: Compilar o Offset Dumper**
```bash
# Na pasta principal
.\build.bat
```
✅ Resultado: `FiveMOffsetDumper.dll` criada

### **Etapa 2: Testar o Offset Dumper**

#### 2.1 Preparar FiveM
1. Abra o FiveM
2. Entre em um servidor (recomendo servidor local/privado)
3. Aguarde carregar completamente no jogo

#### 2.2 Injetar o Dumper
1. Abra o **Process Hacker** como administrador
2. Encontre o processo `FiveM.exe` ou `FiveM_GTAProcess.exe`
3. Clique com botão direito → **Miscellaneous** → **Inject DLL**
4. Selecione `FiveMOffsetDumper.dll`
5. Clique **Open**

#### 2.3 Usar o Dumper
1. No jogo, pressione **INSERT** para abrir a interface
2. Clique em **"Scan All Offsets"**
3. Aguarde o scan completar (pode demorar 1-2 minutos)
4. Vá em **File** → **Export to C++ Header**
5. Salve como `offsets.h`

### **Etapa 3: Compilar o Cheat**

#### 3.1 Atualizar Offsets
```bash
# Copiar offsets reais para o exemplo
copy offsets.h examples\offsets.h
```

#### 3.2 Compilar o Cheat
```bash
cd examples
.\build_cheat.bat
```
✅ Resultado: `FiveMSimpleCheat.dll` criada

### **Etapa 4: Testar o Cheat**

#### 4.1 Preparar Teste
1. **IMPORTANTE**: Use um servidor privado ou modo offline
2. Feche o FiveM completamente
3. Abra o FiveM novamente
4. Entre no servidor de teste

#### 4.2 Injetar o Cheat
1. Abra o **Process Hacker** como administrador
2. Encontre o processo do FiveM
3. Injete `FiveMSimpleCheat.dll`
4. Verifique se não há erros

#### 4.3 Testar Funcionalidades

##### 🎮 Controles Básicos
- **INSERT** - Abrir/fechar menu
- **F1** - Toggle ESP
- **F2** - Toggle God Mode

##### 👁️ Testar ESP
1. Pressione **F1** para ativar ESP
2. Procure por outros jogadores no servidor
3. Você deve ver caixas vermelhas ao redor dos jogadores
4. Informações de vida/armadura devem aparecer

##### 🛡️ Testar God Mode
1. Pressione **F2** para ativar God Mode
2. Deixe NPCs ou outros jogadores te atacarem
3. Sua vida deve permanecer em 100%

##### 📍 Testar Teleport
1. Pressione **INSERT** para abrir menu
2. Vá em **Player Hacks**
3. Digite coordenadas em **Teleport Position**
4. Clique **Teleport**

##### 🔫 Testar Infinite Ammo
1. No menu, vá em **Weapon Hacks**
2. Ative **Infinite Ammo**
3. Atire com qualquer arma
4. Munição deve permanecer infinita

## 🔧 Troubleshooting

### ❌ Problemas Comuns

#### **Cheat não injeta**
```bash
# Soluções:
1. Execute Process Hacker como administrador
2. Verifique se o FiveM está rodando
3. Tente injetar em FiveM_GTAProcess.exe em vez de FiveM.exe
4. Desative antivírus temporariamente
```

#### **Menu não abre (INSERT não funciona)**
```bash
# Soluções:
1. Verifique se a DLL foi injetada corretamente
2. Tente outras teclas (F1, F2)
3. Verifique console para erros
4. Recompile com debug ativo
```

#### **ESP não aparece**
```bash
# Soluções:
1. Certifique-se que há outros jogadores no servidor
2. Pressione F1 várias vezes
3. Verifique se os offsets estão corretos
4. Teste em servidor com mais jogadores
```

#### **Offsets não encontrados**
```bash
# Soluções:
1. Aguarde o FiveM carregar completamente
2. Entre em um servidor antes de escanear
3. Tente escanear múltiplas vezes
4. Verifique se a versão do FiveM é compatível
```

### 🐛 Debug Avançado

#### Ativar Console Debug
```cpp
// No dllmain.cpp, adicione:
AllocConsole();
freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
std::cout << "[DEBUG] Cheat iniciado!" << std::endl;
```

#### Verificar Offsets
```cpp
// Adicione logs para verificar offsets:
std::cout << "[DEBUG] LocalPlayer offset: 0x" << std::hex << FiveMOffsets::Player::LocalPlayer << std::endl;
std::cout << "[DEBUG] Module base: 0x" << std::hex << moduleBase << std::endl;
```

## 🎯 Testes Específicos

### **Teste 1: Verificar Injeção**
```cpp
// Adicione no DllMain:
MessageBox(NULL, L"Cheat injetado com sucesso!", L"FiveM Cheat", MB_OK);
```

### **Teste 2: Verificar Offsets**
```cpp
// No menu, adicione:
ImGui::Text("Module Base: 0x%llX", moduleBase);
ImGui::Text("LocalPlayer Offset: 0x%X", FiveMOffsets::Player::LocalPlayer);
```

### **Teste 3: Verificar Memória**
```cpp
// Teste leitura de memória:
uintptr_t localPlayer = GetLocalPlayer();
ImGui::Text("LocalPlayer Pointer: 0x%llX", localPlayer);
if (localPlayer) {
    Vector3 pos = GetLocalPlayerPosition();
    ImGui::Text("Position: %.2f, %.2f, %.2f", pos.x, pos.y, pos.z);
}
```

## 🛡️ Segurança e Boas Práticas

### ⚠️ Antes de Testar
1. **Sempre teste em servidor privado primeiro**
2. **Faça backup dos saves do GTA V**
3. **Use conta secundária do FiveM**
4. **Desative antivírus durante testes**

### 🔒 Durante os Testes
1. **Não use em servidores públicos**
2. **Não abuse das funcionalidades**
3. **Teste uma funcionalidade por vez**
4. **Monitore por crashes ou instabilidade**

### 📝 Após os Testes
1. **Documente bugs encontrados**
2. **Salve offsets funcionais**
3. **Limpe arquivos temporários**
4. **Reative antivírus**

## 🎮 Servidores Recomendados para Teste

### **Servidor Local**
```bash
# Criar servidor local FiveM:
1. Baixe FXServer
2. Configure server.cfg básico
3. Inicie servidor local
4. Conecte via localhost
```

### **Servidores de Desenvolvimento**
- Servidores marcados como "DEV" ou "TEST"
- Servidores privados com poucos jogadores
- Servidores que permitem cheats/mods

## 📊 Checklist de Teste

### ✅ Pré-teste
- [ ] FiveM funcionando
- [ ] Injetor instalado
- [ ] DLLs compiladas
- [ ] Servidor de teste preparado

### ✅ Teste do Dumper
- [ ] DLL injeta sem erros
- [ ] Interface abre com INSERT
- [ ] Scan encontra offsets
- [ ] Export funciona

### ✅ Teste do Cheat
- [ ] DLL injeta sem erros
- [ ] Menu abre com INSERT
- [ ] ESP funciona (F1)
- [ ] God Mode funciona (F2)
- [ ] Teleport funciona
- [ ] Infinite Ammo funciona

### ✅ Pós-teste
- [ ] Sem crashes
- [ ] Sem detecção
- [ ] Funcionalidades estáveis
- [ ] Offsets salvos

## 🚀 Próximos Passos

Após testar com sucesso:

1. **Adicionar mais funcionalidades**
2. **Melhorar interface**
3. **Implementar proteções anti-detecção**
4. **Criar sistema de configuração**
5. **Adicionar hotkeys customizáveis**

## 📞 Suporte

Se encontrar problemas:
1. Verifique este guia novamente
2. Confira os logs de debug
3. Teste com offsets diferentes
4. Recompile com configurações diferentes

**Lembre-se**: Sempre use responsavelmente! 🎮
