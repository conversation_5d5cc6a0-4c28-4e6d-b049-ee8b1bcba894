#pragma once
#include <Windows.h>
#include <vector>
#include <string>

class PatternScanner {
public:
    PatternScanner();
    ~PatternScanner();
    
    // Pattern scanning functions
    uintptr_t FindPattern(const std::string& pattern, uintptr_t startAddress = 0, size_t searchSize = 0);
    uintptr_t FindPatternInModule(const std::string& pattern, const std::string& moduleName);
    uintptr_t FindPatternInModule(const std::string& pattern, HMODULE hModule);
    
    // Multiple pattern search
    std::vector<uintptr_t> FindAllPatterns(const std::string& pattern, uintptr_t startAddress = 0, size_t searchSize = 0);
    
    // Utility functions
    static std::vector<uint8_t> PatternToBytes(const std::string& pattern);
    static bool CompareBytes(const uint8_t* data, const std::vector<uint8_t>& pattern, const std::vector<bool>& mask);
    
    // Memory protection
    bool IsMemoryReadable(uintptr_t address, size_t size);
    
    // Error handling
    const std::string& GetLastError() const { return m_lastError; }
    
private:
    uintptr_t ScanMemoryRegion(const std::vector<uint8_t>& pattern, const std::vector<bool>& mask, 
                              uintptr_t startAddress, size_t size);
    
    std::string m_lastError;
};
