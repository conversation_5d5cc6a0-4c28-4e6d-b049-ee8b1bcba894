// FiveM Cheat Simples com Offsets Hardcoded
#include <windows.h>
#include <iostream>
#include "offsets_auto.h"

// Funcao para ler memoria com seguranca
template<typename T>
T ReadMemory(uintptr_t address) {
    __try {
        return *reinterpret_cast<T*>(address);
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        return T{};
    }
}

// Funcao para escrever memoria com seguranca
template<typename T>
void WriteMemory(uintptr_t address, T value) {
    __try {
        *reinterpret_cast<T*>(address) = value;
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        // Ignorar erros
    }
}

// Obter jogador local
uintptr_t GetLocalPlayer() {
    auto GetPlayerPed = reinterpret_cast<uintptr_t(*)(int)>(FiveMOffsets::Functions::GetPlayerPed);
    __try {
        return GetPlayerPed(-1);
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// God Mode
void ToggleGodMode() {
    static bool enabled = false;
    enabled = !enabled;
    std::cout << "[God Mode] " << (enabled ? "ON" : "OFF") << std::endl;
    
    if (enabled) {
        uintptr_t localPlayer = GetLocalPlayer();
        if (localPlayer) {
            WriteMemory<float>(localPlayer + 0x280, 100.0f); // Health
            WriteMemory<float>(localPlayer + 0x14C, 100.0f); // Armor
        }
    }
}

// Infinite Ammo
void ToggleInfiniteAmmo() {
    static bool enabled = false;
    enabled = !enabled;
    std::cout << "[Infinite Ammo] " << (enabled ? "ON" : "OFF") << std::endl;
    
    if (enabled) {
        WriteMemory<bool>(FiveMOffsets::Flags::InfiniteAmmo, true);
    } else {
        WriteMemory<bool>(FiveMOffsets::Flags::InfiniteAmmo, false);
    }
}

// Thread principal do cheat
DWORD WINAPI CheatThread(LPVOID lpParam) {
    AllocConsole();
    freopen_s(reinterpret_cast<FILE**>(stdin), "CONIN$", "r", stdin);
    freopen_s(reinterpret_cast<FILE**>(stdout), "CONOUT$", "w", stdout);
    freopen_s(reinterpret_cast<FILE**>(stderr), "CONOUT$", "w", stderr);

    std::cout << "[FiveM Cheat] Iniciado com offsets hardcoded!" << std::endl;
    std::cout << "[FiveM Cheat] F1 = God Mode, F2 = Infinite Ammo" << std::endl;

    while (true) {
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            ToggleGodMode();
            Sleep(500);
        }

        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            ToggleInfiniteAmmo();
            Sleep(500);
        }

        Sleep(50);
    }

    return 0;
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            CreateThread(nullptr, 0, CheatThread, nullptr, 0, nullptr);
            break;
        case DLL_PROCESS_DETACH:
            FreeConsole();
            break;
    }
    return TRUE;
}
