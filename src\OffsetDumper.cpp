#include "OffsetDumper.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <thread>
#include <iomanip>
#include <map>
#include <algorithm>
#include <ctime>
#include <Psapi.h>

OffsetDumper::OffsetDumper() :
    m_isScanning(false),
    m_scanProgress(0.0f),
    m_gameModule(nullptr),
    m_moduleBase(0),
    m_moduleSize(0) {
}

OffsetDumper::~OffsetDumper() {
}

bool OffsetDumper::Initialize() {
    try {
        // Get game module
        m_gameModule = GetModuleHandle(nullptr);
        if (!m_gameModule) {
            m_lastError = "Failed to get game module handle";
            return false;
        }
        
        // Get module info
        MODULEINFO modInfo;
        if (!GetModuleInformation(GetCurrentProcess(), m_gameModule, &modInfo, sizeof(modInfo))) {
            m_lastError = "Failed to get module information";
            return false;
        }
        
        m_moduleBase = reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
        m_moduleSize = modInfo.SizeOfImage;
        
        // Initialize database
        m_database.Initialize();
        InitializeOffsetDatabase();
        
        std::cout << "[OffsetDumper] Initialized successfully" << std::endl;
        std::cout << "[OffsetDumper] Module Base: 0x" << std::hex << m_moduleBase << std::endl;
        std::cout << "[OffsetDumper] Module Size: 0x" << std::hex << m_moduleSize << std::endl;
        std::cout << "[OffsetDumper] Loaded " << m_offsets.size() << " offset patterns" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        m_lastError = std::string("Exception in Initialize: ") + e.what();
        return false;
    }
}

void OffsetDumper::InitializeOffsetDatabase() {
    m_offsets.clear();
    
    const auto& patterns = m_database.GetPatterns();
    for (const auto& pattern : patterns) {
        OffsetInfo info;
        info.name = pattern.name;
        info.description = pattern.description;
        info.pattern = pattern.pattern;
        info.category = pattern.category;
        info.address = 0;
        info.offset = 0;
        info.found = false;
        
        m_offsets.push_back(info);
    }
}

void OffsetDumper::ScanAllOffsets() {
    if (m_isScanning) {
        m_lastError = "Scan already in progress";
        return;
    }
    
    std::thread scanThread([this]() {
        m_isScanning = true;
        m_scanProgress = 0.0f;
        m_lastError.clear();
        
        try {
            std::cout << "[OffsetDumper] Starting scan of " << m_offsets.size() << " offsets..." << std::endl;
            
            for (size_t i = 0; i < m_offsets.size(); ++i) {
                ScanOffset(m_offsets[i]);
                m_scanProgress = static_cast<float>(i + 1) / static_cast<float>(m_offsets.size());
                
                // Small delay to prevent overwhelming the system
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            // Count results
            int foundCount = 0;
            for (const auto& offset : m_offsets) {
                if (offset.found) foundCount++;
            }
            
            std::cout << "[OffsetDumper] Scan completed: " << foundCount << "/" << m_offsets.size() << " offsets found" << std::endl;
            
        } catch (const std::exception& e) {
            m_lastError = std::string("Exception during scan: ") + e.what();
            std::cerr << "[OffsetDumper] " << m_lastError << std::endl;
        }
        
        m_isScanning = false;
        m_scanProgress = 1.0f;
    });
    
    scanThread.detach();
}

void OffsetDumper::ScanSpecificOffset(const std::string& name) {
    auto it = std::find_if(m_offsets.begin(), m_offsets.end(),
        [&name](const OffsetInfo& info) { return info.name == name; });
    
    if (it != m_offsets.end()) {
        ScanOffset(*it);
        std::cout << "[OffsetDumper] Rescanned offset: " << name << " - " 
                  << (it->found ? "Found" : "Not found") << std::endl;
    } else {
        m_lastError = "Offset not found: " + name;
    }
}

void OffsetDumper::ScanOffset(OffsetInfo& offset) {
    try {
        // Find pattern in memory
        uintptr_t foundAddress = m_scanner.FindPattern(offset.pattern, m_moduleBase, m_moduleSize);
        
        if (foundAddress != 0) {
            const auto* pattern = m_database.GetPattern(offset.name);
            if (pattern) {
                if (pattern->isRelative) {
                    // Read relative address
                    uintptr_t relativeAddr = foundAddress + pattern->offset;
                    if (m_scanner.IsMemoryReadable(relativeAddr, sizeof(int32_t))) {
                        int32_t relativeOffset = *reinterpret_cast<int32_t*>(relativeAddr);
                        offset.address = relativeAddr + sizeof(int32_t) + relativeOffset;
                        offset.offset = offset.address - m_moduleBase;
                        offset.found = true;
                    }
                } else {
                    // Direct offset
                    offset.address = foundAddress + pattern->offset;
                    offset.offset = offset.address - m_moduleBase;
                    offset.found = true;
                }
            }
        }
        
        if (!offset.found) {
            offset.address = 0;
            offset.offset = 0;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[OffsetDumper] Exception scanning " << offset.name << ": " << e.what() << std::endl;
        offset.found = false;
        offset.address = 0;
        offset.offset = 0;
    }
}

const OffsetInfo* OffsetDumper::GetOffset(const std::string& name) const {
    auto it = std::find_if(m_offsets.begin(), m_offsets.end(),
        [&name](const OffsetInfo& info) { return info.name == name; });
    
    return (it != m_offsets.end()) ? &(*it) : nullptr;
}

uintptr_t OffsetDumper::GetModuleBase() const {
    return m_moduleBase;
}

uintptr_t OffsetDumper::GetModuleSize() const {
    return m_moduleSize;
}

bool OffsetDumper::ExportToJSON(const std::string& filename) const {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        file << "{\n";
        file << "  \"offsets\": {\n";

        for (size_t i = 0; i < m_offsets.size(); ++i) {
            const auto& offset = m_offsets[i];

            file << "    \"" << offset.name << "\": {\n";
            file << "      \"description\": \"" << offset.description << "\",\n";
            file << "      \"category\": \"" << offset.category << "\",\n";
            file << "      \"found\": " << (offset.found ? "true" : "false") << ",\n";

            if (offset.found) {
                file << "      \"address\": \"0x" << std::hex << std::uppercase << offset.address << "\",\n";
                file << "      \"offset\": \"0x" << std::hex << std::uppercase << offset.offset << "\"\n";
            } else {
                file << "      \"address\": null,\n";
                file << "      \"offset\": null\n";
            }

            file << "    }";
            if (i < m_offsets.size() - 1) file << ",";
            file << "\n";
        }

        file << "  },\n";
        file << "  \"metadata\": {\n";
        file << "    \"module_base\": \"0x" << std::hex << std::uppercase << m_moduleBase << "\",\n";
        file << "    \"module_size\": \"0x" << std::hex << std::uppercase << m_moduleSize << "\",\n";
        file << "    \"timestamp\": \"" << std::time(nullptr) << "\"\n";
        file << "  }\n";
        file << "}\n";

        file.close();
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool OffsetDumper::ExportToCppHeader(const std::string& filename) const {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        file << "#pragma once\n";
        file << "// FiveM Offsets - Generated by FiveM Offset Dumper\n";
        file << "// Timestamp: " << std::time(nullptr) << "\n";
        file << "// Module Base: 0x" << std::hex << std::uppercase << m_moduleBase << "\n";
        file << "// Module Size: 0x" << std::hex << std::uppercase << m_moduleSize << "\n\n";

        file << "#include <cstdint>\n\n";
        file << "namespace FiveMOffsets {\n";
        file << "    constexpr uintptr_t MODULE_BASE = 0x" << std::hex << std::uppercase << m_moduleBase << ";\n";
        file << "    constexpr size_t MODULE_SIZE = 0x" << std::hex << std::uppercase << m_moduleSize << ";\n\n";

        // Group by category
        std::map<std::string, std::vector<const OffsetInfo*>> categories;
        for (const auto& offset : m_offsets) {
            if (offset.found) {
                categories[offset.category].push_back(&offset);
            }
        }

        for (const auto& category : categories) {
            file << "    // " << category.first << " Offsets\n";
            file << "    namespace " << category.first << " {\n";

            for (const auto* offset : category.second) {
                file << "        constexpr uintptr_t " << offset->name << " = 0x"
                     << std::hex << std::uppercase << offset->offset << "; // " << offset->description << "\n";
            }

            file << "    }\n\n";
        }

        file << "}\n";

        file.close();
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool OffsetDumper::ExportToCheatEngineTable(const std::string& filename) const {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        file << "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n";
        file << "<CheatTable>\n";
        file << "  <CheatEntries>\n";

        for (const auto& offset : m_offsets) {
            if (!offset.found) continue;

            file << "    <CheatEntry>\n";
            file << "      <ID>" << &offset - &m_offsets[0] << "</ID>\n";
            file << "      <Description>\"" << offset.name << " - " << offset.description << "\"</Description>\n";
            file << "      <VariableType>8 Bytes</VariableType>\n";
            file << "      <Address>" << std::hex << std::uppercase << offset.address << "</Address>\n";
            file << "    </CheatEntry>\n";
        }

        file << "  </CheatEntries>\n";
        file << "</CheatTable>\n";

        file.close();
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}
