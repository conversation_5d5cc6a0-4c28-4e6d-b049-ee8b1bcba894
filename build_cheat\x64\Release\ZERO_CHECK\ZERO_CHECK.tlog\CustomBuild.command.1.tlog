^C:\USERS\<USER>\DESKTOP\FIVEM OFFSETS\BUILD_CHEAT\CMAKEFILES\83DE4F5F47ED8C19D507D127198799C2\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/fivem offsets" "-BC:/Users/<USER>/Desktop/fivem offsets/build_cheat" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Desktop/fivem offsets/build_cheat/FiveMOffsetDumper.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
