{"citizen-mod-loader-five.dll": {"AddCrashometryV": {"offset": "0x23DF0"}, "CfxState::CfxState": {"offset": "0xCB40"}, "Component::As": {"offset": "0xB5A0"}, "Component::IsA": {"offset": "0xB660"}, "Component::SetCommandLine": {"offset": "0xB670"}, "Component::SetUserData": {"offset": "0xB680"}, "ComponentInstance::DoGameLoad": {"offset": "0xB640"}, "ComponentInstance::Initialize": {"offset": "0xB650"}, "ComponentInstance::Shutdown": {"offset": "0xB680"}, "CoreGetComponentRegistry": {"offset": "0x1A670"}, "CreateComponent": {"offset": "0xB690"}, "DllMain": {"offset": "0x2C24C"}, "DoNtRaiseException": {"offset": "0x25220"}, "GetAbsoluteCitPath": {"offset": "0x24090"}, "HookFunction::Run": {"offset": "0xB740"}, "HookFunctionBase::Register": {"offset": "0x25F30"}, "HookFunctionBase::RunAll": {"offset": "0x25F50"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0xC6F0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x23610"}, "InitFunction::Run": {"offset": "0xB6C0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x25020"}, "InitFunctionBase::Register": {"offset": "0x25390"}, "InitFunctionBase::RunAll": {"offset": "0x253E0"}, "MakeRelativeCitPath": {"offset": "0xA670"}, "ParseGuid": {"offset": "0xEA50"}, "RaiseDebugException": {"offset": "0x25300"}, "ToWide": {"offset": "0x25410"}, "TraceRealV": {"offset": "0x25720"}, "Win32TrapAndJump64": {"offset": "0x26520"}, "_DllMainCRTStartup": {"offset": "0x2BB30"}, "_Init_thread_abort": {"offset": "0x2AF20"}, "_Init_thread_footer": {"offset": "0x2AF50"}, "_Init_thread_header": {"offset": "0x2AFB0"}, "_Init_thread_notify": {"offset": "0x2B018"}, "_Init_thread_wait": {"offset": "0x2B05C"}, "_RTC_Initialize": {"offset": "0x2C2B0"}, "_RTC_Terminate": {"offset": "0x2C2EC"}, "__ArrayUnwind": {"offset": "0x2BBDC"}, "__GSHandlerCheck": {"offset": "0x2B62C"}, "__GSHandlerCheckCommon": {"offset": "0x2B64C"}, "__GSHandlerCheck_EH": {"offset": "0x2B6A8"}, "__GSHandlerCheck_SEH": {"offset": "0x2BDBC"}, "__chkstk": {"offset": "0x2B7C0"}, "__crt_debugger_hook": {"offset": "0x2BFF8"}, "__dyn_tls_init": {"offset": "0x2B484"}, "__isa_available_init": {"offset": "0x2BE4C"}, "__local_stdio_printf_options": {"offset": "0xB480"}, "__local_stdio_scanf_options": {"offset": "0x2AA00"}, "__raise_securityfailure": {"offset": "0x2BC40"}, "__report_gsfailure": {"offset": "0x2BC74"}, "__scrt_acquire_startup_lock": {"offset": "0x2B104"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x2B140"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x2B174"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x2B18C"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x2B1B4"}, "__scrt_dllmain_exception_filter": {"offset": "0x2B1CC"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x2B22C"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x2B25C"}, "__scrt_fastfail": {"offset": "0x2C000"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x2C2A8"}, "__scrt_initialize_crt": {"offset": "0x2B270"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x2C28C"}, "__scrt_initialize_onexit_tables": {"offset": "0x2B2BC"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x2AE28"}, "__scrt_initialize_type_info": {"offset": "0x2C270"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x2B348"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x2D25C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x2C194"}, "__scrt_release_startup_lock": {"offset": "0x2B3E0"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xB680"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xB680"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xB680"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xB680"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xB680"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xB5A0"}, "__scrt_throw_std_bad_alloc": {"offset": "0x2C16C"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xAAE0"}, "__scrt_uninitialize_crt": {"offset": "0x2B404"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x2AEF8"}, "__scrt_uninitialize_type_info": {"offset": "0x2C280"}, "__security_check_cookie": {"offset": "0x2B740"}, "__security_init_cookie": {"offset": "0x2C1A0"}, "__std_count_trivial_1": {"offset": "0x2AB20"}, "__std_find_trivial_1": {"offset": "0x2AC20"}, "__std_find_trivial_2": {"offset": "0x2ACF0"}, "_get_startup_argv_mode": {"offset": "0x2C18C"}, "_guard_check_icall_nop": {"offset": "0xB670"}, "_guard_dispatch_icall_nop": {"offset": "0x2C430"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x2C450"}, "_onexit": {"offset": "0x2B430"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x2D338"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x2D292"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x2D2A9"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x2D2C2"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x2D2D6"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_14''": {"offset": "0x1500"}, "`dynamic initializer for '_init_instance_15''": {"offset": "0x1530"}, "`dynamic initializer for '_init_instance_16''": {"offset": "0x1560"}, "`dynamic initializer for '_init_instance_17''": {"offset": "0x1590"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1280"}, "`dynamic initializer for 'initFunction''": {"offset": "0x15F0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x17B0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x1440"}, "`dynamic initializer for 'xbr::virt::Base<19,2802,0,6>::ms_initFunction''": {"offset": "0x1480"}, "`dynamic initializer for 'xbr::virt::Base<2,2802,2,6>::ms_initFunction''": {"offset": "0x1210"}, "`dynamic initializer for 'xbr::virt::Base<45,2802,0,6>::ms_initFunction''": {"offset": "0x14C0"}, "`fx::MountModStream'::`2'::DlcEntry::~DlcEntry": {"offset": "0x19120"}, "atexit": {"offset": "0x2B46C"}, "capture_previous_context": {"offset": "0x2BD48"}, "dllmain_crt_dispatch": {"offset": "0x2B810"}, "dllmain_crt_process_attach": {"offset": "0x2B860"}, "dllmain_crt_process_detach": {"offset": "0x2B978"}, "dllmain_dispatch": {"offset": "0x2B9FC"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xB120"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0x9660"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x22D60"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x22090"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0xF6D0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x18F20"}, "fmt::v8::detail::add_compare": {"offset": "0x224D0"}, "fmt::v8::detail::assert_fail": {"offset": "0x22610"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x22660"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x22830"}, "fmt::v8::detail::bigint::square": {"offset": "0x23130"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x22090"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x1F80"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x233F0"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x11C50"}, "fmt::v8::detail::compare": {"offset": "0x22790"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x11D10"}, "fmt::v8::detail::count_digits": {"offset": "0xAF00"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x1E8C0"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x1E9D0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2030"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x22C30"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x20840"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x22F10"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x20870"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x21640"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x21210"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x22E90"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x1EA80"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x1EA80"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x11DA0"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x11E60"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x22BC0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2060"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x1FF30"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x2330"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x1FE10"}, "fmt::v8::detail::format_float<double>": {"offset": "0x1E3D0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x20070"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x2410"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x11EE0"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x203D0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x2530"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x2530"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x2690"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x12050"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x28F0"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x122D0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xB3D0"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x1E320"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x20A80"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x20D00"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x20F90"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x21100"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0x96C0"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0x96C0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x29C0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xB210"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3FC0"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x138C0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x4FC0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x4B70"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x5400"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x21D90"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x21C70"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x4AA0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x143E0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x14900"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x144B0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x14D40"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x15180"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x152C0"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x5840"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x15400"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x5880"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x154F0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x5A10"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x156A0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x63D0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x5DE0"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x161D0"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x15BB0"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x9360"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0x9360"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x6B00"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x167F0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x6F20"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x70F0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7290"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7290"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x16C80"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x7420"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x7640"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x77C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x8F50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x7950"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x7B70"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x7E10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8030"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x81B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x83D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x85F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x8770"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x8990"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x8B10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x8D30"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x16E00"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x16FA0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x17140"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x172D0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x17470"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x17610"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x177B0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x17960"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x17B00"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x90E0"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x17CA0"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x17E90"}, "fmt::v8::format_error::format_error": {"offset": "0x9560"}, "fmt::v8::format_error::~format_error": {"offset": "0x9790"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x24870"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2E30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12A50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12820"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2AE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12710"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x29F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x125E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2E30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12A50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2D00"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12920"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x2F60"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x12B90"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3AC0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x13640"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x34F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x130E0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0xC5D0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x24EF0"}, "fprintf": {"offset": "0xB490"}, "fwEvent<>::ConnectInternal": {"offset": "0xDEE0"}, "fwPlatformString::~fwPlatformString": {"offset": "0x97B0"}, "fwRefContainer<fx::ModVFSDevice>::~fwRefContainer<fx::ModVFSDevice>": {"offset": "0xCC70"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0xCC70"}, "fwRefContainer<vfs::RagePackfile7>::~fwRefContainer<vfs::RagePackfile7>": {"offset": "0xCC70"}, "fwRefContainer<vfs::Stream>::~fwRefContainer<vfs::Stream>": {"offset": "0xCC70"}, "fwRefCountable::AddRef": {"offset": "0x25EF0"}, "fwRefCountable::Release": {"offset": "0x25F00"}, "fwRefCountable::~fwRefCountable": {"offset": "0x25EE0"}, "fx::ModPackage::Content::Entry::~Entry": {"offset": "0xCE60"}, "fx::ModPackage::Content::~Content": {"offset": "0xCE50"}, "fx::ModPackage::GetGuidString": {"offset": "0x1A880"}, "fx::ModPackage::Metadata::~Metadata": {"offset": "0xCF20"}, "fx::ModPackage::ParsePackage": {"offset": "0xEEE0"}, "fx::ModVFSDevice::Close": {"offset": "0x1A650"}, "fx::ModVFSDevice::CloseBulk": {"offset": "0x1A660"}, "fx::ModVFSDevice::ExtensionCtl": {"offset": "0x1A700"}, "fx::ModVFSDevice::FindClose": {"offset": "0xB670"}, "fx::ModVFSDevice::FindFirst": {"offset": "0xB5A0"}, "fx::ModVFSDevice::FindNext": {"offset": "0xB660"}, "fx::ModVFSDevice::Flush": {"offset": "0xB680"}, "fx::ModVFSDevice::GetAbsolutePath": {"offset": "0x1A850"}, "fx::ModVFSDevice::GetLength": {"offset": "0x1AA80"}, "fx::ModVFSDevice::MapFileName": {"offset": "0x1AA90"}, "fx::ModVFSDevice::ModVFSDevice": {"offset": "0x17F70"}, "fx::ModVFSDevice::Open": {"offset": "0x1DB90"}, "fx::ModVFSDevice::OpenBulk": {"offset": "0x1DC50"}, "fx::ModVFSDevice::Read": {"offset": "0x1DD10"}, "fx::ModVFSDevice::ReadBulk": {"offset": "0x1DD20"}, "fx::ModVFSDevice::Seek": {"offset": "0x1DD30"}, "fx::ModVFSDevice::SetPathPrefix": {"offset": "0x1DD40"}, "fx::ModsNeedEncryption": {"offset": "0x1AD60"}, "fx::MountFauxStreamingRpf": {"offset": "0x1AD80"}, "fx::MountModDevice": {"offset": "0x1B540"}, "fx::MountModStream": {"offset": "0x1BF50"}, "fx::ParseContent": {"offset": "0xE4D0"}, "fx::ParseMetadata": {"offset": "0xEC20"}, "fx::`dynamic initializer for 'g_devices''": {"offset": "0x15C0"}, "fx::client::GetPureLevel": {"offset": "0xE220"}, "launch::IsSDKGuest": {"offset": "0xE450"}, "sscanf_s": {"offset": "0x2AA10"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<fwRefContainer<vfs::Device>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<fwRefContainer<vfs::Device>,void *> > >": {"offset": "0x18E90"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::shared_ptr<fx::ModPackage>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::shared_ptr<fx::ModPackage>,void *> > >": {"offset": "0xCC00"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x95E0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x18EB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x95E0"}, "std::_Buffered_inplace_merge_divide_and_conquer2<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0xFD40"}, "std::_Buffered_inplace_merge_divide_and_conquer<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0xFE30"}, "std::_Buffered_inplace_merge_unchecked<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10010"}, "std::_Buffered_merge_sort_unchecked<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10230"}, "std::_Buffered_rotate_unchecked<`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x10390"}, "std::_Chunked_merge_unchecked<`fx::MountModStream'::`2'::DlcEntry *,`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10590"}, "std::_Destroy_range<std::allocator<`fx::MountModStream'::`2'::DlcEntry> >": {"offset": "0x107D0"}, "std::_Destroy_range<std::allocator<fx::ModPackage::Content::Entry> >": {"offset": "0xBAA0"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0xBAE0"}, "std::_Facet_Register": {"offset": "0x2ADD8"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0xCC20"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Copy": {"offset": "0xF7F0"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Delete_this": {"offset": "0xB730"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Do_call": {"offset": "0xF810"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Get": {"offset": "0xB720"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Move": {"offset": "0xF7F0"}, "std::_Func_impl_no_alloc<<lambda_0c7ca1b58c9b9139f3fa7f602443c87e>,bool>::_Target_type": {"offset": "0xF830"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Copy": {"offset": "0xB6D0"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Delete_this": {"offset": "0xB730"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Do_call": {"offset": "0xB6F0"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Get": {"offset": "0xB720"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Move": {"offset": "0xB6D0"}, "std::_Func_impl_no_alloc<<lambda_7b1eb61ac2e8465d2f58b86e362e3ec1>,bool>::_Target_type": {"offset": "0xB710"}, "std::_Inplace_merge_buffer_left<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10B80"}, "std::_Inplace_merge_buffer_right<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10D80"}, "std::_Insertion_sort_unchecked<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x10FA0"}, "std::_Maklocstr<char>": {"offset": "0xB4E0"}, "std::_Maklocstr<wchar_t>": {"offset": "0x1E7B0"}, "std::_Move_backward_unchecked<`fx::MountModStream'::`2'::DlcEntry *,`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x11210"}, "std::_Move_unchecked<`fx::MountModStream'::`2'::DlcEntry *,`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x11290"}, "std::_Optimistic_temporary_buffer<`fx::MountModStream'::`2'::DlcEntry>::~_Optimistic_temporary_buffer<`fx::MountModStream'::`2'::DlcEntry>": {"offset": "0x18ED0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xB5A0"}, "std::_Ref_count_obj2<fx::ModPackage>::_Delete_this": {"offset": "0xB760"}, "std::_Ref_count_obj2<fx::ModPackage>::_Destroy": {"offset": "0xB750"}, "std::_Rotate_one_left<`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x114E0"}, "std::_Stable_sort_unchecked<`fx::MountModStream'::`2'::DlcEntry *,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x11660"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x1DD80"}, "std::_Throw_bad_array_new_length": {"offset": "0xAAE0"}, "std::_Throw_bad_cast": {"offset": "0x224B0"}, "std::_Throw_tree_length_error": {"offset": "0xAB00"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x22050"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x22050"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x1C20"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x1EA0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0x9600"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x95E0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x18EB0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xC050"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xA880"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x10B00"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xA880"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x1BC0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xA880"}, "std::_Uninitialized_backout<`fx::MountModStream'::`2'::DlcEntry *>::~_Uninitialized_backout<`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x18EE0"}, "std::_Uninitialized_backout_al<std::allocator<`fx::MountModStream'::`2'::DlcEntry> >::~_Uninitialized_backout_al<std::allocator<`fx::MountModStream'::`2'::DlcEntry> >": {"offset": "0x18EE0"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0xCC60"}, "std::_Uninitialized_chunked_merge_unchecked2<`fx::MountModStream'::`2'::DlcEntry *,`fx::MountModStream'::`2'::DlcEntry,<lambda_3e48ba4227aba35ede5c4ac657045043> >": {"offset": "0x11910"}, "std::_Uninitialized_move<`fx::MountModStream'::`2'::DlcEntry *,std::allocator<`fx::MountModStream'::`2'::DlcEntry> >": {"offset": "0x11AD0"}, "std::_Uninitialized_move<fx::ModPackage::Content::Entry *,std::allocator<fx::ModPackage::Content::Entry> >": {"offset": "0xC450"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0xC550"}, "std::_Uninitialized_move_unchecked<`fx::MountModStream'::`2'::DlcEntry *,`fx::MountModStream'::`2'::DlcEntry *>": {"offset": "0x11B90"}, "std::_Xlen_string": {"offset": "0xAB20"}, "std::allocator<`fx::MountModStream'::`2'::DlcEntry>::deallocate": {"offset": "0x1DFA0"}, "std::allocator<char>::allocate": {"offset": "0xAB40"}, "std::allocator<char>::deallocate": {"offset": "0x1DF60"}, "std::allocator<fx::ModPackage::Content::Entry>::deallocate": {"offset": "0xF630"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0xF5C0"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0xF680"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x1DF60"}, "std::allocator<wchar_t>::allocate": {"offset": "0xABA0"}, "std::bad_alloc::bad_alloc": {"offset": "0x2C14C"}, "std::bad_alloc::~bad_alloc": {"offset": "0x9790"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x94F0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0x9790"}, "std::bad_cast::bad_cast": {"offset": "0x22020"}, "std::bad_cast::~bad_cast": {"offset": "0x9790"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x1B00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_55b0f89b53a0342680e40398a0d39887>,unsigned __int64,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x11310"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0xC110"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0xC2A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0x96C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x1DDA0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xAD90"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9290"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find": {"offset": "0x1DFF0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::replace": {"offset": "0x1E0F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x96C0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x10700"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0x9720"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xAC10"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign": {"offset": "0x1DE30"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x23540"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x25BC0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x25D20"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x9720"}, "std::exception::exception": {"offset": "0x9520"}, "std::exception::what": {"offset": "0xB3B0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0xCC20"}, "std::list<std::shared_ptr<fx::ModPackage>,std::allocator<std::shared_ptr<fx::ModPackage> > >::~list<std::shared_ptr<fx::ModPackage>,std::allocator<std::shared_ptr<fx::ModPackage> > >": {"offset": "0xCCB0"}, "std::locale::~locale": {"offset": "0x22110"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::IgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Try_emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x11770"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::IgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,fx::IgnoreCaseLess,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x18F80"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x22A40"}, "std::numpunct<char>::do_falsename": {"offset": "0x22A60"}, "std::numpunct<char>::do_grouping": {"offset": "0x22AE0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x22B20"}, "std::numpunct<char>::do_truename": {"offset": "0x22B40"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x21E30"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x222C0"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x22A50"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x22AA0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x22AE0"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x22B30"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x22B80"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x18FB0"}, "std::runtime_error::runtime_error": {"offset": "0x95A0"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,IgnoreCaseLess,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,IgnoreCaseLess,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0xCD60"}, "std::shared_ptr<fx::ModPackage>::~shared_ptr<fx::ModPackage>": {"offset": "0xCD90"}, "std::stoi": {"offset": "0x1E290"}, "std::swap<`fx::MountModStream'::`2'::DlcEntry,0>": {"offset": "0x123C0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x220F0"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x21A10"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x21B80"}, "tinyxml2::DynArray<tinyxml2::XMLNode *,10>::Push": {"offset": "0x29E30"}, "tinyxml2::MemPoolT<104>::Alloc": {"offset": "0x27630"}, "tinyxml2::MemPoolT<104>::Clear": {"offset": "0x27E30"}, "tinyxml2::MemPoolT<104>::Free": {"offset": "0x28320"}, "tinyxml2::MemPoolT<104>::ItemSize": {"offset": "0x28B30"}, "tinyxml2::MemPoolT<104>::SetTracked": {"offset": "0x2A150"}, "tinyxml2::MemPoolT<104>::~MemPoolT<104>": {"offset": "0x26A00"}, "tinyxml2::MemPoolT<112>::Alloc": {"offset": "0x278A0"}, "tinyxml2::MemPoolT<112>::Clear": {"offset": "0x27E80"}, "tinyxml2::MemPoolT<112>::Free": {"offset": "0x28320"}, "tinyxml2::MemPoolT<112>::ItemSize": {"offset": "0x28B40"}, "tinyxml2::MemPoolT<112>::SetTracked": {"offset": "0x2A150"}, "tinyxml2::MemPoolT<112>::~MemPoolT<112>": {"offset": "0x26A80"}, "tinyxml2::MemPoolT<120>::Alloc": {"offset": "0x27AF0"}, "tinyxml2::MemPoolT<120>::Clear": {"offset": "0x27DE0"}, "tinyxml2::MemPoolT<120>::Free": {"offset": "0x28320"}, "tinyxml2::MemPoolT<120>::ItemSize": {"offset": "0x28B50"}, "tinyxml2::MemPoolT<120>::SetTracked": {"offset": "0x2A150"}, "tinyxml2::MemPoolT<120>::~MemPoolT<120>": {"offset": "0x26B00"}, "tinyxml2::MemPoolT<80>::Alloc": {"offset": "0x27340"}, "tinyxml2::MemPoolT<80>::Clear": {"offset": "0x27DE0"}, "tinyxml2::MemPoolT<80>::Free": {"offset": "0x28320"}, "tinyxml2::MemPoolT<80>::ItemSize": {"offset": "0x28B20"}, "tinyxml2::MemPoolT<80>::SetTracked": {"offset": "0x2A150"}, "tinyxml2::MemPoolT<80>::~MemPoolT<80>": {"offset": "0x26980"}, "tinyxml2::StrPair::GetStr": {"offset": "0x28560"}, "tinyxml2::StrPair::ParseName": {"offset": "0x29D50"}, "tinyxml2::StrPair::Set": {"offset": "0x29ED0"}, "tinyxml2::StrPair::SetStr": {"offset": "0x2A0B0"}, "tinyxml2::StrPair::~StrPair": {"offset": "0x26B80"}, "tinyxml2::XMLComment::Accept": {"offset": "0x27220"}, "tinyxml2::XMLComment::ParseDeep": {"offset": "0x292F0"}, "tinyxml2::XMLComment::ShallowClone": {"offset": "0x2A160"}, "tinyxml2::XMLComment::ShallowEqual": {"offset": "0x2A640"}, "tinyxml2::XMLComment::ToComment": {"offset": "0x2A9C0"}, "tinyxml2::XMLDeclaration::Accept": {"offset": "0x27230"}, "tinyxml2::XMLDeclaration::ParseDeep": {"offset": "0x293B0"}, "tinyxml2::XMLDeclaration::ShallowClone": {"offset": "0x2A240"}, "tinyxml2::XMLDeclaration::ShallowEqual": {"offset": "0x2A6E0"}, "tinyxml2::XMLDeclaration::ToDeclaration": {"offset": "0x2A9C0"}, "tinyxml2::XMLDocument::Accept": {"offset": "0x27240"}, "tinyxml2::XMLDocument::Clear": {"offset": "0x27ED0"}, "tinyxml2::XMLDocument::ClearError": {"offset": "0x27F60"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLComment,104>": {"offset": "0x26540"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLDeclaration,104>": {"offset": "0x265C0"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLElement,120>": {"offset": "0x26640"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLText,112>": {"offset": "0x266D0"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLUnknown,104>": {"offset": "0x26760"}, "tinyxml2::XMLDocument::Identify": {"offset": "0x28820"}, "tinyxml2::XMLDocument::NewText": {"offset": "0x28B60"}, "tinyxml2::XMLDocument::Parse": {"offset": "0x28DD0"}, "tinyxml2::XMLDocument::SetError": {"offset": "0x29F40"}, "tinyxml2::XMLDocument::ShallowClone": {"offset": "0xB5A0"}, "tinyxml2::XMLDocument::ShallowEqual": {"offset": "0xB660"}, "tinyxml2::XMLDocument::ToDocument": {"offset": "0x2A9C0"}, "tinyxml2::XMLDocument::XMLDocument": {"offset": "0x267E0"}, "tinyxml2::XMLDocument::~XMLDocument": {"offset": "0x26BC0"}, "tinyxml2::XMLElement::Accept": {"offset": "0x272B0"}, "tinyxml2::XMLElement::Attribute": {"offset": "0x27D30"}, "tinyxml2::XMLElement::GetText": {"offset": "0x287D0"}, "tinyxml2::XMLElement::IntText": {"offset": "0x28AB0"}, "tinyxml2::XMLElement::ParseAttributes": {"offset": "0x28F20"}, "tinyxml2::XMLElement::ParseDeep": {"offset": "0x29470"}, "tinyxml2::XMLElement::ShallowClone": {"offset": "0x2A330"}, "tinyxml2::XMLElement::ShallowEqual": {"offset": "0x2A780"}, "tinyxml2::XMLElement::ToElement": {"offset": "0x2A9C0"}, "tinyxml2::XMLNode::DeleteChild": {"offset": "0x27FE0"}, "tinyxml2::XMLNode::DeleteChildren": {"offset": "0x280C0"}, "tinyxml2::XMLNode::DeleteNode": {"offset": "0x281F0"}, "tinyxml2::XMLNode::FirstChildElement": {"offset": "0x28280"}, "tinyxml2::XMLNode::InsertChildPreamble": {"offset": "0x289F0"}, "tinyxml2::XMLNode::NextSiblingElement": {"offset": "0x28C10"}, "tinyxml2::XMLNode::ParseDeep": {"offset": "0x29530"}, "tinyxml2::XMLNode::ToComment": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::ToDeclaration": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::ToDocument": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::ToElement": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::ToText": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::ToUnknown": {"offset": "0xB5A0"}, "tinyxml2::XMLNode::Value": {"offset": "0x2A9D0"}, "tinyxml2::XMLNode::~XMLNode": {"offset": "0x26CA0"}, "tinyxml2::XMLText::Accept": {"offset": "0x27320"}, "tinyxml2::XMLText::ParseDeep": {"offset": "0x29B00"}, "tinyxml2::XMLText::ShallowClone": {"offset": "0x2A510"}, "tinyxml2::XMLText::ShallowEqual": {"offset": "0x2A880"}, "tinyxml2::XMLText::ToText": {"offset": "0x2A9C0"}, "tinyxml2::XMLUnknown::Accept": {"offset": "0x27330"}, "tinyxml2::XMLUnknown::ParseDeep": {"offset": "0x29C90"}, "tinyxml2::XMLUnknown::ShallowClone": {"offset": "0x2A560"}, "tinyxml2::XMLUnknown::ShallowEqual": {"offset": "0x2A920"}, "tinyxml2::XMLUnknown::ToUnknown": {"offset": "0x2A9C0"}, "tinyxml2::XMLUtil::GetCharacterRef": {"offset": "0x28340"}, "utf8::exception::exception": {"offset": "0x25040"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x249F0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x250D0"}, "utf8::invalid_code_point::what": {"offset": "0x25EB0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0x9790"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x25140"}, "utf8::invalid_utf8::what": {"offset": "0x25EC0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0x9790"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x251A0"}, "utf8::not_enough_room::what": {"offset": "0x25ED0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0x9790"}, "vfs::FindData::~FindData": {"offset": "0x96C0"}, "xbr::GetGameBuild": {"offset": "0xE140"}, "xbr::GetReplaceExecutableInit": {"offset": "0x25F80"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x261A0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1800"}}}