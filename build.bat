@echo off
echo Building FiveM Offset Dumper...

:: Create build directory
if not exist "build" mkdir build
cd build

:: Configure with CMake
echo Configuring project...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

:: Build the project
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Output: build\bin\Release\FiveMOffsetDumper.dll

:: Copy to root directory for convenience
if exist "bin\Release\FiveMOffsetDumper.dll" (
    copy "bin\Release\FiveMOffsetDumper.dll" "..\FiveMOffsetDumper.dll"
    echo DLL copied to root directory.
)

pause
