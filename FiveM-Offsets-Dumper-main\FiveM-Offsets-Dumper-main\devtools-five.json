{"devtools-five.dll": {"<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x14DD0"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x14DD0"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xB260"}, "AddGtxdRelationship": {"offset": "0x30C10"}, "CTxdRelationship::~CTxdRelationship": {"offset": "0x2F0B0"}, "CfxState::CfxState": {"offset": "0x24240"}, "Component::As": {"offset": "0xF750"}, "Component::IsA": {"offset": "0x18810"}, "Component::SetCommandLine": {"offset": "0xB140"}, "Component::SetUserData": {"offset": "0x18820"}, "ComponentInstance::DoGameLoad": {"offset": "0x187F0"}, "ComponentInstance::Initialize": {"offset": "0x18800"}, "ComponentInstance::Shutdown": {"offset": "0x18820"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x17B80"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0xFCD0"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0xFEB0"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x15140"}, "ConsoleFlagsToString": {"offset": "0x173B0"}, "CoreGetComponentRegistry": {"offset": "0x30E30"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x30EC0"}, "CountDependencyMemory": {"offset": "0x1DAD0"}, "CreateComponent": {"offset": "0x18830"}, "CreateVariableEntry<bool>": {"offset": "0x105F0"}, "DllMain": {"offset": "0x4443C"}, "DoNtRaiseException": {"offset": "0x3BE20"}, "FatalErrorNoExceptRealV": {"offset": "0xC810"}, "FatalErrorRealV": {"offset": "0xC840"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x2F70"}, "GetAbsoluteCitPath": {"offset": "0x3A1F0"}, "GetFriendlyNameFromBucketHash": {"offset": "0x177F0"}, "GlobalErrorHandler": {"offset": "0xCA80"}, "HookFunction::Run": {"offset": "0xF900"}, "HookFunctionBase::Register": {"offset": "0x3CC60"}, "HookFunctionBase::RunAll": {"offset": "0x3CC80"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x23F20"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x39A10"}, "ImGui::ListViewBase::GetTextFromCellFieldDataPtr": {"offset": "0x32430"}, "ImGui::ListViewBase::render": {"offset": "0x32E30"}, "ImGui::ListViewBase::sort": {"offset": "0x18810"}, "ImGui::ListViewBase::updateHeaderData": {"offset": "0x34550"}, "ImGui::ListViewBase::~ListViewBase": {"offset": "0x1B7B0"}, "InitFunction::Run": {"offset": "0xF1E0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x3BBC0"}, "InitFunctionBase::Register": {"offset": "0x3BF90"}, "InitFunctionBase::RunAll": {"offset": "0x3BFE0"}, "Instance<ICoreGameInit>::Get": {"offset": "0x219A0"}, "MakeIcon": {"offset": "0x1DB90"}, "MakeIconDatas": {"offset": "0x1DE90"}, "MakeIcons": {"offset": "0x1E080"}, "MakeRelativeCitPath": {"offset": "0xD120"}, "Microsoft::WRL::ComPtr<IFileDialog>::~ComPtr<IFileDialog>": {"offset": "0x1B5E0"}, "Microsoft::WRL::ComPtr<IShellItem>::~ComPtr<IShellItem>": {"offset": "0x1B5E0"}, "Microsoft::WRL::ComPtr<IStream>::~ComPtr<IStream>": {"offset": "0x1B5E0"}, "Microsoft::WRL::ComPtr<IWICBitmapDecoder>::~ComPtr<IWICBitmapDecoder>": {"offset": "0x1B5E0"}, "Microsoft::WRL::ComPtr<IWICBitmapFrameDecode>::~ComPtr<IWICBitmapFrameDecode>": {"offset": "0x1B5E0"}, "Microsoft::WRL::ComPtr<IWICBitmapSource>::~ComPtr<IWICBitmapSource>": {"offset": "0x1B5E0"}, "OpenFileBrowser": {"offset": "0x30F50"}, "RaiseDebugException": {"offset": "0x3BF00"}, "RecreateModel": {"offset": "0x315B0"}, "ScopedCoInitialize::~ScopedCoInitialize": {"offset": "0x2F110"}, "ScopedError::~ScopedError": {"offset": "0xB330"}, "SelectedTimecycle::ResetModifier": {"offset": "0x27B00"}, "SelectedTimecycle::SetModifier": {"offset": "0x27B30"}, "SelectedTimecycle::UpdateVars": {"offset": "0x27D30"}, "SelectedTimecycle::~SelectedTimecycle": {"offset": "0x24440"}, "SortCompare<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10B20"}, "StreamingListView::getCellData": {"offset": "0x18B30"}, "StreamingListView::getHeaderData": {"offset": "0x18A40"}, "StreamingListView::getNumColumns": {"offset": "0x18A20"}, "StreamingListView::getNumRows": {"offset": "0x18A30"}, "StreamingListView::updateCount": {"offset": "0x20410"}, "StreamingListView::~StreamingListView": {"offset": "0x1B7F0"}, "StreamingProgress_OnDownload": {"offset": "0x21A00"}, "StreamingProgress_Update": {"offset": "0x222C0"}, "SysError": {"offset": "0xD6A0"}, "TIXML_SNPRINTF": {"offset": "0x427D0"}, "TimecycleEditor::Draw": {"offset": "0x24F10"}, "TimecycleEditor::RebuildSearchCache": {"offset": "0x27800"}, "TimecycleEditor::ShouldUseSearchCache": {"offset": "0x27CF0"}, "ToNarrow": {"offset": "0x3C010"}, "ToWide": {"offset": "0x3C100"}, "TraceRealV": {"offset": "0x3C410"}, "Win32TrapAndJump64": {"offset": "0x3DAB0"}, "_DllMainCRTStartup": {"offset": "0x43DF0"}, "_Init_thread_abort": {"offset": "0x43164"}, "_Init_thread_footer": {"offset": "0x43194"}, "_Init_thread_header": {"offset": "0x431F4"}, "_Init_thread_notify": {"offset": "0x4325C"}, "_Init_thread_wait": {"offset": "0x432A0"}, "_RTC_Initialize": {"offset": "0x44484"}, "_RTC_Terminate": {"offset": "0x444C0"}, "_Smtx_lock_exclusive": {"offset": "0x42FE4"}, "_Smtx_lock_shared": {"offset": "0x42FEC"}, "_Smtx_unlock_exclusive": {"offset": "0x42FF4"}, "_Smtx_unlock_shared": {"offset": "0x42FFC"}, "__ArrayUnwind": {"offset": "0x43A64"}, "__GSHandlerCheck": {"offset": "0x43880"}, "__GSHandlerCheckCommon": {"offset": "0x438A0"}, "__GSHandlerCheck_EH": {"offset": "0x438FC"}, "__GSHandlerCheck_SEH": {"offset": "0x43FAC"}, "__crt_debugger_hook": {"offset": "0x441E0"}, "__dyn_tls_init": {"offset": "0x436C8"}, "__dyn_tls_on_demand_init": {"offset": "0x43730"}, "__isa_available_init": {"offset": "0x44034"}, "__local_stdio_printf_options": {"offset": "0xF100"}, "__local_stdio_scanf_options": {"offset": "0x42C60"}, "__raise_securityfailure": {"offset": "0x43E30"}, "__report_gsfailure": {"offset": "0x43E64"}, "__scrt_acquire_startup_lock": {"offset": "0x43348"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x43384"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x433B8"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x433D0"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x433F8"}, "__scrt_dllmain_exception_filter": {"offset": "0x43410"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x43470"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x434A0"}, "__scrt_fastfail": {"offset": "0x441E8"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x4447C"}, "__scrt_initialize_crt": {"offset": "0x434B4"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x44460"}, "__scrt_initialize_onexit_tables": {"offset": "0x43500"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x4306C"}, "__scrt_initialize_type_info": {"offset": "0x439DC"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x4358C"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x45C8C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x44384"}, "__scrt_release_startup_lock": {"offset": "0x43624"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x18820"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x18820"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x18820"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x18820"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x18820"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xF750"}, "__scrt_throw_std_bad_alloc": {"offset": "0x44354"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xDEA0"}, "__scrt_uninitialize_crt": {"offset": "0x43648"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x4313C"}, "__scrt_uninitialize_type_info": {"offset": "0x439EC"}, "__security_check_cookie": {"offset": "0x43990"}, "__security_init_cookie": {"offset": "0x44390"}, "__std_find_trivial_1": {"offset": "0x42E20"}, "__std_find_trivial_2": {"offset": "0x42EF0"}, "_get_startup_argv_mode": {"offset": "0x4437C"}, "_guard_check_icall_nop": {"offset": "0xB140"}, "_guard_dispatch_icall_nop": {"offset": "0x44630"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x44650"}, "_onexit": {"offset": "0x43674"}, "_snprintf": {"offset": "0x34780"}, "_wwassert": {"offset": "0x3A4F0"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x45CD0"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x45D2F"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x45D46"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x45D5F"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x45D73"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x19F0"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x1A20"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x1A50"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x1EA0"}, "`dynamic initializer for '_init_instance_33''": {"offset": "0x20D0"}, "`dynamic initializer for '_init_instance_34''": {"offset": "0x2100"}, "`dynamic initializer for '_init_instance_35''": {"offset": "0x2130"}, "`dynamic initializer for '_init_instance_68''": {"offset": "0x1760"}, "`dynamic initializer for '_init_instance_69''": {"offset": "0x1790"}, "`dynamic initializer for '_init_instance_70''": {"offset": "0x17C0"}, "`dynamic initializer for '_init_instance_71''": {"offset": "0x17F0"}, "`dynamic initializer for 'addGtxdRelationship''": {"offset": "0x2160"}, "`dynamic initializer for 'currentModel''": {"offset": "0x21A0"}, "`dynamic initializer for 'currentModelFile''": {"offset": "0x21B0"}, "`dynamic initializer for 'currentTxds''": {"offset": "0x21C0"}, "`dynamic initializer for 'g_downloadDone''": {"offset": "0x1B10"}, "`dynamic initializer for 'g_downloadList''": {"offset": "0x1B50"}, "`dynamic initializer for 'g_downloadProgress''": {"offset": "0x1B90"}, "`dynamic initializer for 'g_nameMap''": {"offset": "0x1BD0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'getLocalPlayerPed''": {"offset": "0x21D0"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1430"}, "`dynamic initializer for 'initFunction''": {"offset": "0x2250"}, "`dynamic initializer for 'initFunction2''": {"offset": "0x2210"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x2470"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x27C0"}, "`dynamic initializer for 'modelLoadAction''": {"offset": "0x2410"}, "`dynamic initializer for 'rage__atHashStringNamespaceSupport__GetString''": {"offset": "0x1630"}, "`dynamic initializer for 'registerArchetype''": {"offset": "0x2420"}, "`dynamic initializer for 'txdLoadAction''": {"offset": "0x2460"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x16A0"}, "`dynamic initializer for 'xbr::virt::Base<15,2802,0,6>::ms_initFunction''": {"offset": "0x1720"}, "`dynamic initializer for 'xbr::virt::Base<18,2802,0,6>::ms_initFunction''": {"offset": "0x1220"}, "`dynamic initializer for 'xbr::virt::Base<21,2802,0,6>::ms_initFunction''": {"offset": "0x1260"}, "`dynamic initializer for 'xbr::virt::Base<23,2802,0,6>::ms_initFunction''": {"offset": "0x2090"}, "`dynamic initializer for 'xbr::virt::Base<26,2802,0,6>::ms_initFunction''": {"offset": "0x12A0"}, "`dynamic initializer for 'xbr::virt::Base<29,2802,0,6>::ms_initFunction''": {"offset": "0x12E0"}, "`dynamic initializer for 'xbr::virt::Base<32,2802,0,6>::ms_initFunction''": {"offset": "0x1320"}, "`dynamic initializer for 'xbr::virt::Base<37,2802,0,6>::ms_initFunction''": {"offset": "0x1360"}, "`dynamic initializer for 'xbr::virt::Base<4,2802,2,6>::ms_initFunction''": {"offset": "0x11E0"}, "`dynamic initializer for 'xbr::virt::Base<41,2802,0,6>::ms_initFunction''": {"offset": "0x16E0"}, "`dynamic initializer for 'xbr::virt::Base<5,2802,2,6>::ms_initFunction''": {"offset": "0x1DD0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x151D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x151D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x151F0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x151F0"}, "atexit": {"offset": "0x436B0"}, "boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_iequal>::~first_finderF<char const *,boost::algorithm::is_iequal>": {"offset": "0x24390"}, "capture_previous_context": {"offset": "0x43F38"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x108D0"}, "console::Printfv": {"offset": "0x17C70"}, "dllmain_crt_dispatch": {"offset": "0x43AD0"}, "dllmain_crt_process_attach": {"offset": "0x43B20"}, "dllmain_crt_process_detach": {"offset": "0x43C38"}, "dllmain_dispatch": {"offset": "0x43CBC"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xE4E0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xB200"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x39160"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x38500"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x32260"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x2F020"}, "fmt::v8::detail::add_compare": {"offset": "0x388D0"}, "fmt::v8::detail::assert_fail": {"offset": "0x38A10"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x38A60"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x38C30"}, "fmt::v8::detail::bigint::square": {"offset": "0x39530"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x38500"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x3A10"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x397F0"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x28D00"}, "fmt::v8::detail::compare": {"offset": "0x38B90"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x28DC0"}, "fmt::v8::detail::count_digits": {"offset": "0xE2C0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x34DA0"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x34EB0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x3AC0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x39030"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x36D20"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x39310"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x36D50"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x37B20"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x376F0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x39290"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x34F60"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x34F60"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x28E50"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x28F10"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x38FC0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x3AF0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x36410"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3DC0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x362F0"}, "fmt::v8::detail::format_float<double>": {"offset": "0x347E0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x36550"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x3EA0"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x28F90"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x368B0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3FC0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x3FC0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x4120"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x29100"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x4380"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x29380"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xF050"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x32380"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x36F60"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x371E0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x37470"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x375E0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xB260"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xB260"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x4450"}, "fmt::v8::detail::utf8_decode": {"offset": "0xEE90"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5A50"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2A750"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x6A50"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x6600"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x6E90"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x38270"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x38150"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x6530"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B490"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x2B9B0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x2B560"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x2BDF0"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2C230"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2C370"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x72D0"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2C4B0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x7310"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2C5A0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x74A0"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x2C750"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x7E60"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x7870"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2D280"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x2CC60"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xADF0"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0xADF0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x8590"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x2D8A0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x89B0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x8B80"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x8D20"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x8D20"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x2DD30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8EB0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x90D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x9250"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0xA9E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x93E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x9600"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x98A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x9AC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x9C40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x9E60"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0xA080"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0xA200"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0xA420"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0xA5A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0xA7C0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x2DEB0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x2E050"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x2E1F0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x2E380"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x2E520"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x2E6C0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x2E860"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x2EA10"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x2EBB0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xAB70"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2ED50"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x2EF40"}, "fmt::v8::format_error::format_error": {"offset": "0xAFF0"}, "fmt::v8::format_error::~format_error": {"offset": "0xB390"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x3B1F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x48C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x298E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x46A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x296B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4570"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x295A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4480"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x29470"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x48C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x298E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4790"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x297B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x49F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x29A20"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5550"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x2A4D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4F80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x29F70"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x14A30"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<wchar_t>,wchar_t>": {"offset": "0x2B130"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x3BA90"}, "fprintf": {"offset": "0xF110"}, "fwEvent<>::ConnectInternal": {"offset": "0x171E0"}, "fwEvent<bool *>::ConnectInternal": {"offset": "0x171E0"}, "fwEvent<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::ConnectInternal": {"offset": "0x171E0"}, "fwPlatformString::~fwPlatformString": {"offset": "0xB3B0"}, "fwRefContainer<resources::ResourceCacheDeviceV2>::~fwRefContainer<resources::ResourceCacheDeviceV2>": {"offset": "0x1B6B0"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0x1B6B0"}, "fwRefContainer<vfs::Stream>::~fwRefContainer<vfs::Stream>": {"offset": "0x1B6B0"}, "fwRefCountable::AddRef": {"offset": "0x3DA70"}, "fwRefCountable::Release": {"offset": "0x3DA80"}, "fwRefCountable::~fwRefCountable": {"offset": "0x3DA60"}, "fx::client::GetPureLevel": {"offset": "0x274B0"}, "hook::TransformPattern": {"offset": "0x3D8B0"}, "hook::details::StubInitFunction::Run": {"offset": "0xFB20"}, "hook::get_pattern<char,35>": {"offset": "0x14590"}, "hook::get_pattern<void,24>": {"offset": "0x14590"}, "hook::get_pattern<void,27>": {"offset": "0x14590"}, "hook::get_pattern<void,41>": {"offset": "0x14590"}, "hook::pattern::EnsureMatches": {"offset": "0x3D250"}, "hook::pattern::Initialize": {"offset": "0x3D5B0"}, "hook::pattern::~pattern": {"offset": "0x15250"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x168B0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x10380"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x16AE0"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x14B50"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0xF3E0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0xF260"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0xF440"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x17E30"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0xF2E0"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0xF450"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x16D40"}, "istrstr": {"offset": "0x283D0"}, "launch::IsSDK": {"offset": "0x276E0"}, "launch::IsSDKGuest": {"offset": "0x27780"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xB070"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xB110"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x2980"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xC1E0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xB140"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xD330"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xD3F0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xD660"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xDB00"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xB150"}, "rapidjson::internal::DigitGen": {"offset": "0xC490"}, "rapidjson::internal::Grisu2": {"offset": "0xCF40"}, "rapidjson::internal::Prettify": {"offset": "0xD4A0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x3410"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x34E0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xB110"}, "rapidjson::internal::WriteExponent": {"offset": "0xDA70"}, "rapidjson::internal::u32toa": {"offset": "0xE5D0"}, "rapidjson::internal::u64toa": {"offset": "0xE840"}, "se::Object::~Object": {"offset": "0xB260"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x24370"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB180"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,rage::tcModData>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,rage::tcModData>,void *> > >": {"offset": "0x1B610"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress>,void *> > >": {"offset": "0x210D0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> > >": {"offset": "0x210F0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xB180"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData>,void *> > >": {"offset": "0x1B630"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,void *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,void *>,void *> > >": {"offset": "0x1B610"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<unsigned int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<unsigned int,void *> > >": {"offset": "0x24370"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x28A40"}, "std::_Facet_Register": {"offset": "0x43004"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x14DD0"}, "std::_Func_class<bool,bool *>::~_Func_class<bool,bool *>": {"offset": "0x14DD0"}, "std::_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::~_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>": {"offset": "0x14DD0"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x14DD0"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x14DD0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x14DD0"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Copy": {"offset": "0x20700"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Do_call": {"offset": "0x20720"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Move": {"offset": "0x20700"}, "std::_Func_impl_no_alloc<<lambda_07757b8ce6f669b9b3a8176e64413b14>,bool>::_Target_type": {"offset": "0x20740"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Copy": {"offset": "0x28640"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Do_call": {"offset": "0x28660"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Move": {"offset": "0x28640"}, "std::_Func_impl_no_alloc<<lambda_1b34a5c8f5a97522944a450c44496369>,bool>::_Target_type": {"offset": "0x28680"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0xFA10"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0xFA30"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0xFA10"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0xFB10"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Copy": {"offset": "0x205C0"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Do_call": {"offset": "0x205E0"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Move": {"offset": "0x205C0"}, "std::_Func_impl_no_alloc<<lambda_337b19683d33a27008a98b0d5c59de32>,void,unsigned int,bool *>::_Target_type": {"offset": "0x20620"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Copy": {"offset": "0x239D0"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Do_call": {"offset": "0x239F0"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Move": {"offset": "0x239D0"}, "std::_Func_impl_no_alloc<<lambda_33aebd0618400408662dae7c1783a69a>,bool>::_Target_type": {"offset": "0x23A10"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Copy": {"offset": "0x23A20"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Do_call": {"offset": "0x23A40"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Move": {"offset": "0x23A20"}, "std::_Func_impl_no_alloc<<lambda_384fe65fd68208789ab7f7b8a305740b>,bool>::_Target_type": {"offset": "0x23BC0"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Copy": {"offset": "0x18910"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Do_call": {"offset": "0x18930"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Move": {"offset": "0x18910"}, "std::_Func_impl_no_alloc<<lambda_45383f09f5826208be97124a034ad501>,bool>::_Target_type": {"offset": "0x18950"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Copy": {"offset": "0x18960"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Do_call": {"offset": "0x18980"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Move": {"offset": "0x18960"}, "std::_Func_impl_no_alloc<<lambda_47c979ec39807107e393b727256d439a>,bool>::_Target_type": {"offset": "0x189A0"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Copy": {"offset": "0x18860"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Do_call": {"offset": "0x18880"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Move": {"offset": "0x18860"}, "std::_Func_impl_no_alloc<<lambda_4efd689e14c189a34c1c9a04a048d207>,bool,bool *>::_Target_type": {"offset": "0x188B0"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Copy": {"offset": "0xF580"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Do_call": {"offset": "0xF5A0"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Move": {"offset": "0xF580"}, "std::_Func_impl_no_alloc<<lambda_5b46d2bc832df4ed48e80cc542fa83a9>,bool>::_Target_type": {"offset": "0xF5C0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xF820"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xF7C0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xF8A0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF750"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xF8F0"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Copy": {"offset": "0x206B0"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Do_call": {"offset": "0x206D0"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Move": {"offset": "0x206B0"}, "std::_Func_impl_no_alloc<<lambda_75f1796dee12ee415d49b679706e0551>,bool>::_Target_type": {"offset": "0x206F0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xF6D0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xF7C0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xF760"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF750"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xF7B0"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Copy": {"offset": "0x188C0"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Do_call": {"offset": "0x188E0"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Move": {"offset": "0x188C0"}, "std::_Func_impl_no_alloc<<lambda_87762298673133163ad5d0d19f932c98>,bool>::_Target_type": {"offset": "0x18900"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Copy": {"offset": "0xF1F0"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Do_call": {"offset": "0xF210"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Move": {"offset": "0xF1F0"}, "std::_Func_impl_no_alloc<<lambda_a113d55ffc10044a2010c0929b7cee8b>,bool,bool *>::_Target_type": {"offset": "0xF230"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Copy": {"offset": "0x20630"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Do_call": {"offset": "0x205E0"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Move": {"offset": "0x20630"}, "std::_Func_impl_no_alloc<<lambda_a6dc5d056be60a3a8635b56c7bf5bc21>,void,unsigned int,strRequestInfo *>::_Target_type": {"offset": "0x20650"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Copy": {"offset": "0x285A0"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Do_call": {"offset": "0x285C0"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Move": {"offset": "0x285A0"}, "std::_Func_impl_no_alloc<<lambda_adfe0e7a908df754693a56cacb73144f>,bool>::_Target_type": {"offset": "0x285E0"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Copy": {"offset": "0x20660"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Do_call": {"offset": "0x20680"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Move": {"offset": "0x20660"}, "std::_Func_impl_no_alloc<<lambda_bc5e25aa680775cfad36693d3999f3c5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64>::_Target_type": {"offset": "0x206A0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0xF910"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0xF990"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0xF970"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xF750"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0xF980"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Copy": {"offset": "0x23960"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Do_call": {"offset": "0x23980"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Move": {"offset": "0x23960"}, "std::_Func_impl_no_alloc<<lambda_db67f5a6675fbac8806d8ea8aa1a60b9>,bool,bool *>::_Target_type": {"offset": "0x239C0"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Copy": {"offset": "0x285F0"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Delete_this": {"offset": "0xF250"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Do_call": {"offset": "0x28610"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Move": {"offset": "0x285F0"}, "std::_Func_impl_no_alloc<<lambda_ed0030b4e8ec1ddca09bde741aec33d6>,bool,bool *>::_Target_type": {"offset": "0x28630"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Copy": {"offset": "0xF470"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Delete_this": {"offset": "0xF570"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Do_call": {"offset": "0xF490"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Get": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Move": {"offset": "0xF470"}, "std::_Func_impl_no_alloc<<lambda_fc008e85a42e986d8e066284011a7cfe>,void,unsigned int,atArray<rage::fwArchetype> *>::_Target_type": {"offset": "0xF560"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x10F50"}, "std::_Maklocstr<char>": {"offset": "0xF160"}, "std::_Maklocstr<wchar_t>": {"offset": "0x34C90"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xF750"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0xF680"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0xF5D0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x200A0"}, "std::_Throw_bad_array_new_length": {"offset": "0xDEA0"}, "std::_Throw_bad_cast": {"offset": "0x28220"}, "std::_Throw_tree_length_error": {"offset": "0xDEC0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x384C0"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x384C0"}, "std::_Traits_compare<std::char_traits<char> >": {"offset": "0x20ED0"}, "std::_Tree<std::_Tmap_traits<int,rage::tcModData,std::less<int>,std::allocator<std::pair<int const ,rage::tcModData> >,0> >::_Erase": {"offset": "0x27F00"}, "std::_Tree<std::_Tmap_traits<int,rage::tcModData,std::less<int>,std::allocator<std::pair<int const ,rage::tcModData> >,0> >::clear": {"offset": "0x28240"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned __int64,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x20C50"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x36B0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3930"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xB1A0"}, "std::_Tree<std::_Tmap_traits<unsigned int,void *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,void *> >,0> >::_Find_hint<unsigned int>": {"offset": "0x19630"}, "std::_Tree<std::_Tmap_traits<unsigned int,void *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,void *> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,void *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,void *> >,0> >": {"offset": "0x1B650"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Erase": {"offset": "0x28090"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::clear": {"offset": "0x282B0"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::erase": {"offset": "0x28320"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::insert<0,0>": {"offset": "0x23D20"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x20C50"}, "std::_Tree<std::_Tset_traits<unsigned int,std::less<unsigned int>,std::allocator<unsigned int>,0> >::clear": {"offset": "0x282B0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB180"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress>,void *> > >": {"offset": "0x210D0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> > >": {"offset": "0x210F0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Erase_tree<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x23CC0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Extract": {"offset": "0x233E0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Lrotate": {"offset": "0x237B0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Rrotate": {"offset": "0x23880"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x20B90"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,rage::tcModData> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,rage::tcModData>,void *> > >": {"offset": "0x19570"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,rage::tcModData> > >::_Extract": {"offset": "0x233E0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,rage::tcModData> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,rage::tcModData> > >::_Lrotate": {"offset": "0x237B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,rage::tcModData> > >::_Rrotate": {"offset": "0x23880"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress>,void *> > >": {"offset": "0x20990"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x20A50"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> > >": {"offset": "0x20AD0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> > >::_Extract": {"offset": "0x233E0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> > >::_Lrotate": {"offset": "0x237B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64> > >::_Rrotate": {"offset": "0x23880"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x3650"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData>,void *> > >": {"offset": "0x195D0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,void *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,void *>,void *> > >": {"offset": "0x19570"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,void *> > >::_Insert_node": {"offset": "0xDC40"}, "std::_Tree_val<std::_Tree_simple_types<unsigned int> >::_Erase_tree<std::allocator<std::_Tree_node<unsigned int,void *> > >": {"offset": "0x23CC0"}, "std::_Tree_val<std::_Tree_simple_types<unsigned int> >::_Insert_node": {"offset": "0xDC40"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x28C80"}, "std::_Xlen_string": {"offset": "0xDEE0"}, "std::allocator<char>::allocate": {"offset": "0xDF00"}, "std::allocator<char>::deallocate": {"offset": "0x3C8B0"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x20220"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x203C0"}, "std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>,void *> >::allocate": {"offset": "0x238E0"}, "std::allocator<std::_Tree_node<std::pair<unsigned int const ,void *>,void *> >::allocate": {"offset": "0x20130"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x32150"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x321C0"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x20220"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x203C0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x3C8B0"}, "std::allocator<unsigned int>::allocate": {"offset": "0x200C0"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x20320"}, "std::allocator<wchar_t>::allocate": {"offset": "0xDF60"}, "std::bad_alloc::bad_alloc": {"offset": "0x44334"}, "std::bad_alloc::~bad_alloc": {"offset": "0xB390"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xAF80"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xB390"}, "std::bad_cast::bad_cast": {"offset": "0x24340"}, "std::bad_cast::~bad_cast": {"offset": "0xB390"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x15210"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x15350"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x3590"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x3A850"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x13950"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x3A9C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x20D20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x20290"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xE150"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1B430"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xB260"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x34BC0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xB2C0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xDFD0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x39940"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x3C8F0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x3CA50"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xB2C0"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x144C0"}, "std::exception::exception": {"offset": "0xAFB0"}, "std::exception::what": {"offset": "0xF030"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x14DD0"}, "std::function<bool __cdecl(bool *)>::~function<bool __cdecl(bool *)>": {"offset": "0x14DD0"}, "std::function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64)>::~function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,unsigned __int64,unsigned __int64)>": {"offset": "0x14DD0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x14DD0"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x14DD0"}, "std::function<void __cdecl(unsigned int,atArray<rage::fwArchetype> *)>::~function<void __cdecl(unsigned int,atArray<rage::fwArchetype> *)>": {"offset": "0x14DD0"}, "std::function<void __cdecl(unsigned int,bool *)>::~function<void __cdecl(unsigned int,bool *)>": {"offset": "0x14DD0"}, "std::function<void __cdecl(unsigned int,strRequestInfo *)>::~function<void __cdecl(unsigned int,strRequestInfo *)>": {"offset": "0x14DD0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x14DD0"}, "std::locale::~locale": {"offset": "0x24550"}, "std::map<int,rage::tcModData,std::less<int>,std::allocator<std::pair<int const ,rage::tcModData> > >::~map<int,rage::tcModData,std::less<int>,std::allocator<std::pair<int const ,rage::tcModData> > >": {"offset": "0x1B650"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,StreamingDownloadProgress,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,StreamingDownloadProgress> > >::_Try_emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x20F30"}, "std::map<unsigned int,ImGui::ListViewBase::CellData::IconData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData> > >::~map<unsigned int,ImGui::ListViewBase::CellData::IconData,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,ImGui::ListViewBase::CellData::IconData> > >": {"offset": "0x1B6F0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x38E40"}, "std::numpunct<char>::do_falsename": {"offset": "0x38E60"}, "std::numpunct<char>::do_grouping": {"offset": "0x38EE0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x38F20"}, "std::numpunct<char>::do_truename": {"offset": "0x38F40"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x38310"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x386E0"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x38E50"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x38EA0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x38EE0"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x38F30"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x38F80"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x21110"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned __int64>": {"offset": "0xB260"}, "std::runtime_error::runtime_error": {"offset": "0xB030"}, "std::set<int,std::less<int>,std::allocator<int> >::~set<int,std::less<int>,std::allocator<int> >": {"offset": "0x243C0"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x211C0"}, "std::set<unsigned int,std::less<unsigned int>,std::allocator<unsigned int> >::~set<unsigned int,std::less<unsigned int>,std::allocator<unsigned int> >": {"offset": "0x243C0"}, "std::shared_lock<std::shared_mutex>::~shared_lock<std::shared_mutex>": {"offset": "0x211F0"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x15010"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x15010"}, "std::thread::_Invoke<std::tuple<<lambda_b4e442f4e439fa0a34f12c716cd81aee> >,0>": {"offset": "0x28C00"}, "std::thread::_Invoke<std::tuple<<lambda_d8aa73829cc0fcdc13eab760311416fe> >,0>": {"offset": "0x28C40"}, "std::thread::detach": {"offset": "0x32210"}, "std::thread::~thread": {"offset": "0x2F120"}, "std::to_string": {"offset": "0x185C0"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0x21200"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x15060"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x24420"}, "std::unique_ptr<std::tuple<<lambda_b4e442f4e439fa0a34f12c716cd81aee> >,std::default_delete<std::tuple<<lambda_b4e442f4e439fa0a34f12c716cd81aee> > > >::~unique_ptr<std::tuple<<lambda_b4e442f4e439fa0a34f12c716cd81aee> >,std::default_delete<std::tuple<<lambda_b4e442f4e439fa0a34f12c716cd81aee> > > >": {"offset": "0x2F080"}, "std::unique_ptr<std::tuple<<lambda_d8aa73829cc0fcdc13eab760311416fe> >,std::default_delete<std::tuple<<lambda_d8aa73829cc0fcdc13eab760311416fe> > > >::~unique_ptr<std::tuple<<lambda_d8aa73829cc0fcdc13eab760311416fe> >,std::default_delete<std::tuple<<lambda_d8aa73829cc0fcdc13eab760311416fe> > > >": {"offset": "0x2F080"}, "std::use_facet<std::ctype<char> >": {"offset": "0x23E30"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x37EF0"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x38060"}, "tinyxml2::DynArray<char const *,10>::~DynArray<char const *,10>": {"offset": "0x3E100"}, "tinyxml2::DynArray<char,20>::~DynArray<char,20>": {"offset": "0x3E100"}, "tinyxml2::DynArray<tinyxml2::XMLNode *,10>::Push": {"offset": "0x41760"}, "tinyxml2::MemPoolT<104>::Alloc": {"offset": "0x3EDD0"}, "tinyxml2::MemPoolT<104>::Clear": {"offset": "0x3F520"}, "tinyxml2::MemPoolT<104>::Free": {"offset": "0x3FB40"}, "tinyxml2::MemPoolT<104>::ItemSize": {"offset": "0x40300"}, "tinyxml2::MemPoolT<104>::SetTracked": {"offset": "0x41E90"}, "tinyxml2::MemPoolT<104>::~MemPoolT<104>": {"offset": "0x3E1A0"}, "tinyxml2::MemPoolT<112>::Alloc": {"offset": "0x3F040"}, "tinyxml2::MemPoolT<112>::Clear": {"offset": "0x3F570"}, "tinyxml2::MemPoolT<112>::Free": {"offset": "0x3FB40"}, "tinyxml2::MemPoolT<112>::ItemSize": {"offset": "0x40310"}, "tinyxml2::MemPoolT<112>::SetTracked": {"offset": "0x41E90"}, "tinyxml2::MemPoolT<112>::~MemPoolT<112>": {"offset": "0x3E220"}, "tinyxml2::MemPoolT<120>::Alloc": {"offset": "0x3F290"}, "tinyxml2::MemPoolT<120>::Clear": {"offset": "0x3F4D0"}, "tinyxml2::MemPoolT<120>::Free": {"offset": "0x3FB40"}, "tinyxml2::MemPoolT<120>::ItemSize": {"offset": "0x40320"}, "tinyxml2::MemPoolT<120>::SetTracked": {"offset": "0x41E90"}, "tinyxml2::MemPoolT<120>::~MemPoolT<120>": {"offset": "0x3E2A0"}, "tinyxml2::MemPoolT<80>::Alloc": {"offset": "0x3EAE0"}, "tinyxml2::MemPoolT<80>::Clear": {"offset": "0x3F4D0"}, "tinyxml2::MemPoolT<80>::Free": {"offset": "0x3FB40"}, "tinyxml2::MemPoolT<80>::ItemSize": {"offset": "0x402F0"}, "tinyxml2::MemPoolT<80>::SetTracked": {"offset": "0x41E90"}, "tinyxml2::MemPoolT<80>::~MemPoolT<80>": {"offset": "0x3E120"}, "tinyxml2::StrPair::GetStr": {"offset": "0x3FD80"}, "tinyxml2::StrPair::ParseName": {"offset": "0x412C0"}, "tinyxml2::StrPair::Set": {"offset": "0x419B0"}, "tinyxml2::StrPair::SetStr": {"offset": "0x41D40"}, "tinyxml2::StrPair::~StrPair": {"offset": "0x3E320"}, "tinyxml2::XMLAttribute::SetAttribute": {"offset": "0x41BC0"}, "tinyxml2::XMLComment::Accept": {"offset": "0x3E9C0"}, "tinyxml2::XMLComment::ParseDeep": {"offset": "0x40860"}, "tinyxml2::XMLComment::ShallowClone": {"offset": "0x41F70"}, "tinyxml2::XMLComment::ShallowEqual": {"offset": "0x42450"}, "tinyxml2::XMLComment::ToComment": {"offset": "0x42840"}, "tinyxml2::XMLDeclaration::Accept": {"offset": "0x3E9D0"}, "tinyxml2::XMLDeclaration::ParseDeep": {"offset": "0x40920"}, "tinyxml2::XMLDeclaration::ShallowClone": {"offset": "0x42050"}, "tinyxml2::XMLDeclaration::ShallowEqual": {"offset": "0x424F0"}, "tinyxml2::XMLDeclaration::ToDeclaration": {"offset": "0x42840"}, "tinyxml2::XMLDocument::Accept": {"offset": "0x3E9E0"}, "tinyxml2::XMLDocument::Clear": {"offset": "0x3F5C0"}, "tinyxml2::XMLDocument::ClearError": {"offset": "0x3F650"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLComment,104>": {"offset": "0x3DAD0"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLDeclaration,104>": {"offset": "0x3DB50"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLElement,120>": {"offset": "0x3DBD0"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLText,112>": {"offset": "0x3DC60"}, "tinyxml2::XMLDocument::CreateUnlinkedNode<tinyxml2::XMLUnknown,104>": {"offset": "0x3DCF0"}, "tinyxml2::XMLDocument::Identify": {"offset": "0x3FFF0"}, "tinyxml2::XMLDocument::NewElement": {"offset": "0x40330"}, "tinyxml2::XMLDocument::NewText": {"offset": "0x403E0"}, "tinyxml2::XMLDocument::Print": {"offset": "0x413A0"}, "tinyxml2::XMLDocument::SetError": {"offset": "0x41BD0"}, "tinyxml2::XMLDocument::ShallowClone": {"offset": "0xF750"}, "tinyxml2::XMLDocument::ShallowEqual": {"offset": "0x18810"}, "tinyxml2::XMLDocument::ToDocument": {"offset": "0x42840"}, "tinyxml2::XMLDocument::XMLDocument": {"offset": "0x3DD70"}, "tinyxml2::XMLDocument::~XMLDocument": {"offset": "0x3E360"}, "tinyxml2::XMLElement::Accept": {"offset": "0x3EA50"}, "tinyxml2::XMLElement::FindOrCreateAttribute": {"offset": "0x3FA40"}, "tinyxml2::XMLElement::ParseAttributes": {"offset": "0x40490"}, "tinyxml2::XMLElement::ParseDeep": {"offset": "0x409E0"}, "tinyxml2::XMLElement::SetText": {"offset": "0x41DE0"}, "tinyxml2::XMLElement::ShallowClone": {"offset": "0x42140"}, "tinyxml2::XMLElement::ShallowEqual": {"offset": "0x42590"}, "tinyxml2::XMLElement::ToElement": {"offset": "0x42840"}, "tinyxml2::XMLNode::DeleteChild": {"offset": "0x3F7A0"}, "tinyxml2::XMLNode::DeleteChildren": {"offset": "0x3F880"}, "tinyxml2::XMLNode::DeleteNode": {"offset": "0x3F9B0"}, "tinyxml2::XMLNode::InsertChildPreamble": {"offset": "0x401C0"}, "tinyxml2::XMLNode::InsertEndChild": {"offset": "0x40280"}, "tinyxml2::XMLNode::ParseDeep": {"offset": "0x40AA0"}, "tinyxml2::XMLNode::SetValue": {"offset": "0x41EA0"}, "tinyxml2::XMLNode::ToComment": {"offset": "0xF750"}, "tinyxml2::XMLNode::ToDeclaration": {"offset": "0xF750"}, "tinyxml2::XMLNode::ToDocument": {"offset": "0xF750"}, "tinyxml2::XMLNode::ToElement": {"offset": "0xF750"}, "tinyxml2::XMLNode::ToText": {"offset": "0xF750"}, "tinyxml2::XMLNode::ToUnknown": {"offset": "0xF750"}, "tinyxml2::XMLNode::~XMLNode": {"offset": "0x3E440"}, "tinyxml2::XMLPrinter::ClearBuffer": {"offset": "0x24D40"}, "tinyxml2::XMLPrinter::CloseElement": {"offset": "0x3F6D0"}, "tinyxml2::XMLPrinter::CompactMode": {"offset": "0x23C00"}, "tinyxml2::XMLPrinter::Print": {"offset": "0x41460"}, "tinyxml2::XMLPrinter::PrintSpace": {"offset": "0x415C0"}, "tinyxml2::XMLPrinter::PrintString": {"offset": "0x41600"}, "tinyxml2::XMLPrinter::PushDeclaration": {"offset": "0x41800"}, "tinyxml2::XMLPrinter::PushHeader": {"offset": "0x41880"}, "tinyxml2::XMLPrinter::PushText": {"offset": "0x41930"}, "tinyxml2::XMLPrinter::Visit": {"offset": "0x429A0"}, "tinyxml2::XMLPrinter::VisitEnter": {"offset": "0x42A70"}, "tinyxml2::XMLPrinter::VisitExit": {"offset": "0x42C30"}, "tinyxml2::XMLPrinter::XMLPrinter": {"offset": "0x3DF10"}, "tinyxml2::XMLPrinter::~XMLPrinter": {"offset": "0x244F0"}, "tinyxml2::XMLText::Accept": {"offset": "0x3EAC0"}, "tinyxml2::XMLText::ParseDeep": {"offset": "0x41070"}, "tinyxml2::XMLText::ShallowClone": {"offset": "0x42320"}, "tinyxml2::XMLText::ShallowEqual": {"offset": "0x42690"}, "tinyxml2::XMLText::ToText": {"offset": "0x42840"}, "tinyxml2::XMLUnknown::Accept": {"offset": "0x3EAD0"}, "tinyxml2::XMLUnknown::ParseDeep": {"offset": "0x41200"}, "tinyxml2::XMLUnknown::ShallowClone": {"offset": "0x42370"}, "tinyxml2::XMLUnknown::ShallowEqual": {"offset": "0x42730"}, "tinyxml2::XMLUnknown::ToUnknown": {"offset": "0x42840"}, "tinyxml2::XMLUtil::GetCharacterRef": {"offset": "0x3FB60"}, "tinyxml2::XMLVisitor::Visit": {"offset": "0x18820"}, "tinyxml2::XMLVisitor::VisitEnter": {"offset": "0x18820"}, "tinyxml2::XMLVisitor::VisitExit": {"offset": "0x18820"}, "tinyxml2::XMLVisitor::~XMLVisitor": {"offset": "0x24540"}, "utf8::exception::exception": {"offset": "0x3BBE0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x3AC60"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x3B590"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x3BC70"}, "utf8::invalid_code_point::what": {"offset": "0x3CC00"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xB390"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x3BCE0"}, "utf8::invalid_utf8::what": {"offset": "0x3CC10"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xB390"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x3BD40"}, "utf8::not_enough_room::what": {"offset": "0x3CC20"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xB390"}, "va<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1B3B0"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x3B370"}, "vva": {"offset": "0x3CBE0"}, "xbr::GetGameBuild": {"offset": "0x17AA0"}, "xbr::GetReplaceExecutableInit": {"offset": "0x3CCB0"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x3CED0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x2290"}}}