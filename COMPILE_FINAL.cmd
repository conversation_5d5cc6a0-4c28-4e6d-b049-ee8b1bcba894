@echo off
echo ========================================
echo   COMPILANDO FIVEM CHEAT FINAL
echo ========================================

cd examples

echo [INFO] Configurando Visual Studio...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

echo [INFO] Compilando simple_cheat_final.cpp...
cl.exe /LD /EHsc simple_cheat_final.cpp /Fe:FiveMCheat_FINAL.dll /link user32.lib kernel32.lib

if exist "FiveMCheat_FINAL.dll" (
    echo.
    echo [OK] ✅ FiveMCheat_FINAL.dll criada com sucesso!
    echo.
    dir FiveMCheat_FINAL.dll | find "FiveMCheat_FINAL.dll"
    echo.
    copy "FiveMCheat_FINAL.dll" "..\FiveMCheat_FINAL.dll" >nul
    echo [OK] ✅ DLL copiada para pasta raiz
    echo.
    echo ========================================
    echo ✅ COMPILACAO CONCLUIDA!
    echo ========================================
    echo.
    echo 📁 FiveMCheat_FINAL.dll - Cheat compilado
    echo.
    echo 🎮 CONTROLES NO FIVEM:
    echo [F1] - Toggle God Mode
    echo [F2] - Toggle Infinite Ammo
    echo.
    echo 🚀 COMO USAR:
    echo 1. Abra o FiveM e entre em um servidor
    echo 2. Use um injetor para injetar FiveMCheat_FINAL.dll
    echo 3. Pressione F1 para God Mode
    echo 4. Pressione F2 para Infinite Ammo
    echo.
) else (
    echo [ERROR] ❌ Falha na compilacao
)

cd ..
pause
