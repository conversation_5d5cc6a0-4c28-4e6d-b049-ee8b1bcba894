/*
 * FiveM Offsets Header
 * Gerado automaticamente dos arquivos JSON do FiveM Offset Dumper
 * 
 * IMPORTANTE: Este<PERSON> são offsets REAIS extraídos automaticamente!
 */

#pragma once
#include <cstdint>

namespace FiveMOffsets {
    // Base do módulo (será definida em runtime)
    extern uintptr_t MODULE_BASE;
    
    namespace Functions {
        // Funções importantes do FiveM/GTA V
        constexpr uintptr_t GetPlayerPed = 0x1639CE8;
        constexpr uintptr_t GetAllPhysicalPlayers = 0x118B6EB;
        constexpr uintptr_t GetNumPhysicalPlayers = 0x118B6E4;
        constexpr uintptr_t GetNetObjById = 0x730315;
        constexpr uintptr_t GetNetObjType = 0xFE0EC3;
        constexpr uintptr_t GetNetworkObject = 0xA4E6E8;
        constexpr uintptr_t GetScriptEntity = 0x163B1A4;
        constexpr uintptr_t DeleteObject = 0xD06020;
        constexpr uintptr_t DeletePedReal = 0xD06058;
        constexpr uintptr_t DeleteVehicle = 0xD9F470;
        constexpr uintptr_t RepairVehicle = 0xD8D278;
        constexpr uintptr_t GiveAmmo = 0xD89B8C;
        constexpr uintptr_t HasModelLoaded = 0x108BEC;
        constexpr uintptr_t MarkAsNoLongerNeeded = 0xA5C41C;
    }
    
    namespace Globals {
        // Variáveis globais importantes
        constexpr uintptr_t g_vehiclePool = 0xD7383C;
        constexpr uintptr_t g_viewportGame = 0xF22595;
        constexpr uintptr_t g_pedDensity = 0x72BFEF;
        constexpr uintptr_t g_vehicleDensity = 0xFDFF5D;
        constexpr uintptr_t g_parkedVehicleDensity = 0xFDFF67;
        constexpr uintptr_t g_pedScenarioDensity = 0x72BFF9;
        constexpr uintptr_t g_ambientPedRange = 0x72C00D;
        constexpr uintptr_t g_ambientVehicleRangeMultiplier = 0xFDFF53;
        constexpr uintptr_t g_fireInstances = 0x32C1DB;
        constexpr uintptr_t g_trainConfigData = 0xF1A032;
    }
    
    namespace Classes {
        // Classes e estruturas importantes
        constexpr uintptr_t CWorld = 0x737F0C;
        constexpr uintptr_t CCamera = 0x29E0E0;
        constexpr uintptr_t CViewPort = 0x2829A;
        constexpr uintptr_t CPedFactory = 0x6AD596;
        constexpr uintptr_t CReplayInterface = 0x1FC0;
    }
    
    namespace Modifiers {
        // Modificadores de gameplay
        constexpr uintptr_t WeaponDamageModifier = 0x415BD3;
        constexpr uintptr_t WeaponDamageModifier2 = 0x10C4D15;
        constexpr uintptr_t WeaponDefenseModifier = 0x11CC431;
        constexpr uintptr_t WeaponDefenseModifier2 = 0x11CC2C4;
        constexpr uintptr_t MeleeWeaponDamageModifier = 0x11CC450;
        constexpr uintptr_t MeleeWeaponDefenseModifier = 0x40CE4A;
        constexpr uintptr_t VehicleDamageModifier = 0xEF6849;
        constexpr uintptr_t VehicleDefenseModifier = 0x412BA8;
        constexpr uintptr_t VehicleTopSpeedModifier = 0xF70CBC;
        constexpr uintptr_t VehicleCheatPowerIncrease = 0xFB4C0A;
        constexpr uintptr_t CurrentWeaponDamageModifier = 0x415BBD;
    }
    
    namespace Flags {
        // Flags e estados
        constexpr uintptr_t InfiniteAmmo = 0x10695C1;
        constexpr uintptr_t NoReload = 0x106957C;
        constexpr uintptr_t IsClone = 0xB4591B;
        constexpr uintptr_t PedFlags = 0x1178A02;
        constexpr uintptr_t Handbrake = 0xF388DE;
        constexpr uintptr_t ExpandedRadar = 0x2E7547;
        constexpr uintptr_t RevealFullMap = 0x2E7565;
    }
    
    namespace UI {
        // Interface e HUD
        constexpr uintptr_t HudComponentsArray = 0x2E3F0B;
        constexpr uintptr_t HudComponentsCount = 0x2E3F42;
        constexpr uintptr_t MaxHudColours = 0xD2A86E;
        constexpr uintptr_t MinimapArray = 0x2D24B7;
        constexpr uintptr_t MinimapIsRect = 0x2F5517;
        constexpr uintptr_t WaypointPoint = 0x2CEF4E;
    }
    
    namespace Player {
        // Offsets dentro da estrutura do jogador (baseados no GTA V)
        constexpr uintptr_t PlayerCoords = 0x90;            // Coordenadas do ped
        constexpr uintptr_t PlayerHealth = 0x280;           // Vida do ped
        constexpr uintptr_t PlayerArmor = 0x14C;            // Armadura do ped
        constexpr uintptr_t PlayerStamina = 0x10B4;         // Stamina
        constexpr uintptr_t PlayerMaxStamina = 0x10C4;      // Stamina máxima
        constexpr uintptr_t PlayerFlags = 0x188;            // Flags do jogador
        constexpr uintptr_t CurrentWeapon = 0x20;           // Arma atual
    }
    
    namespace Vehicle {
        // Offsets dentro da estrutura do veículo (baseados no GTA V)
        constexpr uintptr_t VehicleCoords = 0x90;           // Coordenadas do veículo
        constexpr uintptr_t VehicleSpeed = 0x7C;            // Velocidade
        constexpr uintptr_t VehicleHealth = 0x280;          // Vida do veículo
        constexpr uintptr_t VehicleEngineHealth = 0x908;    // Saúde do motor
        constexpr uintptr_t VehicleGravity = 0xBC;          // Gravidade
        constexpr uintptr_t VehicleHandbrake = 0x9C4;       // Freio de mão
        constexpr uintptr_t VehicleSteeringScale = 0x9CC;   // Escala de direção
    }
    
    namespace Weapon {
        // Offsets dentro da estrutura da arma (baseados no GTA V)
        constexpr uintptr_t WeaponAmmo = 0x18;              // Munição da arma
        constexpr uintptr_t WeaponClipAmmo = 0x1C;          // Munição no pente
        constexpr uintptr_t WeaponRecoil = 0x2C;            // Recuo da arma
        constexpr uintptr_t WeaponSpread = 0x74;            // Dispersão
        constexpr uintptr_t WeaponRange = 0x78;             // Alcance
    }
}

/*
 * COMO USAR:
 * 
 * 1. Compile o cheat: cd examples && build_cheat.bat
 * 2. Injete: SimpleInjector.exe
 * 3. Controles:
 *    - INSERT: Menu
 *    - F1: ESP
 *    - F2: God Mode
 * 
 * Offsets extraídos automaticamente dos arquivos JSON!
 */
