#pragma once
#include <Windows.h>
#include <d3d11.h>
#include <string>
#include <vector>
#include "imgui.h"
#include "imgui_impl_win32.h"
#include "imgui_impl_dx11.h"

// Forward declarations
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

class OffsetDumper;

class GUI {
public:
    GUI();
    ~GUI();
    
    bool Initialize();
    void Shutdown();
    void Render();
    void ToggleVisibility() { m_visible = !m_visible; }
    bool IsVisible() const { return m_visible; }
    
private:
    // Window management
    bool CreateDeviceD3D(HWND hWnd);
    void CleanupDeviceD3D();
    void CreateRenderTarget();
    void CleanupRenderTarget();
    static LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    // GUI rendering functions
    void RenderMainWindow();
    void RenderOffsetList();
    void RenderOffsetDetails();
    void RenderScanControls();
    void RenderExportControls();
    void RenderStatusBar();
    void RenderAboutWindow();
    
    // Utility functions
    void ShowHelpMarker(const char* desc);
    std::string FormatAddress(uintptr_t address);
    ImVec4 GetStatusColor(bool found);
    
    // Window state
    HWND m_hwnd;
    WNDCLASSEX m_wc;
    bool m_visible;
    bool m_initialized;
    
    // DirectX 11
    ID3D11Device* m_pd3dDevice;
    ID3D11DeviceContext* m_pd3dDeviceContext;
    IDXGISwapChain* m_pSwapChain;
    ID3D11RenderTargetView* m_mainRenderTargetView;
    
    // GUI state
    bool m_showAbout;
    int m_selectedOffset;
    char m_exportPath[256];
    char m_customPattern[512];
    
    // Filters
    char m_filterText[256];
    bool m_showFoundOnly;
    bool m_showNotFoundOnly;
    
    static GUI* s_instance;
};
