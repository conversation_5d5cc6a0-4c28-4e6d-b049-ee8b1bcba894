# FiveM Offset Dumper

Uma DLL profissional para fazer dump dos offsets do FiveM com interface gráfica integrada.

## Características

- ✅ Interface gráfica moderna usando Dear ImGui
- ✅ Sistema avançado de pattern scanning
- ✅ Base de dados completa de offsets do FiveM
- ✅ Exportação em múltiplos formatos (JSON, C++ Headers, Cheat Engine)
- ✅ Scanning em tempo real com barra de progresso
- ✅ Filtros e busca de offsets
- ✅ Sistema de memória seguro com proteção contra crashes

## Offsets Suportados

### Player
- LocalPlayer - Ponteiro para o jogador local
- PlayerList - Array de todos os jogadores
- PlayerInfo - Estrutura de informações do jogador
- PlayerCoords - Coordenadas do jogador
- PlayerHealth - Vida do jogador
- PlayerArmor - Armadura do jogador

### Vehicle
- VehiclePool - Pool de veículos
- VehicleCoords - Coordenadas do veículo
- VehicleSpeed - Velocidade do veículo
- VehicleHealth - Vida do veículo
- VehicleGravity - Gravidade do veículo

### World
- WorldPtr - Ponteiro para o mundo/estado do jogo
- GameState - Estado atual do jogo
- TimeScale - Escala de tempo do jogo
- Weather - Clima atual

### Weapon
- WeaponManager - Gerenciador de armas
- CurrentWeapon - Arma atual
- WeaponAmmo - Munição da arma
- WeaponDamage - Multiplicador de dano

### Network
- NetworkManager - Gerenciador de rede
- SessionInfo - Informações da sessão
- PlayerCount - Contagem de jogadores

### Misc
- FrameCount - Contador de frames
- GameVersion - Versão do jogo
- FPS - FPS atual
- MenuState - Estado do menu

## Compilação

### Pré-requisitos
- Visual Studio 2022 (ou 2019)
- CMake 3.16+
- Git

### Passos

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd fivem-offset-dumper
```

2. **Configure Dear ImGui:**
```bash
setup_imgui.bat
```

3. **Compile o projeto:**
```bash
build.bat
```

O arquivo `FiveMOffsetDumper.dll` será gerado na pasta raiz.

## Uso

### Injeção
1. Injete a DLL no processo do FiveM usando seu injetor preferido
2. A DLL irá automaticamente:
   - Alocar um console para debug
   - Inicializar a interface gráfica
   - Carregar a base de dados de offsets

### Controles
- **INSERT** - Alternar visibilidade da interface
- **END** - Sair da DLL

### Interface

#### Scan Controls
- **Scan All Offsets** - Escaneia todos os offsets da base de dados
- **Custom Pattern** - Campo para testar padrões personalizados
- **Filtros** - Filtrar offsets por nome, status (encontrado/não encontrado)

#### Lista de Offsets
- Visualização em tabela com nome, categoria, endereço e status
- Clique em um offset para ver detalhes
- Ordenação por colunas

#### Detalhes do Offset
- Informações completas do offset selecionado
- Botões para copiar endereço/offset
- Opção de re-escanear offsets específicos

#### Exportação
- **File > Export to JSON** - Exporta para formato JSON
- **File > Export to C++ Header** - Gera header C++ com constantes
- **Export to Cheat Engine** - Cria tabela para Cheat Engine

## Estrutura do Projeto

```
src/
├── dllmain.cpp          # Ponto de entrada da DLL
├── OffsetDumper.h/cpp   # Classe principal do dumper
├── PatternScanner.h/cpp # Sistema de pattern scanning
├── OffsetDatabase.h/cpp # Base de dados de offsets
├── GUI.h/cpp           # Interface gráfica
└── MemoryUtils.h/cpp   # Utilitários de memória

external/
└── imgui/              # Dear ImGui (configurado automaticamente)

build/                  # Arquivos de build (gerado)
```

## Formatos de Exportação

### JSON
```json
{
  "offsets": {
    "LocalPlayer": {
      "description": "Pointer to local player entity",
      "category": "Player",
      "found": true,
      "address": "0x7FF123456789",
      "offset": "0x123456"
    }
  },
  "metadata": {
    "module_base": "0x7FF120000000",
    "module_size": "0x2000000",
    "timestamp": "1640995200"
  }
}
```

### C++ Header
```cpp
#pragma once
namespace FiveMOffsets {
    constexpr uintptr_t MODULE_BASE = 0x7FF120000000;
    
    namespace Player {
        constexpr uintptr_t LocalPlayer = 0x123456;
        constexpr uintptr_t PlayerCoords = 0x789ABC;
    }
}
```

## Segurança

- Todas as operações de memória são protegidas contra crashes
- Verificação de permissões de memória antes de leitura/escrita
- Tratamento de exceções em todas as operações críticas
- Scanning seguro com chunks de memória

## Desenvolvimento

### Adicionando Novos Offsets

1. Edite `src/OffsetDatabase.cpp`
2. Adicione o padrão na função apropriada (AddPlayerPatterns, etc.)
3. Recompile o projeto

### Formato de Padrão
```cpp
AddPattern({
    "NomeDoOffset",
    "Descrição do offset",
    "48 8B 05 ? ? ? ? 48 8B 48 08", // Padrão de bytes (? = wildcard)
    "Categoria",
    3,      // Offset adicional
    true    // Se é endereço relativo
});
```

## Aviso Legal

Esta ferramenta é destinada apenas para fins educacionais e de pesquisa. O uso desta ferramenta para trapacear em jogos online pode resultar em banimento. Use por sua própria conta e risco.

## Licença

Este projeto é fornecido "como está" sem garantias. Use responsavelmente.
