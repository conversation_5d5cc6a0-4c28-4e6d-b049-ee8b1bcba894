{"pool-sizes-state.dll": {"<lambda_41af1ba482b752c39807db2ef15caf48>::~<lambda_41af1ba482b752c39807db2ef15caf48>": {"offset": "0x9E40"}, "<lambda_7604da78e8f33a312c72ae5a43f9b93b>::~<lambda_7604da78e8f33a312c72ae5a43f9b93b>": {"offset": "0x17650"}, "<lambda_f17ebabe33bc32be107ce6fff046b802>::~<lambda_f17ebabe33bc32be107ce6fff046b802>": {"offset": "0x17650"}, "AddCrashometryV": {"offset": "0x27260"}, "CfxState::CfxState": {"offset": "0x268A0"}, "Component::As": {"offset": "0xDE00"}, "Component::DoGameLoad": {"offset": "0xDEA0"}, "Component::IsA": {"offset": "0xDEC0"}, "Component::SetCommandLine": {"offset": "0x9D20"}, "Component::SetUserData": {"offset": "0xDEA0"}, "ComponentInstance::Initialize": {"offset": "0xDEB0"}, "ComponentInstance::Shutdown": {"offset": "0xDEA0"}, "ConsoleArgumentType<int,void>::Parse": {"offset": "0x19F80"}, "ConsoleCommand::ConsoleCommand<<lambda_41af1ba482b752c39807db2ef15caf48> >": {"offset": "0xE010"}, "ConsoleCommand::ConsoleCommand<<lambda_cd07e93ce4e084374db77c62c4f7d117> >": {"offset": "0xE270"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x17DC0"}, "ConsoleFlagsToString": {"offset": "0x194C0"}, "CoreGetComponentRegistry": {"offset": "0x197E0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x19870"}, "CreateComponent": {"offset": "0xDED0"}, "CreateVariableEntry<int>": {"offset": "0xEA80"}, "DllMain": {"offset": "0x2B298"}, "DoNtRaiseException": {"offset": "0x28EF0"}, "FatalErrorNoExceptRealV": {"offset": "0xB3F0"}, "FatalErrorRealV": {"offset": "0xB420"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x1B50"}, "GetAbsoluteCitPath": {"offset": "0x27500"}, "GlobalErrorHandler": {"offset": "0xB660"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x264D0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x26960"}, "InitFunction::Run": {"offset": "0xDF00"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x28C90"}, "InitFunctionBase::Register": {"offset": "0x29060"}, "InitFunctionBase::RunAll": {"offset": "0x290B0"}, "MakeRelativeCitPath": {"offset": "0xBD00"}, "RaiseDebugException": {"offset": "0x28FD0"}, "ScopedError::~ScopedError": {"offset": "0x9F10"}, "SysError": {"offset": "0xC280"}, "ToNarrow": {"offset": "0x290E0"}, "ToWide": {"offset": "0x291D0"}, "TraceRealV": {"offset": "0x294E0"}, "Win32TrapAndJump64": {"offset": "0x29D10"}, "_DllMainCRTStartup": {"offset": "0x2AB7C"}, "_Init_thread_abort": {"offset": "0x29F68"}, "_Init_thread_footer": {"offset": "0x29F98"}, "_Init_thread_header": {"offset": "0x29FF8"}, "_Init_thread_notify": {"offset": "0x2A060"}, "_Init_thread_wait": {"offset": "0x2A0A4"}, "_RTC_Initialize": {"offset": "0x2B2E8"}, "_RTC_Terminate": {"offset": "0x2B324"}, "__ArrayUnwind": {"offset": "0x2AC28"}, "__GSHandlerCheck": {"offset": "0x2A684"}, "__GSHandlerCheckCommon": {"offset": "0x2A6A4"}, "__GSHandlerCheck_EH": {"offset": "0x2A700"}, "__GSHandlerCheck_SEH": {"offset": "0x2AE08"}, "__chkstk": {"offset": "0x2A7F0"}, "__crt_debugger_hook": {"offset": "0x2B03C"}, "__dyn_tls_init": {"offset": "0x2A4CC"}, "__dyn_tls_on_demand_init": {"offset": "0x2A534"}, "__isa_available_init": {"offset": "0x2AE90"}, "__local_stdio_printf_options": {"offset": "0xDCE0"}, "__local_stdio_scanf_options": {"offset": "0x2B2BC"}, "__raise_securityfailure": {"offset": "0x2AC8C"}, "__report_gsfailure": {"offset": "0x2ACC0"}, "__scrt_acquire_startup_lock": {"offset": "0x2A14C"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x2A188"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x2A1BC"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x2A1D4"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x2A1FC"}, "__scrt_dllmain_exception_filter": {"offset": "0x2A214"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x2A274"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x2A2A4"}, "__scrt_fastfail": {"offset": "0x2B044"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x2B2E0"}, "__scrt_initialize_crt": {"offset": "0x2A2B8"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x2B2C4"}, "__scrt_initialize_onexit_tables": {"offset": "0x2A304"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x29E70"}, "__scrt_initialize_type_info": {"offset": "0x2A840"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x2A390"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x2CAB8"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x2B1E0"}, "__scrt_release_startup_lock": {"offset": "0x2A428"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xDEA0"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xDEA0"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xDEA0"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xDEA0"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xDEA0"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xDE00"}, "__scrt_throw_std_bad_alloc": {"offset": "0x2B1B0"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xCA80"}, "__scrt_uninitialize_crt": {"offset": "0x2A44C"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x29F40"}, "__scrt_uninitialize_type_info": {"offset": "0x2A850"}, "__security_check_cookie": {"offset": "0x2A790"}, "__security_init_cookie": {"offset": "0x2B1EC"}, "__std_find_trivial_1": {"offset": "0x29D30"}, "_get_startup_argv_mode": {"offset": "0x2B1D8"}, "_guard_check_icall_nop": {"offset": "0x9D20"}, "_guard_dispatch_icall_nop": {"offset": "0x2B470"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x2B490"}, "_onexit": {"offset": "0x2A478"}, "_wwassert": {"offset": "0x27880"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x2CB76"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x2CAD0"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x2CAE7"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x2CB00"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x2CB14"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x12A0"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x12D0"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x1300"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x1330"}, "`dynamic initializer for 'fx::PoolSizeManager::limits''": {"offset": "0x1210"}, "`dynamic initializer for 'fx::PoolSizeManager::sizeIncrease''": {"offset": "0x1220"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initFunction''": {"offset": "0x1360"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x13A0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17E50"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17E50"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>,<lambda_7604da78e8f33a312c72ae5a43f9b93b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17E70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>,<lambda_f17ebabe33bc32be107ce6fff046b802> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x17E70"}, "atexit": {"offset": "0x2A4B4"}, "capture_previous_context": {"offset": "0x2AD94"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xED60"}, "console::Printfv": {"offset": "0x1A010"}, "dllmain_crt_dispatch": {"offset": "0x2A85C"}, "dllmain_crt_process_attach": {"offset": "0x2A8AC"}, "dllmain_crt_process_detach": {"offset": "0x2A9C4"}, "dllmain_dispatch": {"offset": "0x2AA48"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD0C0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0x9DE0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x25CF0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x25330"}, "fmt::v8::detail::add_compare": {"offset": "0x25500"}, "fmt::v8::detail::assert_fail": {"offset": "0x25640"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x25690"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x25860"}, "fmt::v8::detail::bigint::square": {"offset": "0x260C0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x25330"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x25F0"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x26380"}, "fmt::v8::detail::compare": {"offset": "0x257C0"}, "fmt::v8::detail::count_digits": {"offset": "0xCEA0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x21E80"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x26A0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x25BC0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x23D50"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x25EA0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x23D80"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x24A40"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x24610"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x25E20"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x21F90"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x21F90"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x25B50"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x26D0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x23440"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x29A0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x23320"}, "fmt::v8::detail::format_float<double>": {"offset": "0x21AA0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x23580"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x2A80"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x238E0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x2BA0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x2D00"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x2F60"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xDC30"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x23F90"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x24210"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x244A0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0x9E40"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3030"}, "fmt::v8::detail::utf8_decode": {"offset": "0xDA70"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4630"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5630"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x51E0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x5A70"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x250A0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x24F80"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5110"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x5EB0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x5EF0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6080"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x6A40"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6450"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x99D0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7170"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7590"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7760"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7900"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7900"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x7A90"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x7CB0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x7E30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x95C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x7FC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x81E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8480"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x86A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8820"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x8A40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x8C60"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x8DE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9000"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9180"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x93A0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9750"}, "fmt::v8::format_error::format_error": {"offset": "0x9BD0"}, "fmt::v8::format_error::~format_error": {"offset": "0x9F70"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x282C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x34A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3280"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3150"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3060"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x34A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3370"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x35D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4130"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3B60"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x168B0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x28B60"}, "fprintf": {"offset": "0xDCF0"}, "fwPlatformString::~fwPlatformString": {"offset": "0x9F90"}, "fwRefCountable::AddRef": {"offset": "0x29CD0"}, "fwRefCountable::Release": {"offset": "0x29CE0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x29CC0"}, "fx::PoolSizeManager::FetchIncreaseRequest": {"offset": "0x19900"}, "fx::PoolSizeManager::FetchLimits": {"offset": "0x19DC0"}, "fx::PoolSizeManager::GetIncreaseRequest": {"offset": "0x19F10"}, "fx::PoolSizeManager::LimitsLoaded": {"offset": "0x19F70"}, "fx::PoolSizeManager::Validate": {"offset": "0x1ABC0"}, "fx::PoolSizeManager::ValidateImpl": {"offset": "0x1AC60"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::Call": {"offset": "0x18850"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0xE730"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x18C60"}, "internal::ConsoleVariableEntry<int>::ConsoleVariableEntry<int>": {"offset": "0x169D0"}, "internal::ConsoleVariableEntry<int>::GetOfflineValue": {"offset": "0x19F20"}, "internal::ConsoleVariableEntry<int>::GetValue": {"offset": "0x19F40"}, "internal::ConsoleVariableEntry<int>::SaveOfflineValue": {"offset": "0x1A1D0"}, "internal::ConsoleVariableEntry<int>::SetRawValue": {"offset": "0x1A1E0"}, "internal::ConsoleVariableEntry<int>::SetValue": {"offset": "0x1A570"}, "internal::ConsoleVariableEntry<int>::UpdateTrackingVariable": {"offset": "0x1A670"}, "internal::Constraints<int,void>::Compare": {"offset": "0x190A0"}, "launch::IsSDKGuest": {"offset": "0x27800"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[23],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10ED0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[24],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10ED0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[25],char const *>": {"offset": "0x10F90"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[26],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char>": {"offset": "0x11060"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[29],char const *>": {"offset": "0x10F90"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[12],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[3],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x11170"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x112D0"}, "nlohmann::json_abi_v3_11_2::detail::exception::exception": {"offset": "0x17450"}, "nlohmann::json_abi_v3_11_2::detail::exception::name": {"offset": "0x1E400"}, "nlohmann::json_abi_v3_11_2::detail::exception::what": {"offset": "0x21A20"}, "nlohmann::json_abi_v3_11_2::detail::invalid_iterator::invalid_iterator": {"offset": "0x174D0"}, "nlohmann::json_abi_v3_11_2::detail::invalid_iterator::~invalid_iterator": {"offset": "0x17ED0"}, "nlohmann::json_abi_v3_11_2::detail::other_error::create<std::nullptr_t,0>": {"offset": "0x113A0"}, "nlohmann::json_abi_v3_11_2::detail::other_error::other_error": {"offset": "0x17500"}, "nlohmann::json_abi_v3_11_2::detail::other_error::~other_error": {"offset": "0x17ED0"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::create<std::nullptr_t,0>": {"offset": "0x115E0"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::out_of_range": {"offset": "0x17530"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::~out_of_range": {"offset": "0x17ED0"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::create<std::nullptr_t,0>": {"offset": "0x11810"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::parse_error": {"offset": "0x175E0"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::position_string": {"offset": "0x1EE70"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::~parse_error": {"offset": "0x17ED0"}, "nlohmann::json_abi_v3_11_2::detail::type_error::type_error": {"offset": "0x17620"}, "nlohmann::json_abi_v3_11_2::detail::type_error::~type_error": {"offset": "0x17ED0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x9C50"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x9CF0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1560"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xADC0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x9D20"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xBF10"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xBFD0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC240"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xC6E0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0x9D30"}, "rapidjson::internal::DigitGen": {"offset": "0xB070"}, "rapidjson::internal::Grisu2": {"offset": "0xBB20"}, "rapidjson::internal::Prettify": {"offset": "0xC080"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x1FF0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x20C0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0x9CF0"}, "rapidjson::internal::WriteExponent": {"offset": "0xC650"}, "rapidjson::internal::u32toa": {"offset": "0xD1B0"}, "rapidjson::internal::u64toa": {"offset": "0xD420"}, "se::Object::~Object": {"offset": "0x9E40"}, "snprintf": {"offset": "0x21A40"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x17680"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x9D60"}, "std::_Destroy_in_place<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x9E40"}, "std::_Facet_Register": {"offset": "0x29E20"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x17650"}, "std::_Func_class<void,int const &>::~_Func_class<void,int const &>": {"offset": "0x17650"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x17650"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Copy": {"offset": "0x1B180"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Delete_this": {"offset": "0x1B310"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Do_call": {"offset": "0x1B4D0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Get": {"offset": "0x1BCD0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Move": {"offset": "0xDE00"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Target_type": {"offset": "0x1C070"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x1B1E0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x1B390"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x1B4E0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1BCD0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xDE00"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x1C080"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x1B260"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x1B3F0"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x1B530"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0x1BCD0"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0x1B260"}, "std::_Func_impl_no_alloc<<lambda_7f7545ea1588cc5959ac8fa27a171ca9>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x1C090"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Copy": {"offset": "0x1B270"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Delete_this": {"offset": "0x1B3F0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Do_call": {"offset": "0x1B550"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Get": {"offset": "0x1BCD0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Move": {"offset": "0x1B270"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Target_type": {"offset": "0x1C0A0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x1B290"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x1B390"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x1B630"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1BCD0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xDE00"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x1C0B0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10340"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Forced_rehash": {"offset": "0x1BA50"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Unchecked_erase": {"offset": "0x1C160"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::clear": {"offset": "0x1C670"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::emplace_hint<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >": {"offset": "0x11F40"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::~_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >": {"offset": "0x176C0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >::_Assign_grow": {"offset": "0x1AEE0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >": {"offset": "0x17740"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x10520"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x10410"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *>::_Freenode<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x104B0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x177A0"}, "std::_Maklocstr<char>": {"offset": "0xDD40"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xDE00"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Delete_this": {"offset": "0x1B400"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Destroy": {"offset": "0x1B420"}, "std::_Throw_bad_array_new_length": {"offset": "0xCA80"}, "std::_Throw_tree_length_error": {"offset": "0xCAA0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x252F0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2290"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2510"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0x9D80"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2230"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xC820"}, "std::_Xlen_string": {"offset": "0xCAC0"}, "std::allocator<char>::allocate": {"offset": "0xCAE0"}, "std::allocator<char>::deallocate": {"offset": "0x1C700"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x1C700"}, "std::allocator<unsigned int>::allocate": {"offset": "0x1C400"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x1C740"}, "std::allocator<wchar_t>::allocate": {"offset": "0xCB40"}, "std::bad_alloc::bad_alloc": {"offset": "0x2B190"}, "std::bad_alloc::~bad_alloc": {"offset": "0x9F70"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x9B60"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0x9F70"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x17E90"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x17F10"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2170"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x10790"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x10900"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x10A90"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x1C550"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xCD30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x17170"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0xDF20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::reserve": {"offset": "0x1F390"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x9E40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0xEFB0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0x9EA0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xCBB0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x171B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x29980"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x29AE0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x9EA0"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x12220"}, "std::exception::exception": {"offset": "0x9B90"}, "std::exception::what": {"offset": "0xDC10"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > >": {"offset": "0x125A0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x17650"}, "std::function<void __cdecl(bool,char const *,unsigned __int64)>::~function<void __cdecl(bool,char const *,unsigned __int64)>": {"offset": "0x17650"}, "std::function<void __cdecl(int const &)>::~function<void __cdecl(int const &)>": {"offset": "0x17650"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x17650"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >": {"offset": "0x17B40"}, "std::locale::~locale": {"offset": "0x253B0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x25A70"}, "std::numpunct<char>::do_falsename": {"offset": "0x25A80"}, "std::numpunct<char>::do_grouping": {"offset": "0x25AC0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x25B00"}, "std::numpunct<char>::do_truename": {"offset": "0x25B10"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x25140"}, "std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x17B70"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>": {"offset": "0x9E40"}, "std::runtime_error::runtime_error": {"offset": "0x9C10"}, "std::shared_ptr<HttpRequestHandle>::~shared_ptr<HttpRequestHandle>": {"offset": "0x17C40"}, "std::shared_ptr<internal::ConsoleVariableEntry<int> >::~shared_ptr<internal::ConsoleVariableEntry<int> >": {"offset": "0x17C40"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x17C40"}, "std::to_string": {"offset": "0x21810"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x17D10"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x25390"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >": {"offset": "0x17C90"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >": {"offset": "0x17C90"}, "std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >": {"offset": "0x17360"}, "std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >::~unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >": {"offset": "0x17D40"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x24E10"}, "utf8::exception::exception": {"offset": "0x28CB0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x27D30"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x28660"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x28D40"}, "utf8::invalid_code_point::what": {"offset": "0x29C90"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0x9F70"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x28DB0"}, "utf8::invalid_utf8::what": {"offset": "0x29CA0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0x9F70"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x28E10"}, "utf8::not_enough_room::what": {"offset": "0x29CB0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0x9F70"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x28440"}, "vva": {"offset": "0x29C70"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1060"}}}