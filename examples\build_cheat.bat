@echo off
echo Building FiveM Simple Cheat...

REM Verificar se ImGui está configurado
if not exist "..\external\imgui" (
    echo Dear ImGui not found! Running setup...
    cd ..
    call setup_imgui.bat
    cd examples
)

REM Criar diretório de build
if not exist "build" mkdir build
cd build

echo Configuring project...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

cd ..

echo Build completed successfully!
echo Output: FiveMSimpleCheat.dll

if exist "FiveMSimpleCheat.dll" (
    echo DLL found in examples directory.
) else (
    echo Warning: DLL not found in expected location.
)

echo.
echo Usage:
echo 1. Inject FiveMSimpleCheat.dll into FiveM process
echo 2. Press INSERT to open menu
echo 3. Press F1 to toggle ESP
echo 4. Press F2 to toggle God Mode
echo.

pause
