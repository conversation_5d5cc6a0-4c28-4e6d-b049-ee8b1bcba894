{"citizen-game-main.dll": {"<lambda_0857a9bc3a63a5f981792fe3ba8c6308>::~<lambda_0857a9bc3a63a5f981792fe3ba8c6308>": {"offset": "0x10120"}, "<lambda_147316b919797be978274d8a1c849eb4>::~<lambda_147316b919797be978274d8a1c849eb4>": {"offset": "0x10230"}, "<lambda_1a73e681d985a5b86a541cbcc350ebc8>::~<lambda_1a73e681d985a5b86a541cbcc350ebc8>": {"offset": "0x10230"}, "<lambda_2c71f2734007fd255e94b39244217c06>::~<lambda_2c71f2734007fd255e94b39244217c06>": {"offset": "0x10230"}, "<lambda_41af1ba482b752c39807db2ef15caf48>::~<lambda_41af1ba482b752c39807db2ef15caf48>": {"offset": "0xA560"}, "<lambda_4bd345ce870bcb6a9dba57dde6595eaf>::<lambda_4bd345ce870bcb6a9dba57dde6595eaf>": {"offset": "0x11DF0"}, "<lambda_55cf421c54d17ec848e1c39d1a1f440e>::~<lambda_55cf421c54d17ec848e1c39d1a1f440e>": {"offset": "0x10230"}, "<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x10230"}, "<lambda_7604da78e8f33a312c72ae5a43f9b93b>::~<lambda_7604da78e8f33a312c72ae5a43f9b93b>": {"offset": "0x10230"}, "<lambda_78c37b1a3777bfd305deeb2c72fc6c44>::~<lambda_78c37b1a3777bfd305deeb2c72fc6c44>": {"offset": "0x10230"}, "<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>::~<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>": {"offset": "0x10150"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x10230"}, "<lambda_86be70dd2992469b5252a9192288f807>::~<lambda_86be70dd2992469b5252a9192288f807>": {"offset": "0xA560"}, "<lambda_8d215c8ebd35918aa8dcf54639f44090>::~<lambda_8d215c8ebd35918aa8dcf54639f44090>": {"offset": "0x101B0"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xA560"}, "<lambda_e53e06857a4b8a0a6fa4a7061e30490e>::~<lambda_e53e06857a4b8a0a6fa4a7061e30490e>": {"offset": "0x10120"}, "<lambda_f17ebabe33bc32be107ce6fff046b802>::~<lambda_f17ebabe33bc32be107ce6fff046b802>": {"offset": "0x10230"}, "<lambda_f65fa3bc1f1c0ae5a28b4030ae52c0e3>::~<lambda_f65fa3bc1f1c0ae5a28b4030ae52c0e3>": {"offset": "0x10230"}, "<lambda_fa4021805ee7d60682a29a1232274ee1>::~<lambda_fa4021805ee7d60682a29a1232274ee1>": {"offset": "0x10230"}, "CefStringBase<CefStringTraitsUTF16>::~CefStringBase<CefStringTraitsUTF16>": {"offset": "0x307A0"}, "CfxState::CfxState": {"offset": "0x1CF80"}, "Component::SetCommandLine": {"offset": "0xA440"}, "Component::SetUserData": {"offset": "0xE780"}, "ComponentInstance::DoGameLoad": {"offset": "0xE6B0"}, "ComponentInstance::Initialize": {"offset": "0xE6C0"}, "ComponentInstance::Run": {"offset": "0xE740"}, "ComponentInstance::Shutdown": {"offset": "0xE780"}, "ConVar<bool>::ConVar<bool>": {"offset": "0x2F1B0"}, "ConVar<bool>::~ConVar<bool>": {"offset": "0x307F0"}, "ConVar<int>::ConVar<int>": {"offset": "0x2F020"}, "ConVar<int>::~ConVar<int>": {"offset": "0x307F0"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x32F40"}, "ConsoleArgumentType<int,void>::Parse": {"offset": "0x32EB0"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0x2C7F0"}, "ConsoleCommand::ConsoleCommand<<lambda_41af1ba482b752c39807db2ef15caf48> >": {"offset": "0x2C9D0"}, "ConsoleCommand::ConsoleCommand<<lambda_5b1c43db5646bdcc3820d1877854a8bd> >": {"offset": "0x11E00"}, "ConsoleCommand::ConsoleCommand<<lambda_86be70dd2992469b5252a9192288f807> >": {"offset": "0x11FB0"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0x2CC20"}, "ConsoleCommand::ConsoleCommand<<lambda_cd07e93ce4e084374db77c62c4f7d117> >": {"offset": "0x2CE70"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x1EA30"}, "ConsoleFlagsToString": {"offset": "0x21410"}, "CoreGetComponentRegistry": {"offset": "0x21730"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x217C0"}, "CreateComponent": {"offset": "0xE790"}, "CreateVariableEntry<bool>": {"offset": "0x2E5C0"}, "CreateVariableEntry<int>": {"offset": "0x2E2E0"}, "CreateVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13270"}, "DllMain": {"offset": "0xCE2A8"}, "DloadAcquireSectionWriteAccess": {"offset": "0xCE46C"}, "DloadGetSRWLockFunctionPointers": {"offset": "0xCE518"}, "DloadMakePermanentImageCommit": {"offset": "0xCE5B8"}, "DloadObtainSection": {"offset": "0xCE650"}, "DloadProtectSection": {"offset": "0xCE6EC"}, "DloadReleaseSectionWriteAccess": {"offset": "0xCE77C"}, "DoNtRaiseException": {"offset": "0x489F0"}, "EnsureImFontTexture": {"offset": "0xE7D0"}, "FatalErrorNoExceptRealV": {"offset": "0xBB10"}, "FatalErrorRealV": {"offset": "0xBB40"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x2270"}, "FrontendNuiInterface::BlitTexture": {"offset": "0xFA90"}, "FrontendNuiInterface::CreateTexture": {"offset": "0xF340"}, "FrontendNuiInterface::CreateTextureBacking": {"offset": "0xF590"}, "FrontendNuiInterface::CreateTextureFromD3D11Texture": {"offset": "0xFB90"}, "FrontendNuiInterface::CreateTextureFromShareHandle": {"offset": "0xF640"}, "FrontendNuiInterface::DrawRectangles": {"offset": "0xF7F0"}, "FrontendNuiInterface::GetD3D11Device": {"offset": "0xFB30"}, "FrontendNuiInterface::GetD3D11DeviceContext": {"offset": "0xFB50"}, "FrontendNuiInterface::GetGameResolution": {"offset": "0xF300"}, "FrontendNuiInterface::GetHWND": {"offset": "0xFA80"}, "FrontendNuiInterface::SetGameMouseFocus": {"offset": "0xA440"}, "FrontendNuiInterface::SetTexture": {"offset": "0xF6F0"}, "FrontendNuiInterface::UnsetTexture": {"offset": "0xA440"}, "FrontendNuiTexture::FrontendNuiTexture": {"offset": "0xFE00"}, "FrontendNuiTexture::GetHostTexture": {"offset": "0xF100"}, "FrontendNuiTexture::GetNativeTexture": {"offset": "0xEA60"}, "FrontendNuiTexture::Map": {"offset": "0xF110"}, "FrontendNuiTexture::Unmap": {"offset": "0xA440"}, "FrontendNuiTexture::~FrontendNuiTexture": {"offset": "0x10450"}, "GetAbsoluteCitPath": {"offset": "0x3FC50"}, "GlobalErrorHandler": {"offset": "0xBD80"}, "HookFunctionBase::RunAll": {"offset": "0x49DF0"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x1C1F0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x3F470"}, "HostSharedData<ReverseGameData>::HostSharedData<ReverseGameData>": {"offset": "0x1C440"}, "InitFunction::Run": {"offset": "0xE7C0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x47B20"}, "InitFunctionBase::Register": {"offset": "0x48B60"}, "InitFunctionBase::RunAll": {"offset": "0x48BB0"}, "InitializeNui": {"offset": "0x112E0"}, "MakeRelativeCitPath": {"offset": "0xC420"}, "Microsoft::WRL::ComPtr<ID3D11Device>::~ComPtr<ID3D11Device>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<ID3D11Resource>::~ComPtr<ID3D11Resource>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<ID3D11Texture2D>::~ComPtr<ID3D11Texture2D>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<IDCompositionDesktopDevice>::~ComPtr<IDCompositionDesktopDevice>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<IDCompositionTarget>::~ComPtr<IDCompositionTarget>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<IDCompositionVisual2>::~ComPtr<IDCompositionVisual2>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<IDXGIDevice>::~ComPtr<IDXGIDevice>": {"offset": "0x10200"}, "Microsoft::WRL::ComPtr<IDXGIKeyedMutex>::ComPtr<IDXGIKeyedMutex>": {"offset": "0x2F010"}, "Microsoft::WRL::ComPtr<IDXGIKeyedMutex>::~ComPtr<IDXGIKeyedMutex>": {"offset": "0x10200"}, "RaiseDebugException": {"offset": "0x48AD0"}, "ReverseGameData::ReverseGameData": {"offset": "0x1DAA0"}, "RunnableComponent::As": {"offset": "0xE590"}, "RunnableComponent::IsA": {"offset": "0xE6D0"}, "ScopedError::~ScopedError": {"offset": "0xA630"}, "SysError": {"offset": "0xC9A0"}, "ToNarrow": {"offset": "0x48BE0"}, "ToWide": {"offset": "0x48CD0"}, "TraceRealV": {"offset": "0x48FE0"}, "UpdateSubresources": {"offset": "0x74410"}, "Win32TrapAndJump64": {"offset": "0x49E20"}, "_DllMainCRTStartup": {"offset": "0xCDAF0"}, "_Init_thread_abort": {"offset": "0xCCDF4"}, "_Init_thread_footer": {"offset": "0xCCE24"}, "_Init_thread_header": {"offset": "0xCCE84"}, "_Init_thread_notify": {"offset": "0xCCEEC"}, "_Init_thread_wait": {"offset": "0xCCF30"}, "_RTC_Initialize": {"offset": "0xCE2F0"}, "_RTC_Terminate": {"offset": "0xCE32C"}, "_Smtx_lock_exclusive": {"offset": "0xCCCEC"}, "_Smtx_unlock_exclusive": {"offset": "0xCCCF4"}, "__ArrayUnwind": {"offset": "0xCD6D8"}, "__GSHandlerCheck": {"offset": "0xCD510"}, "__GSHandlerCheckCommon": {"offset": "0xCD530"}, "__GSHandlerCheck_EH": {"offset": "0xCD58C"}, "__GSHandlerCheck_SEH": {"offset": "0xCDDCC"}, "__chkstk": {"offset": "0xCDE70"}, "__crt_debugger_hook": {"offset": "0xCE074"}, "__delayLoadHelper2": {"offset": "0xCE810"}, "__dyn_tls_init": {"offset": "0xCD358"}, "__dyn_tls_on_demand_init": {"offset": "0xCD3C0"}, "__isa_available_init": {"offset": "0xCDEC0"}, "__local_stdio_printf_options": {"offset": "0xE400"}, "__local_stdio_scanf_options": {"offset": "0x38CF0"}, "__raise_securityfailure": {"offset": "0xCDB30"}, "__report_gsfailure": {"offset": "0xCDB64"}, "__report_rangecheckfailure": {"offset": "0xCDC38"}, "__report_securityfailure": {"offset": "0xCDC4C"}, "__scrt_acquire_startup_lock": {"offset": "0xCCFD8"}, "__scrt_dllmain_after_initialize_c": {"offset": "0xCD014"}, "__scrt_dllmain_before_initialize_c": {"offset": "0xCD048"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0xCD060"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0xCD088"}, "__scrt_dllmain_exception_filter": {"offset": "0xCD0A0"}, "__scrt_dllmain_uninitialize_c": {"offset": "0xCD100"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0xCD130"}, "__scrt_fastfail": {"offset": "0xCE07C"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0xCE2E8"}, "__scrt_initialize_crt": {"offset": "0xCD144"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0xCE2CC"}, "__scrt_initialize_onexit_tables": {"offset": "0xCD190"}, "__scrt_initialize_thread_safe_statics": {"offset": "0xCCCFC"}, "__scrt_initialize_type_info": {"offset": "0xCD744"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0xCD21C"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0xD30ED"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0xCE1F0"}, "__scrt_release_startup_lock": {"offset": "0xCD2B4"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE780"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE780"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE780"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE780"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE780"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xEA60"}, "__scrt_throw_std_bad_alloc": {"offset": "0xCE1C8"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD1A0"}, "__scrt_uninitialize_crt": {"offset": "0xCD2D8"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0xCCDCC"}, "__scrt_uninitialize_type_info": {"offset": "0xCD754"}, "__security_check_cookie": {"offset": "0xCD620"}, "__security_init_cookie": {"offset": "0xCE1FC"}, "__std_find_trivial_1": {"offset": "0xCC640"}, "__std_find_trivial_2": {"offset": "0xCC710"}, "__std_fs_code_page": {"offset": "0xCC9C4"}, "__std_fs_convert_narrow_to_wide": {"offset": "0xCC9EC"}, "__std_fs_convert_wide_to_narrow": {"offset": "0xCCA34"}, "__std_fs_convert_wide_to_narrow_replace_chars": {"offset": "0xCCB58"}, "__std_fs_get_full_path_name": {"offset": "0xCCC24"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0xCCCE0"}, "__std_swap_ranges_trivially_swappable_noalias": {"offset": "0xCC7E0"}, "__std_system_error_allocate_message": {"offset": "0xCC920"}, "__std_system_error_deallocate_message": {"offset": "0xCC9B4"}, "_get_startup_argv_mode": {"offset": "0x5BF90"}, "_guard_check_icall_nop": {"offset": "0xA440"}, "_guard_dispatch_icall_nop": {"offset": "0xCEB20"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0xCEB40"}, "_onexit": {"offset": "0xCD304"}, "_wwassert": {"offset": "0x3FF50"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0xD3131"}, "`bgfx::d3d11::hasDepthOp'::`2'::FindDepthOp::find": {"offset": "0x689A0"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0xD31BC"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0xD31D3"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0xD31EC"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0xD3200"}, "`dynamic initializer for 'DebugUvVertex::ms_decl''": {"offset": "0x1690"}, "`dynamic initializer for 'HostSharedData<ReverseGameData>::m_fakeData''": {"offset": "0x1460"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x1710"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x1740"}, "`dynamic initializer for '_init_instance_23''": {"offset": "0x1470"}, "`dynamic initializer for '_init_instance_24''": {"offset": "0x14A0"}, "`dynamic initializer for '_init_instance_25''": {"offset": "0x14D0"}, "`dynamic initializer for '_init_instance_26''": {"offset": "0x1270"}, "`dynamic initializer for 'citizen::Win32GameWindow::ms_windowMapping''": {"offset": "0x16A0"}, "`dynamic initializer for 'doNext''": {"offset": "0x1500"}, "`dynamic initializer for 'g_earlyOnRenderQueue''": {"offset": "0x1320"}, "`dynamic initializer for 'g_frontendDeletionMutex''": {"offset": "0x1390"}, "`dynamic initializer for 'g_onRenderQueue''": {"offset": "0x13C0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initFunction''": {"offset": "0x12A0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1800"}, "`dynamic initializer for 'm_decl''": {"offset": "0x12E0"}, "`dynamic initializer for 'nuiGi''": {"offset": "0x1430"}, "`dynamic initializer for 'tbb::detail::r1::concurrent_monitor_mutex::my_init_mutex''": {"offset": "0x1AA0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>,<lambda_2c71f2734007fd255e94b39244217c06> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>,<lambda_55cf421c54d17ec848e1c39d1a1f440e> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>,<lambda_7604da78e8f33a312c72ae5a43f9b93b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>,<lambda_7d6aeca7e4d2a325a53db3c90716b4b4> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x10650"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>,<lambda_86be70dd2992469b5252a9192288f807> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>,<lambda_86be70dd2992469b5252a9192288f807> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>,<lambda_f17ebabe33bc32be107ce6fff046b802> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1EB70"}, "astc_codec::ASTCDecompressToRGBA": {"offset": "0xBFC70"}, "astc_codec::DecodeColorsForMode": {"offset": "0xC6340"}, "astc_codec::DecompressToImage": {"offset": "0xBFD20"}, "astc_codec::EndpointRangeForBlock": {"offset": "0xC5370"}, "astc_codec::Footprint::Footprint": {"offset": "0xC0000"}, "astc_codec::Footprint::FromFootprintType": {"offset": "0xC01F0"}, "astc_codec::GetASTCPartition": {"offset": "0xC5E40"}, "astc_codec::InfillWeights": {"offset": "0xC9CA0"}, "astc_codec::IntegerSequenceCodec::GetBitCount": {"offset": "0xC4B10"}, "astc_codec::IntegerSequenceCodec::GetBitCountForRange": {"offset": "0xC1240"}, "astc_codec::IntegerSequenceCodec::GetCountsForRange": {"offset": "0xC4B60"}, "astc_codec::IntegerSequenceCodec::IntegerSequenceCodec": {"offset": "0xC46F0"}, "astc_codec::IntegerSequenceDecoder::Decode": {"offset": "0xC4840"}, "astc_codec::IntermediateBlockData::~IntermediateBlockData": {"offset": "0xC2BE0"}, "astc_codec::IntermediateEndpointData::~IntermediateEndpointData": {"offset": "0xC5310"}, "astc_codec::LogicalASTCBlock::CalculateWeights": {"offset": "0xC2DC0"}, "astc_codec::LogicalASTCBlock::ColorAt": {"offset": "0xC3070"}, "astc_codec::LogicalASTCBlock::LogicalASTCBlock": {"offset": "0xC29F0"}, "astc_codec::LogicalASTCBlock::SetDualPlaneChannel": {"offset": "0xC3450"}, "astc_codec::LogicalASTCBlock::~LogicalASTCBlock": {"offset": "0xBFB10"}, "astc_codec::Partition::~Partition": {"offset": "0xBFC10"}, "astc_codec::PhysicalASTCBlock::ColorStartBit": {"offset": "0xC03E0"}, "astc_codec::PhysicalASTCBlock::ColorValuesRange": {"offset": "0xC0470"}, "astc_codec::PhysicalASTCBlock::DualPlaneChannel": {"offset": "0xC1100"}, "astc_codec::PhysicalASTCBlock::GetColorValuesInfo": {"offset": "0xC1280"}, "astc_codec::PhysicalASTCBlock::GetEndpointMode": {"offset": "0xC1430"}, "astc_codec::PhysicalASTCBlock::IsDualPlane": {"offset": "0xC1540"}, "astc_codec::PhysicalASTCBlock::IsIllegalEncoding": {"offset": "0xC15F0"}, "astc_codec::PhysicalASTCBlock::IsVoidExtent": {"offset": "0xC1A50"}, "astc_codec::PhysicalASTCBlock::NumColorBits": {"offset": "0xC1B20"}, "astc_codec::PhysicalASTCBlock::NumColorValues": {"offset": "0xC1C00"}, "astc_codec::PhysicalASTCBlock::NumPartitions": {"offset": "0xC1CF0"}, "astc_codec::PhysicalASTCBlock::NumWeightBits": {"offset": "0xC1DE0"}, "astc_codec::PhysicalASTCBlock::PartitionID": {"offset": "0xC1EC0"}, "astc_codec::PhysicalASTCBlock::PhysicalASTCBlock": {"offset": "0xC0350"}, "astc_codec::PhysicalASTCBlock::VoidExtentCoords": {"offset": "0xC1F30"}, "astc_codec::PhysicalASTCBlock::WeightGridDims": {"offset": "0xC2070"}, "astc_codec::PhysicalASTCBlock::WeightRange": {"offset": "0xC2220"}, "astc_codec::UnpackIntermediateBlock": {"offset": "0xC5570"}, "astc_codec::UnpackLogicalBlock": {"offset": "0xC35C0"}, "astc_codec::UnpackVoidExtent": {"offset": "0xC5C60"}, "astc_codec::UnquantizeCEValueFromRange": {"offset": "0xC9BE0"}, "astc_codec::UnquantizeWeightFromRange": {"offset": "0xC9C20"}, "astc_codec::`anonymous namespace'::BitQuantizationMap<6>::BitQuantizationMap<6>": {"offset": "0xC6E80"}, "astc_codec::`anonymous namespace'::BitQuantizationMap<8>::BitQuantizationMap<8>": {"offset": "0xC7050"}, "astc_codec::`anonymous namespace'::DecodeBlockMode": {"offset": "0xC0550"}, "astc_codec::`anonymous namespace'::DecodeDualPlaneBit": {"offset": "0xC0770"}, "astc_codec::`anonymous namespace'::DecodeDualPlaneBitStartPos": {"offset": "0xC07F0"}, "astc_codec::`anonymous namespace'::DecodeEndpointMode": {"offset": "0xC0870"}, "astc_codec::`anonymous namespace'::DecodeEndpoints": {"offset": "0xC3340"}, "astc_codec::`anonymous namespace'::DecodeISEBlock<3>": {"offset": "0xC3A30"}, "astc_codec::`anonymous namespace'::DecodeISEBlock<5>": {"offset": "0xC3C40"}, "astc_codec::`anonymous namespace'::DecodeNumColorValues": {"offset": "0xC09E0"}, "astc_codec::`anonymous namespace'::DecodeNumExtraCEMBits": {"offset": "0xC0BA0"}, "astc_codec::`anonymous namespace'::DecodeNumWeightBits": {"offset": "0xC0C10"}, "astc_codec::`anonymous namespace'::DecodeWeightProps": {"offset": "0xC0D80"}, "astc_codec::`anonymous namespace'::ExtraConfigBitPosition": {"offset": "0xC54B0"}, "astc_codec::`anonymous namespace'::GetQuantMapForValueRange": {"offset": "0xC7E20"}, "astc_codec::`anonymous namespace'::GetQuantMapForWeightRange": {"offset": "0xC9000"}, "astc_codec::`anonymous namespace'::QuantizationMap::GenerateQuantizationMap": {"offset": "0xC7D80"}, "astc_codec::`anonymous namespace'::QuantizationMap::~QuantizationMap": {"offset": "0xC7C10"}, "astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue>::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue>": {"offset": "0xC7220"}, "astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight>::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight>": {"offset": "0xC73C0"}, "astc_codec::`anonymous namespace'::SelectASTCPartition": {"offset": "0xC5FD0"}, "astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue>::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue>": {"offset": "0xC7540"}, "astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight>::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight>": {"offset": "0xC7810"}, "astc_codec::`anonymous namespace'::`dynamic initializer for 'kMaxRanges''": {"offset": "0x1870"}, "astc_codec::base::BitStream<astc_codec::base::UInt128>::MaskFor": {"offset": "0xC4D30"}, "astc_codec::base::GetBits<astc_codec::base::UInt128>": {"offset": "0xC0250"}, "astc_codec::base::Log2Floor": {"offset": "0xC4CC0"}, "astc_codec::base::Optional<astc_codec::IntermediateBlockData>::~Optional<astc_codec::IntermediateBlockData>": {"offset": "0xC2BD0"}, "astc_codec::base::Optional<astc_codec::LogicalASTCBlock::DualPlaneData>::destruct": {"offset": "0xC39D0"}, "astc_codec::base::Optional<astc_codec::LogicalASTCBlock::DualPlaneData>::~Optional<astc_codec::LogicalASTCBlock::DualPlaneData>": {"offset": "0xBFA30"}, "astc_codec::base::Optional<astc_codec::LogicalASTCBlock>::Optional<astc_codec::LogicalASTCBlock>": {"offset": "0xC27E0"}, "astc_codec::base::Optional<astc_codec::LogicalASTCBlock>::~Optional<astc_codec::LogicalASTCBlock>": {"offset": "0xBFAA0"}, "astc_codec::base::Optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~Optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xC0370"}, "astc_codec::base::ReverseBits<astc_codec::base::UInt128>": {"offset": "0xC4DE0"}, "atexit": {"offset": "0xCD340"}, "bgfx::AllocatorStub::realloc": {"offset": "0x535F0"}, "bgfx::Attachment::init": {"offset": "0x51610"}, "bgfx::BlitState::hasItem": {"offset": "0x5C010"}, "bgfx::CallbackStub::cacheRead": {"offset": "0xF110"}, "bgfx::CallbackStub::cacheReadSize": {"offset": "0xEA60"}, "bgfx::CallbackStub::cacheWrite": {"offset": "0xA440"}, "bgfx::CallbackStub::captureBegin": {"offset": "0xA440"}, "bgfx::CallbackStub::captureEnd": {"offset": "0xA440"}, "bgfx::CallbackStub::captureFrame": {"offset": "0xA440"}, "bgfx::CallbackStub::fatal": {"offset": "0x50270"}, "bgfx::CallbackStub::profilerBegin": {"offset": "0xA440"}, "bgfx::CallbackStub::profilerBeginLiteral": {"offset": "0xA440"}, "bgfx::CallbackStub::profilerEnd": {"offset": "0xA440"}, "bgfx::CallbackStub::screenShot": {"offset": "0x55840"}, "bgfx::CallbackStub::traceVargs": {"offset": "0x56F40"}, "bgfx::ClearQuad::init": {"offset": "0x51630"}, "bgfx::CommandBuffer::write<bgfx::Init>": {"offset": "0x4B300"}, "bgfx::Context::Context": {"offset": "0x4B330"}, "bgfx::Context::allocTransientVertexBuffer": {"offset": "0x4C6D0"}, "bgfx::Context::apiSemWait": {"offset": "0x4C930"}, "bgfx::Context::createIndexBuffer": {"offset": "0x4D810"}, "bgfx::Context::createProgram": {"offset": "0x4DC60"}, "bgfx::Context::createShader": {"offset": "0x4DEE0"}, "bgfx::Context::createTexture": {"offset": "0x4E930"}, "bgfx::Context::createTransientIndexBuffer": {"offset": "0x4EC70"}, "bgfx::Context::createTransientVertexBuffer": {"offset": "0x4EDD0"}, "bgfx::Context::createUniform": {"offset": "0x4EFB0"}, "bgfx::Context::createVertexBuffer": {"offset": "0x4F480"}, "bgfx::Context::destroyDynamicIndexBufferInternal": {"offset": "0x4F950"}, "bgfx::Context::destroyDynamicVertexBufferInternal": {"offset": "0x4FBD0"}, "bgfx::Context::destroyIndexBuffer": {"offset": "0x4FDC0"}, "bgfx::Context::destroyUniform": {"offset": "0x4FED0"}, "bgfx::Context::destroyVertexBuffer": {"offset": "0x4FFF0"}, "bgfx::Context::findVertexLayout": {"offset": "0x50490"}, "bgfx::Context::flip": {"offset": "0x50650"}, "bgfx::Context::flushTextureUpdateBatch": {"offset": "0x50760"}, "bgfx::Context::frame": {"offset": "0x50A20"}, "bgfx::Context::freeAllHandles": {"offset": "0x50E10"}, "bgfx::Context::getAvailTransientVertexBuffer": {"offset": "0x51390"}, "bgfx::Context::init": {"offset": "0x51A60"}, "bgfx::Context::renderFrame": {"offset": "0x53D70"}, "bgfx::Context::renderSemWait": {"offset": "0x53F10"}, "bgfx::Context::renderThread": {"offset": "0x53F80"}, "bgfx::Context::rendererExecCommands": {"offset": "0x54120"}, "bgfx::Context::resetView": {"offset": "0x554C0"}, "bgfx::Context::resizeTexture": {"offset": "0x556A0"}, "bgfx::Context::setName": {"offset": "0x55DB0"}, "bgfx::Context::shaderDecRef": {"offset": "0x56380"}, "bgfx::Context::swap": {"offset": "0x56A50"}, "bgfx::Context::textureDecRef": {"offset": "0x56DA0"}, "bgfx::Context::updateTexture": {"offset": "0x57120"}, "bgfx::Context::~Context": {"offset": "0x4BE30"}, "bgfx::DxbcContext::~DxbcContext": {"offset": "0x648A0"}, "bgfx::DxbcInstruction::~DxbcInstruction": {"offset": "0xB40D0"}, "bgfx::DxbcSignature::Element::Element": {"offset": "0xB3F40"}, "bgfx::DxbcSignature::Element::~Element": {"offset": "0xB3FC0"}, "bgfx::DxbcSubOperand::DxbcSubOperand": {"offset": "0xB3F30"}, "bgfx::Dxgi::Dxgi": {"offset": "0xB8940"}, "bgfx::Dxgi::createSwapChain": {"offset": "0xB8970"}, "bgfx::Dxgi::init": {"offset": "0xB8B30"}, "bgfx::Dxgi::resizeBuffers": {"offset": "0xB8F80"}, "bgfx::Dxgi::shutdown": {"offset": "0xB9040"}, "bgfx::Dxgi::trim": {"offset": "0xB90C0"}, "bgfx::Dxgi::update": {"offset": "0xB9110"}, "bgfx::Dxgi::updateHdr10": {"offset": "0xB91E0"}, "bgfx::Encoder::setVertexBuffer": {"offset": "0x56150"}, "bgfx::EncoderImpl::EncoderImpl": {"offset": "0x4B910"}, "bgfx::EncoderImpl::begin": {"offset": "0x4C9D0"}, "bgfx::EncoderImpl::discard": {"offset": "0x50100"}, "bgfx::EncoderImpl::setUniform": {"offset": "0x56030"}, "bgfx::EncoderImpl::submit": {"offset": "0x56670"}, "bgfx::Frame::Frame": {"offset": "0x4BA60"}, "bgfx::Frame::create": {"offset": "0x4D2D0"}, "bgfx::Frame::destroy": {"offset": "0x4F750"}, "bgfx::Frame::finish": {"offset": "0x505B0"}, "bgfx::Frame::sort": {"offset": "0x564B0"}, "bgfx::Frame::~Frame": {"offset": "0xA440"}, "bgfx::FrameBufferRef::FrameBufferRef": {"offset": "0x4BC00"}, "bgfx::FrameBufferRef::~FrameBufferRef": {"offset": "0x4BFE0"}, "bgfx::FrameCache::isZeroArea": {"offset": "0x5D210"}, "bgfx::IndexBuffer::IndexBuffer": {"offset": "0x4BC00"}, "bgfx::IndexBuffer::~IndexBuffer": {"offset": "0x4BFE0"}, "bgfx::Init::Init": {"offset": "0x4BC20"}, "bgfx::NonLocalAllocator::compact": {"offset": "0x4D1B0"}, "bgfx::NonLocalAllocator::free": {"offset": "0x50CA0"}, "bgfx::NonLocalAllocator::remove": {"offset": "0x537B0"}, "bgfx::NonLocalAllocator::~NonLocalAllocator": {"offset": "0x4C0A0"}, "bgfx::NvApi::NvApi": {"offset": "0xB3670"}, "bgfx::NvApi::getMemoryInfo": {"offset": "0xB3690"}, "bgfx::NvApi::init": {"offset": "0xB3700"}, "bgfx::NvApi::initAftermath": {"offset": "0xB3970"}, "bgfx::NvApi::loadAftermath": {"offset": "0xB3A10"}, "bgfx::NvApi::shutdown": {"offset": "0xB3CD0"}, "bgfx::PlatformData::PlatformData": {"offset": "0x4BC80"}, "bgfx::Profiler<bgfx::d3d11::TimerQueryD3D11>::begin": {"offset": "0x64CF0"}, "bgfx::Profiler<bgfx::d3d11::TimerQueryD3D11>::~Profiler<bgfx::d3d11::TimerQueryD3D11>": {"offset": "0x58740"}, "bgfx::Profiler<bgfx::d3d12::TimerQueryD3D12>::begin": {"offset": "0x74D00"}, "bgfx::Profiler<bgfx::d3d12::TimerQueryD3D12>::~Profiler<bgfx::d3d12::TimerQueryD3D12>": {"offset": "0x58740"}, "bgfx::Profiler<bgfx::d3d9::TimerQueryD3D9>::begin": {"offset": "0x58820"}, "bgfx::Profiler<bgfx::d3d9::TimerQueryD3D9>::~Profiler<bgfx::d3d9::TimerQueryD3D9>": {"offset": "0x58740"}, "bgfx::Profiler<bgfx::gl::TimerQueryGL>::begin": {"offset": "0x849B0"}, "bgfx::Profiler<bgfx::gl::TimerQueryGL>::~Profiler<bgfx::gl::TimerQueryGL>": {"offset": "0x58740"}, "bgfx::RenderBind::clear": {"offset": "0x4D030"}, "bgfx::RenderDraw::clear": {"offset": "0x4D110"}, "bgfx::Resolution::Resolution": {"offset": "0x4BCA0"}, "bgfx::ShaderRef::ShaderRef": {"offset": "0x4BCC0"}, "bgfx::ShaderRef::~ShaderRef": {"offset": "0x4C0F0"}, "bgfx::SortKey::decode": {"offset": "0x5B5C0"}, "bgfx::SortKey::encodeDraw": {"offset": "0x50190"}, "bgfx::StateCache::~StateCache": {"offset": "0x58750"}, "bgfx::StateCacheLru<IUnknown *,1024>::add": {"offset": "0x64A70"}, "bgfx::StateCacheLru<IUnknown *,1024>::find": {"offset": "0x68800"}, "bgfx::StateCacheLru<IUnknown *,1024>::invalidateWithParent": {"offset": "0x6B8C0"}, "bgfx::StateCacheLru<IUnknown *,1024>::~StateCacheLru<IUnknown *,1024>": {"offset": "0x58750"}, "bgfx::StateCacheLru<bgfx::d3d12::Bind,64>::find": {"offset": "0x78E10"}, "bgfx::StateCacheLru<bgfx::d3d12::Bind,64>::invalidate": {"offset": "0x7CA30"}, "bgfx::StateCacheLru<bgfx::d3d12::Bind,64>::~StateCacheLru<bgfx::d3d12::Bind,64>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D11BlendState>::~StateCacheT<ID3D11BlendState>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D11DepthStencilState>::~StateCacheT<ID3D11DepthStencilState>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D11InputLayout>::~StateCacheT<ID3D11InputLayout>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D11RasterizerState>::invalidate": {"offset": "0x6B580"}, "bgfx::StateCacheT<ID3D11RasterizerState>::~StateCacheT<ID3D11RasterizerState>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D11SamplerState>::find": {"offset": "0x68920"}, "bgfx::StateCacheT<ID3D11SamplerState>::invalidate": {"offset": "0x6B580"}, "bgfx::StateCacheT<ID3D11SamplerState>::~StateCacheT<ID3D11SamplerState>": {"offset": "0x58750"}, "bgfx::StateCacheT<ID3D12PipelineState>::add": {"offset": "0x745D0"}, "bgfx::StateCacheT<ID3D12PipelineState>::find": {"offset": "0x68920"}, "bgfx::StateCacheT<ID3D12PipelineState>::invalidate": {"offset": "0x6B580"}, "bgfx::StateCacheT<ID3D12PipelineState>::~StateCacheT<ID3D12PipelineState>": {"offset": "0x58750"}, "bgfx::StateCacheT<IDirect3DVertexDeclaration9>::~StateCacheT<IDirect3DVertexDeclaration9>": {"offset": "0x58750"}, "bgfx::TextVideoMem::printf": {"offset": "0x5E110"}, "bgfx::TextVideoMem::printfVargs": {"offset": "0x53160"}, "bgfx::TextVideoMem::resize": {"offset": "0x555C0"}, "bgfx::TextVideoMem::~TextVideoMem": {"offset": "0x648E0"}, "bgfx::TextVideoMemBlitter::init": {"offset": "0x52180"}, "bgfx::TextureRef::TextureRef": {"offset": "0x4BC00"}, "bgfx::TextureRef::~TextureRef": {"offset": "0x4BFE0"}, "bgfx::TinyStlAllocator::static_allocate": {"offset": "0x56610"}, "bgfx::TinyStlAllocator::static_deallocate": {"offset": "0x56640"}, "bgfx::UniformBuffer::create": {"offset": "0x5A7D0"}, "bgfx::UniformBuffer::finish": {"offset": "0x5BCF0"}, "bgfx::UniformBuffer::write": {"offset": "0x917B0"}, "bgfx::UniformBuffer::writeUniformHandle": {"offset": "0x57540"}, "bgfx::UniformRef::UniformRef": {"offset": "0x4BC00"}, "bgfx::UniformRef::~UniformRef": {"offset": "0x4BFE0"}, "bgfx::UniformRegistry::find": {"offset": "0x5BC80"}, "bgfx::VertexBuffer::VertexBuffer": {"offset": "0x4BC00"}, "bgfx::VertexBuffer::~VertexBuffer": {"offset": "0x4BFE0"}, "bgfx::VertexLayout::VertexLayout": {"offset": "0x4A0D0"}, "bgfx::VertexLayout::add": {"offset": "0x4A0E0"}, "bgfx::VertexLayout::begin": {"offset": "0x4A2D0"}, "bgfx::VertexLayout::decode": {"offset": "0x4A310"}, "bgfx::VertexLayout::end": {"offset": "0x4A350"}, "bgfx::VertexLayoutRef::find": {"offset": "0x50420"}, "bgfx::VertexLayoutRef::init": {"offset": "0x52780"}, "bgfx::VertexLayoutRef::shutdown<64>": {"offset": "0x4B220"}, "bgfx::ViewState::reset": {"offset": "0x5E6A0"}, "bgfx::ViewState::setPredefined<1,bgfx::gl::RendererContextGL,bgfx::gl::ProgramGL,bgfx::RenderCompute>": {"offset": "0x830D0"}, "bgfx::ViewState::setPredefined<1,bgfx::gl::RendererContextGL,bgfx::gl::ProgramGL,bgfx::RenderDraw>": {"offset": "0x839A0"}, "bgfx::ViewState::setPredefined<4,bgfx::d3d11::RendererContextD3D11,bgfx::d3d11::ProgramD3D11,bgfx::RenderCompute>": {"offset": "0x62C50"}, "bgfx::ViewState::setPredefined<4,bgfx::d3d11::RendererContextD3D11,bgfx::d3d11::ProgramD3D11,bgfx::RenderDraw>": {"offset": "0x63760"}, "bgfx::ViewState::setPredefined<4,bgfx::d3d12::RendererContextD3D12,bgfx::d3d12::ProgramD3D12,bgfx::RenderCompute>": {"offset": "0x72860"}, "bgfx::ViewState::setPredefined<4,bgfx::d3d12::RendererContextD3D12,bgfx::d3d12::ProgramD3D12,bgfx::RenderDraw>": {"offset": "0x731B0"}, "bgfx::ViewState::setPredefined<4,bgfx::d3d9::RendererContextD3D9,bgfx::d3d9::ProgramD3D9,bgfx::RenderDraw>": {"offset": "0x579E0"}, "bgfx::ViewState::setPredefined<4,bgfx::vk::RendererContextVK,bgfx::vk::ProgramVK,bgfx::RenderCompute>": {"offset": "0x91800"}, "bgfx::ViewState::setPredefined<4,bgfx::vk::RendererContextVK,bgfx::vk::ProgramVK,bgfx::RenderDraw>": {"offset": "0x92150"}, "bgfx::alloc": {"offset": "0x4C4D0"}, "bgfx::allocTransientBuffers": {"offset": "0x4C510"}, "bgfx::allocTransientIndexBuffer": {"offset": "0x4C5D0"}, "bgfx::blit": {"offset": "0x4CAF0"}, "bgfx::calcNumMips": {"offset": "0x4CF10"}, "bgfx::compareDescending": {"offset": "0x4D250"}, "bgfx::copy": {"offset": "0x4D260"}, "bgfx::createEmbeddedShader": {"offset": "0x4D5B0"}, "bgfx::createIndexBuffer": {"offset": "0x4D9B0"}, "bgfx::createProgram": {"offset": "0x4DE80"}, "bgfx::createShader": {"offset": "0x4E640"}, "bgfx::createTexture2D": {"offset": "0x4E6B0"}, "bgfx::createUniform": {"offset": "0x4F450"}, "bgfx::createVertexBuffer": {"offset": "0x4F720"}, "bgfx::d3d11::BufferD3D11::create": {"offset": "0x66410"}, "bgfx::d3d11::BufferD3D11::update": {"offset": "0x71D10"}, "bgfx::d3d11::DirectAccessResourceD3D11::createTexture2D": {"offset": "0x67BC0"}, "bgfx::d3d11::DirectAccessResourceD3D11::createTexture3D": {"offset": "0x67CD0"}, "bgfx::d3d11::FrameBufferD3D11::destroy": {"offset": "0x68040"}, "bgfx::d3d11::FrameBufferD3D11::postReset": {"offset": "0x6BBF0"}, "bgfx::d3d11::FrameBufferD3D11::preReset": {"offset": "0x6C350"}, "bgfx::d3d11::OcclusionQueryD3D11::begin": {"offset": "0x64DB0"}, "bgfx::d3d11::OcclusionQueryD3D11::end": {"offset": "0x684E0"}, "bgfx::d3d11::OcclusionQueryD3D11::resolve": {"offset": "0x6CB10"}, "bgfx::d3d11::RendererContextD3D11::RendererContextD3D11": {"offset": "0x64270"}, "bgfx::d3d11::RendererContextD3D11::blitRender": {"offset": "0x65090"}, "bgfx::d3d11::RendererContextD3D11::blitSetup": {"offset": "0x65220"}, "bgfx::d3d11::RendererContextD3D11::capture": {"offset": "0x65540"}, "bgfx::d3d11::RendererContextD3D11::capturePostReset": {"offset": "0x65670"}, "bgfx::d3d11::RendererContextD3D11::capturePreReset": {"offset": "0x657B0"}, "bgfx::d3d11::RendererContextD3D11::clear": {"offset": "0x65810"}, "bgfx::d3d11::RendererContextD3D11::clearQuad": {"offset": "0x65AE0"}, "bgfx::d3d11::RendererContextD3D11::commit": {"offset": "0x65EF0"}, "bgfx::d3d11::RendererContextD3D11::commitShaderConstants": {"offset": "0x66290"}, "bgfx::d3d11::RendererContextD3D11::commitTextureStage": {"offset": "0x66360"}, "bgfx::d3d11::RendererContextD3D11::createDynamicIndexBuffer": {"offset": "0x676D0"}, "bgfx::d3d11::RendererContextD3D11::createDynamicVertexBuffer": {"offset": "0x67710"}, "bgfx::d3d11::RendererContextD3D11::createFrameBuffer": {"offset": "0x67800"}, "bgfx::d3d11::RendererContextD3D11::createIndexBuffer": {"offset": "0x67A90"}, "bgfx::d3d11::RendererContextD3D11::createProgram": {"offset": "0x67AD0"}, "bgfx::d3d11::RendererContextD3D11::createShader": {"offset": "0x67BA0"}, "bgfx::d3d11::RendererContextD3D11::createTexture": {"offset": "0x67DE0"}, "bgfx::d3d11::RendererContextD3D11::createUniform": {"offset": "0x67E20"}, "bgfx::d3d11::RendererContextD3D11::createVertexBuffer": {"offset": "0x67F90"}, "bgfx::d3d11::RendererContextD3D11::createVertexLayout": {"offset": "0x68000"}, "bgfx::d3d11::RendererContextD3D11::destroyDynamicIndexBuffer": {"offset": "0x68290"}, "bgfx::d3d11::RendererContextD3D11::destroyDynamicVertexBuffer": {"offset": "0x68300"}, "bgfx::d3d11::RendererContextD3D11::destroyFrameBuffer": {"offset": "0x68370"}, "bgfx::d3d11::RendererContextD3D11::destroyIndexBuffer": {"offset": "0x68290"}, "bgfx::d3d11::RendererContextD3D11::destroyProgram": {"offset": "0x68400"}, "bgfx::d3d11::RendererContextD3D11::destroyShader": {"offset": "0x68430"}, "bgfx::d3d11::RendererContextD3D11::destroyTexture": {"offset": "0x68450"}, "bgfx::d3d11::RendererContextD3D11::destroyUniform": {"offset": "0x68470"}, "bgfx::d3d11::RendererContextD3D11::destroyVertexBuffer": {"offset": "0x68300"}, "bgfx::d3d11::RendererContextD3D11::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::d3d11::RendererContextD3D11::flip": {"offset": "0x689C0"}, "bgfx::d3d11::RendererContextD3D11::getCachedSrv": {"offset": "0x68C50"}, "bgfx::d3d11::RendererContextD3D11::getCachedUav": {"offset": "0x690F0"}, "bgfx::d3d11::RendererContextD3D11::getInternal": {"offset": "0x694A0"}, "bgfx::d3d11::RendererContextD3D11::getRendererName": {"offset": "0x69560"}, "bgfx::d3d11::RendererContextD3D11::getRendererType": {"offset": "0x69570"}, "bgfx::d3d11::RendererContextD3D11::getSamplerState": {"offset": "0x69580"}, "bgfx::d3d11::RendererContextD3D11::init": {"offset": "0x6A230"}, "bgfx::d3d11::RendererContextD3D11::invalidateCache": {"offset": "0x6B610"}, "bgfx::d3d11::RendererContextD3D11::invalidateCompute": {"offset": "0x6B780"}, "bgfx::d3d11::RendererContextD3D11::invalidateOcclusionQuery": {"offset": "0x6B830"}, "bgfx::d3d11::RendererContextD3D11::isDeviceRemoved": {"offset": "0x6BA70"}, "bgfx::d3d11::RendererContextD3D11::isVisible": {"offset": "0x6BA80"}, "bgfx::d3d11::RendererContextD3D11::overrideInternal": {"offset": "0x6BB30"}, "bgfx::d3d11::RendererContextD3D11::postReset": {"offset": "0x6C030"}, "bgfx::d3d11::RendererContextD3D11::preReset": {"offset": "0x6C3F0"}, "bgfx::d3d11::RendererContextD3D11::readTexture": {"offset": "0x6C520"}, "bgfx::d3d11::RendererContextD3D11::requestScreenShot": {"offset": "0x6C780"}, "bgfx::d3d11::RendererContextD3D11::resizeTexture": {"offset": "0x6C9B0"}, "bgfx::d3d11::RendererContextD3D11::setBlendState": {"offset": "0x6CC60"}, "bgfx::d3d11::RendererContextD3D11::setDepthStencilState": {"offset": "0x6D6F0"}, "bgfx::d3d11::RendererContextD3D11::setFrameBuffer": {"offset": "0x6DEF0"}, "bgfx::d3d11::RendererContextD3D11::setInputLayout": {"offset": "0x6E1C0"}, "bgfx::d3d11::RendererContextD3D11::setMarker": {"offset": "0xA440"}, "bgfx::d3d11::RendererContextD3D11::setName": {"offset": "0x6EB10"}, "bgfx::d3d11::RendererContextD3D11::setRasterizerState": {"offset": "0x6EBE0"}, "bgfx::d3d11::RendererContextD3D11::shutdown": {"offset": "0x6EF40"}, "bgfx::d3d11::RendererContextD3D11::submit": {"offset": "0x6F300"}, "bgfx::d3d11::RendererContextD3D11::submitBlit": {"offset": "0x71950"}, "bgfx::d3d11::RendererContextD3D11::updateDynamicIndexBuffer": {"offset": "0x72220"}, "bgfx::d3d11::RendererContextD3D11::updateDynamicVertexBuffer": {"offset": "0x72260"}, "bgfx::d3d11::RendererContextD3D11::updateMsaa": {"offset": "0x722A0"}, "bgfx::d3d11::RendererContextD3D11::updateResolution": {"offset": "0x72360"}, "bgfx::d3d11::RendererContextD3D11::updateTexture": {"offset": "0x72760"}, "bgfx::d3d11::RendererContextD3D11::updateTextureBegin": {"offset": "0xA440"}, "bgfx::d3d11::RendererContextD3D11::updateTextureEnd": {"offset": "0xA440"}, "bgfx::d3d11::RendererContextD3D11::updateUniform": {"offset": "0x727D0"}, "bgfx::d3d11::RendererContextD3D11::updateViewName": {"offset": "0x727F0"}, "bgfx::d3d11::ShaderD3D11::create": {"offset": "0x66670"}, "bgfx::d3d11::ShaderD3D11::destroy": {"offset": "0x68110"}, "bgfx::d3d11::TextureD3D11::commit": {"offset": "0x661C0"}, "bgfx::d3d11::TextureD3D11::create": {"offset": "0x66DD0"}, "bgfx::d3d11::TextureD3D11::destroy": {"offset": "0x681A0"}, "bgfx::d3d11::TextureD3D11::getSrvFormat": {"offset": "0x6A1D0"}, "bgfx::d3d11::TextureD3D11::update": {"offset": "0x71E80"}, "bgfx::d3d11::TimerQueryD3D11::begin": {"offset": "0x64EE0"}, "bgfx::d3d11::TimerQueryD3D11::end": {"offset": "0x68570"}, "bgfx::d3d11::TimerQueryD3D11::update": {"offset": "0x72090"}, "bgfx::d3d11::`dynamic initializer for 's_zero''": {"offset": "0x1850"}, "bgfx::d3d11::amdAgsMultiDrawIndexedInstancedIndirect": {"offset": "0x64C90"}, "bgfx::d3d11::amdAgsMultiDrawInstancedIndirect": {"offset": "0x64CC0"}, "bgfx::d3d11::getLostReason": {"offset": "0x694E0"}, "bgfx::d3d11::nvapiMultiDrawIndexedInstancedIndirect": {"offset": "0x6BAD0"}, "bgfx::d3d11::nvapiMultiDrawInstancedIndirect": {"offset": "0x6BB00"}, "bgfx::d3d11::rendererCreate": {"offset": "0x6C640"}, "bgfx::d3d11::rendererDestroy": {"offset": "0x6C710"}, "bgfx::d3d11::setDebugObjectName<ID3D11Buffer>": {"offset": "0x62C40"}, "bgfx::d3d11::setDebugObjectName<ID3D11DeviceChild>": {"offset": "0x62C40"}, "bgfx::d3d11::setDebugObjectName<ID3D11Resource>": {"offset": "0x62C40"}, "bgfx::d3d11::setIntelDirectAccessResource": {"offset": "0x6EA90"}, "bgfx::d3d11::stubMultiDrawIndexedInstancedIndirect": {"offset": "0x6F200"}, "bgfx::d3d11::stubMultiDrawInstancedIndirect": {"offset": "0x6F280"}, "bgfx::d3d12::BatchD3D12::create": {"offset": "0x76080"}, "bgfx::d3d12::BatchD3D12::draw": {"offset": "0x784F0"}, "bgfx::d3d12::BatchD3D12::end": {"offset": "0x78990"}, "bgfx::d3d12::BatchD3D12::flush": {"offset": "0x792E0"}, "bgfx::d3d12::BufferD3D12::create": {"offset": "0x76320"}, "bgfx::d3d12::BufferD3D12::destroy": {"offset": "0x78160"}, "bgfx::d3d12::BufferD3D12::setState": {"offset": "0x7ECB0"}, "bgfx::d3d12::BufferD3D12::update": {"offset": "0x82630"}, "bgfx::d3d12::CommandQueueD3D12::alloc": {"offset": "0x74740"}, "bgfx::d3d12::CommandQueueD3D12::consume": {"offset": "0x75F50"}, "bgfx::d3d12::CommandQueueD3D12::init": {"offset": "0x7B880"}, "bgfx::d3d12::CommandQueueD3D12::kick": {"offset": "0x7CCA0"}, "bgfx::d3d12::CommandQueueD3D12::release": {"offset": "0x7DA50"}, "bgfx::d3d12::CommandQueueD3D12::shutdown": {"offset": "0x7EDE0"}, "bgfx::d3d12::CommandQueueD3D12::~CommandQueueD3D12": {"offset": "0x73F70"}, "bgfx::d3d12::DescriptorAllocatorD3D12::alloc": {"offset": "0x74860"}, "bgfx::d3d12::DescriptorAllocatorD3D12::create": {"offset": "0x764E0"}, "bgfx::d3d12::FrameBufferD3D12::clear": {"offset": "0x755F0"}, "bgfx::d3d12::FrameBufferD3D12::create": {"offset": "0x765C0"}, "bgfx::d3d12::FrameBufferD3D12::postReset": {"offset": "0x7CF10"}, "bgfx::d3d12::FrameBufferD3D12::setState": {"offset": "0x7ED20"}, "bgfx::d3d12::OcclusionQueryD3D12::begin": {"offset": "0x74DC0"}, "bgfx::d3d12::OcclusionQueryD3D12::end": {"offset": "0x789D0"}, "bgfx::d3d12::OcclusionQueryD3D12::init": {"offset": "0x7B980"}, "bgfx::d3d12::RendererContextD3D12::RendererContextD3D12": {"offset": "0x73B20"}, "bgfx::d3d12::RendererContextD3D12::blitRender": {"offset": "0x75140"}, "bgfx::d3d12::RendererContextD3D12::blitSetup": {"offset": "0x75220"}, "bgfx::d3d12::RendererContextD3D12::clear": {"offset": "0x75910"}, "bgfx::d3d12::RendererContextD3D12::clearQuad": {"offset": "0x75AA0"}, "bgfx::d3d12::RendererContextD3D12::commit": {"offset": "0x75B80"}, "bgfx::d3d12::RendererContextD3D12::commitShaderConstants": {"offset": "0x75E40"}, "bgfx::d3d12::RendererContextD3D12::createDynamicIndexBuffer": {"offset": "0x77C20"}, "bgfx::d3d12::RendererContextD3D12::createDynamicVertexBuffer": {"offset": "0x77C60"}, "bgfx::d3d12::RendererContextD3D12::createFrameBuffer": {"offset": "0x77D10"}, "bgfx::d3d12::RendererContextD3D12::createIndexBuffer": {"offset": "0x77DF0"}, "bgfx::d3d12::RendererContextD3D12::createProgram": {"offset": "0x77E30"}, "bgfx::d3d12::RendererContextD3D12::createShader": {"offset": "0x77F00"}, "bgfx::d3d12::RendererContextD3D12::createTexture": {"offset": "0x77F20"}, "bgfx::d3d12::RendererContextD3D12::createUniform": {"offset": "0x77F50"}, "bgfx::d3d12::RendererContextD3D12::createVertexBuffer": {"offset": "0x780C0"}, "bgfx::d3d12::RendererContextD3D12::createVertexLayout": {"offset": "0x78120"}, "bgfx::d3d12::RendererContextD3D12::destroyDynamicIndexBuffer": {"offset": "0x78290"}, "bgfx::d3d12::RendererContextD3D12::destroyDynamicVertexBuffer": {"offset": "0x782E0"}, "bgfx::d3d12::RendererContextD3D12::destroyFrameBuffer": {"offset": "0x78330"}, "bgfx::d3d12::RendererContextD3D12::destroyIndexBuffer": {"offset": "0x78290"}, "bgfx::d3d12::RendererContextD3D12::destroyProgram": {"offset": "0x78410"}, "bgfx::d3d12::RendererContextD3D12::destroyShader": {"offset": "0x78440"}, "bgfx::d3d12::RendererContextD3D12::destroyTexture": {"offset": "0x78460"}, "bgfx::d3d12::RendererContextD3D12::destroyUniform": {"offset": "0x78480"}, "bgfx::d3d12::RendererContextD3D12::destroyVertexBuffer": {"offset": "0x782E0"}, "bgfx::d3d12::RendererContextD3D12::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::d3d12::RendererContextD3D12::finish": {"offset": "0x78F40"}, "bgfx::d3d12::RendererContextD3D12::finishAll": {"offset": "0x78FD0"}, "bgfx::d3d12::RendererContextD3D12::flip": {"offset": "0x79090"}, "bgfx::d3d12::RendererContextD3D12::getInternal": {"offset": "0x79570"}, "bgfx::d3d12::RendererContextD3D12::getPipelineState": {"offset": "0x797E0"}, "bgfx::d3d12::RendererContextD3D12::getRendererName": {"offset": "0x7B540"}, "bgfx::d3d12::RendererContextD3D12::getRendererType": {"offset": "0x7B550"}, "bgfx::d3d12::RendererContextD3D12::getRtv": {"offset": "0x7B610"}, "bgfx::d3d12::RendererContextD3D12::getSamplerState": {"offset": "0x7B690"}, "bgfx::d3d12::RendererContextD3D12::init": {"offset": "0x7BA40"}, "bgfx::d3d12::RendererContextD3D12::invalidateCache": {"offset": "0x7CBA0"}, "bgfx::d3d12::RendererContextD3D12::invalidateOcclusionQuery": {"offset": "0x7CC10"}, "bgfx::d3d12::RendererContextD3D12::isDeviceRemoved": {"offset": "0x7CC90"}, "bgfx::d3d12::RendererContextD3D12::kick": {"offset": "0x7CDA0"}, "bgfx::d3d12::RendererContextD3D12::overrideInternal": {"offset": "0x7CDF0"}, "bgfx::d3d12::RendererContextD3D12::patchCb0": {"offset": "0x7CE50"}, "bgfx::d3d12::RendererContextD3D12::postReset": {"offset": "0x7D360"}, "bgfx::d3d12::RendererContextD3D12::preReset": {"offset": "0x7D700"}, "bgfx::d3d12::RendererContextD3D12::readTexture": {"offset": "0x7D7F0"}, "bgfx::d3d12::RendererContextD3D12::requestScreenShot": {"offset": "0x7DCE0"}, "bgfx::d3d12::RendererContextD3D12::resizeTexture": {"offset": "0x7DFE0"}, "bgfx::d3d12::RendererContextD3D12::setBlendState": {"offset": "0x7E140"}, "bgfx::d3d12::RendererContextD3D12::setFrameBuffer": {"offset": "0x7E4B0"}, "bgfx::d3d12::RendererContextD3D12::setInputLayout": {"offset": "0x7E880"}, "bgfx::d3d12::RendererContextD3D12::setMarker": {"offset": "0xA440"}, "bgfx::d3d12::RendererContextD3D12::setName": {"offset": "0x7EC20"}, "bgfx::d3d12::RendererContextD3D12::shutdown": {"offset": "0x7EED0"}, "bgfx::d3d12::RendererContextD3D12::submit": {"offset": "0x7F410"}, "bgfx::d3d12::RendererContextD3D12::submitBlit": {"offset": "0x821E0"}, "bgfx::d3d12::RendererContextD3D12::updateDynamicIndexBuffer": {"offset": "0x82B10"}, "bgfx::d3d12::RendererContextD3D12::updateDynamicVertexBuffer": {"offset": "0x82B60"}, "bgfx::d3d12::RendererContextD3D12::updateMsaa": {"offset": "0x82BB0"}, "bgfx::d3d12::RendererContextD3D12::updateResolution": {"offset": "0x82CA0"}, "bgfx::d3d12::RendererContextD3D12::updateTexture": {"offset": "0x83000"}, "bgfx::d3d12::RendererContextD3D12::updateTextureBegin": {"offset": "0xA440"}, "bgfx::d3d12::RendererContextD3D12::updateTextureEnd": {"offset": "0xA440"}, "bgfx::d3d12::RendererContextD3D12::updateUniform": {"offset": "0x83040"}, "bgfx::d3d12::RendererContextD3D12::updateViewName": {"offset": "0x83060"}, "bgfx::d3d12::ScratchBufferD3D12::ScratchBufferD3D12": {"offset": "0x17690"}, "bgfx::d3d12::ScratchBufferD3D12::allocSrv": {"offset": "0x74B10"}, "bgfx::d3d12::ScratchBufferD3D12::allocUav": {"offset": "0x74C30"}, "bgfx::d3d12::ScratchBufferD3D12::create": {"offset": "0x767F0"}, "bgfx::d3d12::ScratchBufferD3D12::~ScratchBufferD3D12": {"offset": "0xA440"}, "bgfx::d3d12::ShaderD3D12::create": {"offset": "0x76900"}, "bgfx::d3d12::ShaderD3D12::destroy": {"offset": "0x781A0"}, "bgfx::d3d12::TextureD3D12::create": {"offset": "0x77130"}, "bgfx::d3d12::TextureD3D12::destroy": {"offset": "0x78200"}, "bgfx::d3d12::TextureD3D12::setState": {"offset": "0x7ECB0"}, "bgfx::d3d12::TextureD3D12::update": {"offset": "0x827B0"}, "bgfx::d3d12::TimerQueryD3D12::begin": {"offset": "0x74F70"}, "bgfx::d3d12::TimerQueryD3D12::end": {"offset": "0x78AB0"}, "bgfx::d3d12::TimerQueryD3D12::init": {"offset": "0x7C820"}, "bgfx::d3d12::createCommittedResource": {"offset": "0x77B70"}, "bgfx::d3d12::fill": {"offset": "0x78C20"}, "bgfx::d3d12::getLostReason": {"offset": "0x694E0"}, "bgfx::d3d12::initHeapProperties": {"offset": "0x7C940"}, "bgfx::d3d12::rendererCreate": {"offset": "0x7DBA0"}, "bgfx::d3d12::rendererDestroy": {"offset": "0x7DC70"}, "bgfx::d3d12::setDebugObjectName": {"offset": "0x62C40"}, "bgfx::d3d9::FrameBufferD3D9::create": {"offset": "0x59B80"}, "bgfx::d3d9::FrameBufferD3D9::preReset": {"offset": "0x5DC30"}, "bgfx::d3d9::IndexBufferD3D9::create": {"offset": "0x59D90"}, "bgfx::d3d9::IndexBufferD3D9::preReset": {"offset": "0x5DCE0"}, "bgfx::d3d9::OcclusionQueryD3D9::begin": {"offset": "0x588E0"}, "bgfx::d3d9::OcclusionQueryD3D9::end": {"offset": "0x5BA60"}, "bgfx::d3d9::OcclusionQueryD3D9::resolve": {"offset": "0x5EA10"}, "bgfx::d3d9::RendererContextD3D9::RendererContextD3D9": {"offset": "0x584C0"}, "bgfx::d3d9::RendererContextD3D9::blitRender": {"offset": "0x58BB0"}, "bgfx::d3d9::RendererContextD3D9::blitSetup": {"offset": "0x58D30"}, "bgfx::d3d9::RendererContextD3D9::capture": {"offset": "0x59050"}, "bgfx::d3d9::RendererContextD3D9::clearQuad": {"offset": "0x59190"}, "bgfx::d3d9::RendererContextD3D9::commit": {"offset": "0x59750"}, "bgfx::d3d9::RendererContextD3D9::createDynamicIndexBuffer": {"offset": "0x5A9F0"}, "bgfx::d3d9::RendererContextD3D9::createDynamicVertexBuffer": {"offset": "0x5AA10"}, "bgfx::d3d9::RendererContextD3D9::createFrameBuffer": {"offset": "0x5AAD0"}, "bgfx::d3d9::RendererContextD3D9::createIndexBuffer": {"offset": "0x5AC40"}, "bgfx::d3d9::RendererContextD3D9::createProgram": {"offset": "0x5AD90"}, "bgfx::d3d9::RendererContextD3D9::createShader": {"offset": "0x5AE50"}, "bgfx::d3d9::RendererContextD3D9::createTexture": {"offset": "0x5AE70"}, "bgfx::d3d9::RendererContextD3D9::createUniform": {"offset": "0x5B180"}, "bgfx::d3d9::RendererContextD3D9::createVertexBuffer": {"offset": "0x5B2F0"}, "bgfx::d3d9::RendererContextD3D9::createVertexLayout": {"offset": "0x5B420"}, "bgfx::d3d9::RendererContextD3D9::destroyDynamicIndexBuffer": {"offset": "0x5B6C0"}, "bgfx::d3d9::RendererContextD3D9::destroyDynamicVertexBuffer": {"offset": "0x5B730"}, "bgfx::d3d9::RendererContextD3D9::destroyFrameBuffer": {"offset": "0x5B7A0"}, "bgfx::d3d9::RendererContextD3D9::destroyIndexBuffer": {"offset": "0x5B6C0"}, "bgfx::d3d9::RendererContextD3D9::destroyProgram": {"offset": "0x5B920"}, "bgfx::d3d9::RendererContextD3D9::destroyShader": {"offset": "0x5B950"}, "bgfx::d3d9::RendererContextD3D9::destroyTexture": {"offset": "0x5B970"}, "bgfx::d3d9::RendererContextD3D9::destroyUniform": {"offset": "0x5B9F0"}, "bgfx::d3d9::RendererContextD3D9::destroyVertexBuffer": {"offset": "0x5B730"}, "bgfx::d3d9::RendererContextD3D9::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::d3d9::RendererContextD3D9::flip": {"offset": "0x5BD30"}, "bgfx::d3d9::RendererContextD3D9::flush": {"offset": "0x5BEF0"}, "bgfx::d3d9::RendererContextD3D9::getInternal": {"offset": "0x5BF30"}, "bgfx::d3d9::RendererContextD3D9::getRendererName": {"offset": "0x5BF70"}, "bgfx::d3d9::RendererContextD3D9::getRendererType": {"offset": "0x5BF90"}, "bgfx::d3d9::RendererContextD3D9::init": {"offset": "0x5C120"}, "bgfx::d3d9::RendererContextD3D9::invalidateOcclusionQuery": {"offset": "0x5D130"}, "bgfx::d3d9::RendererContextD3D9::isDeviceRemoved": {"offset": "0xF110"}, "bgfx::d3d9::RendererContextD3D9::isVisible": {"offset": "0x5D1C0"}, "bgfx::d3d9::RendererContextD3D9::overrideInternal": {"offset": "0x5D4B0"}, "bgfx::d3d9::RendererContextD3D9::postReset": {"offset": "0x5D630"}, "bgfx::d3d9::RendererContextD3D9::preReset": {"offset": "0x5DD10"}, "bgfx::d3d9::RendererContextD3D9::readTexture": {"offset": "0x5E140"}, "bgfx::d3d9::RendererContextD3D9::requestScreenShot": {"offset": "0x5E4A0"}, "bgfx::d3d9::RendererContextD3D9::resizeTexture": {"offset": "0x5E880"}, "bgfx::d3d9::RendererContextD3D9::setFrameBuffer": {"offset": "0x5EB90"}, "bgfx::d3d9::RendererContextD3D9::setInputLayout": {"offset": "0x5EE70"}, "bgfx::d3d9::RendererContextD3D9::setMarker": {"offset": "0xA440"}, "bgfx::d3d9::RendererContextD3D9::setName": {"offset": "0xA440"}, "bgfx::d3d9::RendererContextD3D9::setSamplerState": {"offset": "0x5F8D0"}, "bgfx::d3d9::RendererContextD3D9::shutdown": {"offset": "0x5F930"}, "bgfx::d3d9::RendererContextD3D9::submit": {"offset": "0x5FBA0"}, "bgfx::d3d9::RendererContextD3D9::submitBlit": {"offset": "0x61BA0"}, "bgfx::d3d9::RendererContextD3D9::updateDynamicIndexBuffer": {"offset": "0x62410"}, "bgfx::d3d9::RendererContextD3D9::updateDynamicVertexBuffer": {"offset": "0x624D0"}, "bgfx::d3d9::RendererContextD3D9::updateResolution": {"offset": "0x62590"}, "bgfx::d3d9::RendererContextD3D9::updateTexture": {"offset": "0x62800"}, "bgfx::d3d9::RendererContextD3D9::updateTextureBegin": {"offset": "0x62860"}, "bgfx::d3d9::RendererContextD3D9::updateTextureEnd": {"offset": "0x628E0"}, "bgfx::d3d9::RendererContextD3D9::updateUniform": {"offset": "0x62920"}, "bgfx::d3d9::RendererContextD3D9::updateViewName": {"offset": "0x62940"}, "bgfx::d3d9::ShaderD3D9::create": {"offset": "0x59ED0"}, "bgfx::d3d9::ShaderD3D9::destroy": {"offset": "0x5B650"}, "bgfx::d3d9::TextureD3D9::commit": {"offset": "0x59AD0"}, "bgfx::d3d9::TextureD3D9::create": {"offset": "0x5A350"}, "bgfx::d3d9::TextureD3D9::createCubeTexture": {"offset": "0x5A870"}, "bgfx::d3d9::TextureD3D9::createTexture": {"offset": "0x5AEB0"}, "bgfx::d3d9::TextureD3D9::createVolumeTexture": {"offset": "0x5B460"}, "bgfx::d3d9::TextureD3D9::getSurface": {"offset": "0x5BFA0"}, "bgfx::d3d9::TextureD3D9::lock": {"offset": "0x5D2F0"}, "bgfx::d3d9::TextureD3D9::preReset": {"offset": "0x5E0C0"}, "bgfx::d3d9::TextureD3D9::unlock": {"offset": "0x61E80"}, "bgfx::d3d9::TextureD3D9::update": {"offset": "0x61F80"}, "bgfx::d3d9::TimerQueryD3D9::begin": {"offset": "0x58A10"}, "bgfx::d3d9::TimerQueryD3D9::end": {"offset": "0x5BAE0"}, "bgfx::d3d9::TimerQueryD3D9::update": {"offset": "0x622B0"}, "bgfx::d3d9::VertexBufferD3D9::preReset": {"offset": "0x5DCE0"}, "bgfx::d3d9::rendererCreate": {"offset": "0x5E360"}, "bgfx::d3d9::rendererDestroy": {"offset": "0x5E430"}, "bgfx::destroy": {"offset": "0x4F8D0"}, "bgfx::dump": {"offset": "0xA440"}, "bgfx::dxbcHash": {"offset": "0xB4250"}, "bgfx::dxbcHashBlock": {"offset": "0xB4380"}, "bgfx::fatal": {"offset": "0x502B0"}, "bgfx::filter": {"offset": "0xB4BB0"}, "bgfx::findModule": {"offset": "0xB8600"}, "bgfx::frame": {"offset": "0x50C90"}, "bgfx::getAvailTransientIndexBuffer": {"offset": "0x51300"}, "bgfx::getInternalData": {"offset": "0x51430"}, "bgfx::getName": {"offset": "0x51450"}, "bgfx::getRendererType": {"offset": "0x51470"}, "bgfx::getSupportedRenderers": {"offset": "0x51490"}, "bgfx::getViableTextureFormat": {"offset": "0x515A0"}, "bgfx::gl::FrameBufferGL::destroy": {"offset": "0x88360"}, "bgfx::gl::FrameBufferGL::postReset": {"offset": "0x8B460"}, "bgfx::gl::FrameBufferGL::resolve": {"offset": "0x8C0E0"}, "bgfx::gl::GlContext::create": {"offset": "0xB93E0"}, "bgfx::gl::GlContext::createSwapChain": {"offset": "0xB9AB0"}, "bgfx::gl::GlContext::destroy": {"offset": "0xB9B50"}, "bgfx::gl::GlContext::destroySwapChain": {"offset": "0xB9BB0"}, "bgfx::gl::GlContext::getCaps": {"offset": "0xB9C40"}, "bgfx::gl::GlContext::import": {"offset": "0xB9C50"}, "bgfx::gl::GlContext::makeCurrent": {"offset": "0xBF620"}, "bgfx::gl::GlContext::resize": {"offset": "0xBF670"}, "bgfx::gl::GlContext::swap": {"offset": "0xBF690"}, "bgfx::gl::IndexBufferGL::update": {"offset": "0x90D50"}, "bgfx::gl::LineReader::read": {"offset": "0x8B810"}, "bgfx::gl::OcclusionQueryGL::begin": {"offset": "0x84A70"}, "bgfx::gl::OcclusionQueryGL::create": {"offset": "0x86170"}, "bgfx::gl::OcclusionQueryGL::end": {"offset": "0x88850"}, "bgfx::gl::OcclusionQueryGL::resolve": {"offset": "0x8C300"}, "bgfx::gl::ProgramGL::bindAttributes": {"offset": "0x84E10"}, "bgfx::gl::ProgramGL::bindAttributesEnd": {"offset": "0x84F30"}, "bgfx::gl::ProgramGL::bindInstanceData": {"offset": "0x84F80"}, "bgfx::gl::ProgramGL::create": {"offset": "0x861B0"}, "bgfx::gl::ProgramGL::init": {"offset": "0x88CF0"}, "bgfx::gl::ProgramGL::unbindAttributes": {"offset": "0x90CB0"}, "bgfx::gl::ProgramGL::unbindInstanceData": {"offset": "0x90D00"}, "bgfx::gl::RendererContextGL::RendererContextGL": {"offset": "0x84270"}, "bgfx::gl::RendererContextGL::blitMsaaFbo": {"offset": "0x85020"}, "bgfx::gl::RendererContextGL::blitRender": {"offset": "0x850E0"}, "bgfx::gl::RendererContextGL::blitSetup": {"offset": "0x852A0"}, "bgfx::gl::RendererContextGL::capture": {"offset": "0x85460"}, "bgfx::gl::RendererContextGL::captureFinish": {"offset": "0x85510"}, "bgfx::gl::RendererContextGL::clearQuad": {"offset": "0x855F0"}, "bgfx::gl::RendererContextGL::commit": {"offset": "0x85C90"}, "bgfx::gl::RendererContextGL::createDynamicIndexBuffer": {"offset": "0x87DD0"}, "bgfx::gl::RendererContextGL::createDynamicVertexBuffer": {"offset": "0x87E40"}, "bgfx::gl::RendererContextGL::createFrameBuffer": {"offset": "0x87F50"}, "bgfx::gl::RendererContextGL::createIndexBuffer": {"offset": "0x87FC0"}, "bgfx::gl::RendererContextGL::createProgram": {"offset": "0x88050"}, "bgfx::gl::RendererContextGL::createShader": {"offset": "0x880C0"}, "bgfx::gl::RendererContextGL::createTexture": {"offset": "0x880E0"}, "bgfx::gl::RendererContextGL::createUniform": {"offset": "0x88120"}, "bgfx::gl::RendererContextGL::createVertexBuffer": {"offset": "0x88280"}, "bgfx::gl::RendererContextGL::createVertexLayout": {"offset": "0x88320"}, "bgfx::gl::RendererContextGL::destroyDynamicIndexBuffer": {"offset": "0x883E0"}, "bgfx::gl::RendererContextGL::destroyDynamicVertexBuffer": {"offset": "0x88420"}, "bgfx::gl::RendererContextGL::destroyFrameBuffer": {"offset": "0x88460"}, "bgfx::gl::RendererContextGL::destroyIndexBuffer": {"offset": "0x883E0"}, "bgfx::gl::RendererContextGL::destroyMsaaFbo": {"offset": "0x884D0"}, "bgfx::gl::RendererContextGL::destroyProgram": {"offset": "0x88540"}, "bgfx::gl::RendererContextGL::destroyShader": {"offset": "0x885C0"}, "bgfx::gl::RendererContextGL::destroyTexture": {"offset": "0x88600"}, "bgfx::gl::RendererContextGL::destroyUniform": {"offset": "0x88670"}, "bgfx::gl::RendererContextGL::destroyVertexBuffer": {"offset": "0x88420"}, "bgfx::gl::RendererContextGL::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::gl::RendererContextGL::flip": {"offset": "0x88A80"}, "bgfx::gl::RendererContextGL::getInternal": {"offset": "0x88C40"}, "bgfx::gl::RendererContextGL::getNumRt": {"offset": "0x88C50"}, "bgfx::gl::RendererContextGL::getRendererName": {"offset": "0x88C80"}, "bgfx::gl::RendererContextGL::getRendererType": {"offset": "0x88C90"}, "bgfx::gl::RendererContextGL::init": {"offset": "0x89450"}, "bgfx::gl::RendererContextGL::invalidateOcclusionQuery": {"offset": "0x8B000"}, "bgfx::gl::RendererContextGL::isDeviceRemoved": {"offset": "0xF110"}, "bgfx::gl::RendererContextGL::isVisible": {"offset": "0x8B360"}, "bgfx::gl::RendererContextGL::overrideInternal": {"offset": "0x8B3B0"}, "bgfx::gl::RendererContextGL::readTexture": {"offset": "0x8B940"}, "bgfx::gl::RendererContextGL::requestScreenShot": {"offset": "0x8BDC0"}, "bgfx::gl::RendererContextGL::resizeTexture": {"offset": "0x8BF20"}, "bgfx::gl::RendererContextGL::setFrameBuffer": {"offset": "0x8C430"}, "bgfx::gl::RendererContextGL::setMarker": {"offset": "0x8C700"}, "bgfx::gl::RendererContextGL::setName": {"offset": "0x8C710"}, "bgfx::gl::RendererContextGL::setRenderContextSize": {"offset": "0x8C800"}, "bgfx::gl::RendererContextGL::setSamplerState": {"offset": "0x8C9C0"}, "bgfx::gl::RendererContextGL::submit": {"offset": "0x8DC10"}, "bgfx::gl::RendererContextGL::submitBlit": {"offset": "0x90870"}, "bgfx::gl::RendererContextGL::updateDynamicIndexBuffer": {"offset": "0x91280"}, "bgfx::gl::RendererContextGL::updateDynamicVertexBuffer": {"offset": "0x91300"}, "bgfx::gl::RendererContextGL::updateResolution": {"offset": "0x914F0"}, "bgfx::gl::RendererContextGL::updateTexture": {"offset": "0x916B0"}, "bgfx::gl::RendererContextGL::updateTextureBegin": {"offset": "0xA440"}, "bgfx::gl::RendererContextGL::updateTextureEnd": {"offset": "0xA440"}, "bgfx::gl::RendererContextGL::updateUniform": {"offset": "0x91720"}, "bgfx::gl::RendererContextGL::updateViewName": {"offset": "0x91740"}, "bgfx::gl::SamplerStateCache::find": {"offset": "0x88A00"}, "bgfx::gl::SamplerStateCache::~SamplerStateCache": {"offset": "0x84840"}, "bgfx::gl::ShaderGL::create": {"offset": "0x86530"}, "bgfx::gl::TextureGL::commit": {"offset": "0x85E10"}, "bgfx::gl::TextureGL::create": {"offset": "0x87790"}, "bgfx::gl::TextureGL::init": {"offset": "0x8A760"}, "bgfx::gl::TextureGL::setSamplerState": {"offset": "0x8D4B0"}, "bgfx::gl::TextureGL::update": {"offset": "0x90E30"}, "bgfx::gl::TimerQueryGL::begin": {"offset": "0x84BA0"}, "bgfx::gl::TimerQueryGL::create": {"offset": "0x87D40"}, "bgfx::gl::TimerQueryGL::end": {"offset": "0x888C0"}, "bgfx::gl::UniformStateCache::~UniformStateCache": {"offset": "0x84870"}, "bgfx::gl::VertexBufferGL::update": {"offset": "0x911B0"}, "bgfx::gl::compressedTexImage": {"offset": "0x85EC0"}, "bgfx::gl::convertGlType": {"offset": "0x85FB0"}, "bgfx::gl::createContext": {"offset": "0xB9960"}, "bgfx::gl::debugProcCb": {"offset": "0xA440"}, "bgfx::gl::dumpExtensions": {"offset": "0x886E0"}, "bgfx::gl::getGLStringHash": {"offset": "0x88B40"}, "bgfx::gl::getGlError": {"offset": "0x88C10"}, "bgfx::gl::glGet": {"offset": "0x88CA0"}, "bgfx::gl::initTestTexture": {"offset": "0x8AAB0"}, "bgfx::gl::isFramebufferFormatValid": {"offset": "0x8B080"}, "bgfx::gl::isImageFormatValid": {"offset": "0x8B260"}, "bgfx::gl::rendererCreate": {"offset": "0x8BB70"}, "bgfx::gl::rendererDestroy": {"offset": "0x8BC40"}, "bgfx::gl::stubDrawArraysInstanced": {"offset": "0x8DB30"}, "bgfx::gl::stubDrawElementsInstanced": {"offset": "0x8DB40"}, "bgfx::gl::stubInsertEventMarker": {"offset": "0xA440"}, "bgfx::gl::stubInvalidateFramebuffer": {"offset": "0xA440"}, "bgfx::gl::stubMultiDrawArraysIndirect": {"offset": "0x8DB50"}, "bgfx::gl::stubMultiDrawElementsIndirect": {"offset": "0x8DBA0"}, "bgfx::gl::stubObjectLabel": {"offset": "0xA440"}, "bgfx::gl::stubPolygonMode": {"offset": "0xA440"}, "bgfx::gl::stubPopDebugGroup": {"offset": "0xA440"}, "bgfx::gl::stubPushDebugGroup": {"offset": "0xA440"}, "bgfx::gl::stubVertexAttribDivisor": {"offset": "0xA440"}, "bgfx::gl::texImage": {"offset": "0x90AD0"}, "bgfx::gl::updateExtension": {"offset": "0x91380"}, "bgfx::gnm::rendererCreate": {"offset": "0xEA60"}, "bgfx::gnm::rendererDestroy": {"offset": "0xA440"}, "bgfx::hasVertexStreamChanged": {"offset": "0x5C030"}, "bgfx::idToAttrib": {"offset": "0x4A940"}, "bgfx::init": {"offset": "0x527D0"}, "bgfx::initAttribTypeSizeTable": {"offset": "0x4A980"}, "bgfx::isTextureValid": {"offset": "0x52C50"}, "bgfx::loadRenderDoc": {"offset": "0xB8780"}, "bgfx::makeRef": {"offset": "0x52E70"}, "bgfx::nameToPredefinedUniformEnum": {"offset": "0x52EF0"}, "bgfx::noop::RendererContextNOOP::blitRender": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::blitSetup": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createDynamicIndexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createDynamicVertexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createFrameBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createIndexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createProgram": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createShader": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createTexture": {"offset": "0xEA60"}, "bgfx::noop::RendererContextNOOP::createUniform": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createVertexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::createVertexLayout": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyDynamicIndexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyDynamicVertexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyFrameBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyIndexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyProgram": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyShader": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyTexture": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyUniform": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyVertexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::flip": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::getInternal": {"offset": "0xEA60"}, "bgfx::noop::RendererContextNOOP::getRendererName": {"offset": "0x57700"}, "bgfx::noop::RendererContextNOOP::getRendererType": {"offset": "0xEA60"}, "bgfx::noop::RendererContextNOOP::invalidateOcclusionQuery": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::isDeviceRemoved": {"offset": "0xF110"}, "bgfx::noop::RendererContextNOOP::overrideInternal": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::readTexture": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::requestScreenShot": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::resizeTexture": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::setMarker": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::setName": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::submit": {"offset": "0x57830"}, "bgfx::noop::RendererContextNOOP::updateDynamicIndexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateDynamicVertexBuffer": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateTexture": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateTextureBegin": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateTextureEnd": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateUniform": {"offset": "0xA440"}, "bgfx::noop::RendererContextNOOP::updateViewName": {"offset": "0xA440"}, "bgfx::noop::rendererCreate": {"offset": "0x57710"}, "bgfx::noop::rendererDestroy": {"offset": "0x577D0"}, "bgfx::nvn::rendererCreate": {"offset": "0xEA60"}, "bgfx::nvn::rendererDestroy": {"offset": "0xA440"}, "bgfx::overrideInternal": {"offset": "0x52FC0"}, "bgfx::parse": {"offset": "0xB5280"}, "bgfx::parseAttrTo": {"offset": "0x53040"}, "bgfx::read": {"offset": "0xB6470"}, "bgfx::release": {"offset": "0x53760"}, "bgfx::renderDocTriggerCapture": {"offset": "0xB8920"}, "bgfx::renderFrame": {"offset": "0x53E60"}, "bgfx::rendererCreate": {"offset": "0x53FA0"}, "bgfx::rendererUpdateUniforms": {"offset": "0x55290"}, "bgfx::reset": {"offset": "0x55380"}, "bgfx::setGraphicsDebuggerPresent": {"offset": "0x55BD0"}, "bgfx::setIndexBuffer": {"offset": "0x55C20"}, "bgfx::setPlatformData": {"offset": "0x55E40"}, "bgfx::setState": {"offset": "0x55EB0"}, "bgfx::setTexture": {"offset": "0x55F00"}, "bgfx::setTransform": {"offset": "0x55F80"}, "bgfx::setVertexBuffer": {"offset": "0x56240"}, "bgfx::setViewClear": {"offset": "0x562B0"}, "bgfx::setViewRect": {"offset": "0x56320"}, "bgfx::submit": {"offset": "0x56A10"}, "bgfx::touch": {"offset": "0x56EB0"}, "bgfx::trace": {"offset": "0x56EF0"}, "bgfx::unloadRenderDoc": {"offset": "0xA440"}, "bgfx::updateTexture2D": {"offset": "0x57040"}, "bgfx::vk::BufferVK::create": {"offset": "0x94AC0"}, "bgfx::vk::BufferVK::destroy": {"offset": "0x97370"}, "bgfx::vk::BufferVK::update": {"offset": "0xA1190"}, "bgfx::vk::ProgramVK::create": {"offset": "0x94D20"}, "bgfx::vk::RendererContextVK::RendererContextVK": {"offset": "0x92AA0"}, "bgfx::vk::RendererContextVK::allocDescriptorSet": {"offset": "0x93280"}, "bgfx::vk::RendererContextVK::allocateMemory": {"offset": "0x93AF0"}, "bgfx::vk::RendererContextVK::beginNewCommand": {"offset": "0x93BE0"}, "bgfx::vk::RendererContextVK::blitRender": {"offset": "0x93C60"}, "bgfx::vk::RendererContextVK::blitSetup": {"offset": "0x93D50"}, "bgfx::vk::RendererContextVK::clearQuad": {"offset": "0x94300"}, "bgfx::vk::RendererContextVK::commit": {"offset": "0x94550"}, "bgfx::vk::RendererContextVK::createDynamicIndexBuffer": {"offset": "0x967B0"}, "bgfx::vk::RendererContextVK::createDynamicVertexBuffer": {"offset": "0x967F0"}, "bgfx::vk::RendererContextVK::createFrameBuffer": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::createIndexBuffer": {"offset": "0x969D0"}, "bgfx::vk::RendererContextVK::createProgram": {"offset": "0x96A10"}, "bgfx::vk::RendererContextVK::createShader": {"offset": "0x96A70"}, "bgfx::vk::RendererContextVK::createSwapchain": {"offset": "0x96A90"}, "bgfx::vk::RendererContextVK::createSwapchainFramebuffer": {"offset": "0x96DB0"}, "bgfx::vk::RendererContextVK::createSwapchainRenderPass": {"offset": "0x96E90"}, "bgfx::vk::RendererContextVK::createTexture": {"offset": "0x96FD0"}, "bgfx::vk::RendererContextVK::createUniform": {"offset": "0x97000"}, "bgfx::vk::RendererContextVK::createVertexBuffer": {"offset": "0x97170"}, "bgfx::vk::RendererContextVK::createVertexLayout": {"offset": "0x971D0"}, "bgfx::vk::RendererContextVK::destroyDynamicIndexBuffer": {"offset": "0x97550"}, "bgfx::vk::RendererContextVK::destroyDynamicVertexBuffer": {"offset": "0x97570"}, "bgfx::vk::RendererContextVK::destroyFrameBuffer": {"offset": "0x97590"}, "bgfx::vk::RendererContextVK::destroyIndexBuffer": {"offset": "0x97550"}, "bgfx::vk::RendererContextVK::destroyProgram": {"offset": "0x975E0"}, "bgfx::vk::RendererContextVK::destroyShader": {"offset": "0x97650"}, "bgfx::vk::RendererContextVK::destroyTexture": {"offset": "0x97670"}, "bgfx::vk::RendererContextVK::destroyUniform": {"offset": "0x97690"}, "bgfx::vk::RendererContextVK::destroyVertexBuffer": {"offset": "0x97570"}, "bgfx::vk::RendererContextVK::destroyVertexLayout": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::flip": {"offset": "0x97A20"}, "bgfx::vk::RendererContextVK::getInternal": {"offset": "0xEA60"}, "bgfx::vk::RendererContextVK::getPipeline": {"offset": "0x97E40"}, "bgfx::vk::RendererContextVK::getRenderPass": {"offset": "0x99930"}, "bgfx::vk::RendererContextVK::getRenderPassHashkey": {"offset": "0x99CE0"}, "bgfx::vk::RendererContextVK::getRendererName": {"offset": "0x9A310"}, "bgfx::vk::RendererContextVK::getRendererType": {"offset": "0x9A320"}, "bgfx::vk::RendererContextVK::getSampler": {"offset": "0x9A330"}, "bgfx::vk::RendererContextVK::init": {"offset": "0x9AB00"}, "bgfx::vk::RendererContextVK::initSwapchainImageLayout": {"offset": "0x9D280"}, "bgfx::vk::RendererContextVK::invalidateOcclusionQuery": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::isDeviceRemoved": {"offset": "0xF110"}, "bgfx::vk::RendererContextVK::kick": {"offset": "0x9D3F0"}, "bgfx::vk::RendererContextVK::overrideInternal": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::readTexture": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::releaseSwapchain": {"offset": "0x9D490"}, "bgfx::vk::RendererContextVK::releaseSwapchainFramebuffer": {"offset": "0x9D5C0"}, "bgfx::vk::RendererContextVK::releaseSwapchainRenderPass": {"offset": "0x9D620"}, "bgfx::vk::RendererContextVK::requestScreenShot": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::resizeTexture": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::setBlendState": {"offset": "0x9D7F0"}, "bgfx::vk::RendererContextVK::setFrameBuffer": {"offset": "0x9DA00"}, "bgfx::vk::RendererContextVK::setInputLayout": {"offset": "0x9DD50"}, "bgfx::vk::RendererContextVK::setMarker": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::setName": {"offset": "0x9E330"}, "bgfx::vk::RendererContextVK::shutdown": {"offset": "0x9E3D0"}, "bgfx::vk::RendererContextVK::submit": {"offset": "0x9E920"}, "bgfx::vk::RendererContextVK::submitBlit": {"offset": "0xA0A80"}, "bgfx::vk::RendererContextVK::submitCommandAndWait": {"offset": "0xA10F0"}, "bgfx::vk::RendererContextVK::updateDynamicIndexBuffer": {"offset": "0xA1800"}, "bgfx::vk::RendererContextVK::updateDynamicVertexBuffer": {"offset": "0xA1850"}, "bgfx::vk::RendererContextVK::updateResolution": {"offset": "0xA1AE0"}, "bgfx::vk::RendererContextVK::updateTexture": {"offset": "0xA1DD0"}, "bgfx::vk::RendererContextVK::updateTextureBegin": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::updateTextureEnd": {"offset": "0xA440"}, "bgfx::vk::RendererContextVK::updateUniform": {"offset": "0xA1E10"}, "bgfx::vk::RendererContextVK::updateViewName": {"offset": "0xA1E30"}, "bgfx::vk::ScratchBufferVK::ScratchBufferVK": {"offset": "0x17690"}, "bgfx::vk::ScratchBufferVK::create": {"offset": "0x95050"}, "bgfx::vk::ScratchBufferVK::reset": {"offset": "0x9D7A0"}, "bgfx::vk::ScratchBufferVK::~ScratchBufferVK": {"offset": "0xA440"}, "bgfx::vk::ShaderVK::create": {"offset": "0x951A0"}, "bgfx::vk::ShaderVK::destroy": {"offset": "0x973E0"}, "bgfx::vk::StateCacheT<bgfx::vk::VkDescriptorSetLayout>::add": {"offset": "0x92F80"}, "bgfx::vk::StateCacheT<bgfx::vk::VkDescriptorSetLayout>::find": {"offset": "0x97990"}, "bgfx::vk::StateCacheT<bgfx::vk::VkDescriptorSetLayout>::~StateCacheT<bgfx::vk::VkDescriptorSetLayout>": {"offset": "0x58750"}, "bgfx::vk::StateCacheT<bgfx::vk::VkPipeline>::add": {"offset": "0x93100"}, "bgfx::vk::StateCacheT<bgfx::vk::VkPipeline>::find": {"offset": "0x97990"}, "bgfx::vk::StateCacheT<bgfx::vk::VkPipeline>::invalidate": {"offset": "0x9D380"}, "bgfx::vk::StateCacheT<bgfx::vk::VkPipeline>::~StateCacheT<bgfx::vk::VkPipeline>": {"offset": "0x58750"}, "bgfx::vk::StateCacheT<bgfx::vk::VkRenderPass>::~StateCacheT<bgfx::vk::VkRenderPass>": {"offset": "0x58750"}, "bgfx::vk::StateCacheT<bgfx::vk::VkSampler>::~StateCacheT<bgfx::vk::VkSampler>": {"offset": "0x58750"}, "bgfx::vk::TextureVK::copyBufferToTexture": {"offset": "0x94810"}, "bgfx::vk::TextureVK::create": {"offset": "0x95BC0"}, "bgfx::vk::TextureVK::destroy": {"offset": "0x97470"}, "bgfx::vk::TextureVK::setImageMemoryBarrier": {"offset": "0x9DB70"}, "bgfx::vk::TextureVK::update": {"offset": "0xA13F0"}, "bgfx::vk::debugReportCb": {"offset": "0x97210"}, "bgfx::vk::dumpExtensions": {"offset": "0x976E0"}, "bgfx::vk::rendererCreate": {"offset": "0x9D660"}, "bgfx::vk::rendererDestroy": {"offset": "0x9D730"}, "bgfx::vk::setDebugObjectName<bgfx::vk::VkBuffer>": {"offset": "0x917F0"}, "bgfx::vk::setDebugObjectName<bgfx::vk::VkShaderModule>": {"offset": "0x917F0"}, "bgfx::vk::setImageMemoryBarrier": {"offset": "0x9DBC0"}, "bgfx::vk::stubCmdBeginDebugUtilsLabelEXT": {"offset": "0xA440"}, "bgfx::vk::stubCmdEndDebugUtilsLabelEXT": {"offset": "0xA440"}, "bgfx::vk::stubCmdInsertDebugUtilsLabelEXT": {"offset": "0xA440"}, "bgfx::vk::stubSetDebugUtilsObjectNameEXT": {"offset": "0xEA60"}, "bgfx::vk::updateExtension": {"offset": "0xA18A0"}, "bgfx::vk::updateLayer": {"offset": "0xA19C0"}, "bgfx::vk::vkDestroy": {"offset": "0xA1F60"}, "bgfx::webgpu::rendererCreate": {"offset": "0xEA60"}, "bgfx::webgpu::rendererDestroy": {"offset": "0xA440"}, "bgfx::windowsVersionIs": {"offset": "0x57340"}, "bgfx::write": {"offset": "0xB80C0"}, "bimg::BitReader::read": {"offset": "0xB2CB0"}, "bimg::bc6hUnquantize": {"offset": "0xA8B80"}, "bimg::calcNumMips": {"offset": "0x4CF10"}, "bimg::decodeBlockATC": {"offset": "0xA8C20"}, "bimg::decodeBlockBc6h": {"offset": "0xAA4E0"}, "bimg::decodeBlockBc7": {"offset": "0xAA910"}, "bimg::decodeBlockDxt": {"offset": "0xABA70"}, "bimg::decodeBlockDxt1": {"offset": "0xAB670"}, "bimg::decodeBlockDxt45A": {"offset": "0xAB8B0"}, "bimg::decodeBlockEtc12": {"offset": "0xABC70"}, "bimg::decodeBlockEtc2ModeH": {"offset": "0xAC640"}, "bimg::decodeBlockEtc2ModePlanar": {"offset": "0xACC80"}, "bimg::decodeBlockEtc2ModeT": {"offset": "0xAD1E0"}, "bimg::decodeBlockPtc14": {"offset": "0xAD730"}, "bimg::decodeBlockPtc14A": {"offset": "0xAE210"}, "bimg::getBitsPerPixel": {"offset": "0xAEEC0"}, "bimg::getBlockInfo": {"offset": "0xAEEE0"}, "bimg::getName": {"offset": "0xAEF00"}, "bimg::imageAlloc": {"offset": "0xAEF20"}, "bimg::imageCheckerboard": {"offset": "0xAF1E0"}, "bimg::imageConvert": {"offset": "0xAF590"}, "bimg::imageCopy": {"offset": "0xAF5C0"}, "bimg::imageDecodeToBgra8": {"offset": "0xAF670"}, "bimg::imageDecodeToRgba32f": {"offset": "0xB0720"}, "bimg::imageDecodeToRgba8": {"offset": "0xB1000"}, "bimg::imageGetRawData": {"offset": "0xB1270"}, "bimg::imageGetSize": {"offset": "0xB1580"}, "bimg::imageParse": {"offset": "0xB19C0"}, "bimg::imageParseDds": {"offset": "0xB1A30"}, "bimg::imageParseGnf": {"offset": "0xBF720"}, "bimg::imageParseKtx": {"offset": "0xB2090"}, "bimg::imageParsePvr3": {"offset": "0xB26E0"}, "bimg::imageSwizzleBgra8": {"offset": "0xB2940"}, "bimg::imageWriteTga": {"offset": "0xB2A80"}, "bimg::isCompressed": {"offset": "0xB2C90"}, "bimg::isDepth": {"offset": "0xB2CA0"}, "bx::DefaultAllocator::DefaultAllocator": {"offset": "0xA1FA0"}, "bx::DefaultAllocator::realloc": {"offset": "0xA1FF0"}, "bx::DefaultAllocator::~DefaultAllocator": {"offset": "0xA1FB0"}, "bx::DigitGen": {"offset": "0xA5470"}, "bx::FilePath::FilePath": {"offset": "0xA6480"}, "bx::FilePath::getCPtr": {"offset": "0x17690"}, "bx::FileWriter::FileWriter": {"offset": "0xB2D40"}, "bx::FileWriter::close": {"offset": "0xB2F20"}, "bx::FileWriter::open": {"offset": "0xB2F20"}, "bx::FileWriter::seek": {"offset": "0xB2F20"}, "bx::FileWriter::write": {"offset": "0xB2F20"}, "bx::FileWriter::~FileWriter": {"offset": "0xB2DB0"}, "bx::FileWriterImpl::close": {"offset": "0xB2F30"}, "bx::FileWriterImpl::open": {"offset": "0xB2F60"}, "bx::FileWriterImpl::seek": {"offset": "0xB3070"}, "bx::FileWriterImpl::write": {"offset": "0xB30A0"}, "bx::Grisu2": {"offset": "0xA5820"}, "bx::HandleAlloc::alloc": {"offset": "0x4C4A0"}, "bx::HandleAllocLruT<1024>::alloc": {"offset": "0x64BF0"}, "bx::HandleAllocLruT<1024>::free": {"offset": "0x68BA0"}, "bx::HandleHashMapT<1024,unsigned int>::find": {"offset": "0x503C0"}, "bx::HandleHashMapT<1024,unsigned int>::insert": {"offset": "0x52B30"}, "bx::HandleHashMapT<1024,unsigned int>::removeByHandle": {"offset": "0x53810"}, "bx::HandleHashMapT<1024,unsigned int>::removeIndex": {"offset": "0x5E240"}, "bx::HandleHashMapT<128,unsigned int>::insert": {"offset": "0x52BC0"}, "bx::HandleHashMapT<128,unsigned int>::removeByHandle": {"offset": "0x53970"}, "bx::HandleListT<64>::pushFront": {"offset": "0x7D790"}, "bx::HashMurmur2A::add<bgfx::TextureHandle>": {"offset": "0x49E40"}, "bx::HashMurmur2A::add<bool>": {"offset": "0x629B0"}, "bx::HashMurmur2A::add<unsigned char>": {"offset": "0x629B0"}, "bx::HashMurmur2A::add<unsigned short>": {"offset": "0x49E40"}, "bx::HashMurmur2A::addAligned": {"offset": "0x4C300"}, "bx::HashMurmur2A::addUnaligned": {"offset": "0x4A170"}, "bx::MemoryBlock::getSize": {"offset": "0xB4F70"}, "bx::MemoryBlock::more": {"offset": "0xB5230"}, "bx::MemoryBlock::~MemoryBlock": {"offset": "0xB4100"}, "bx::MemoryReader::read": {"offset": "0x534F0"}, "bx::MemoryReader::seek": {"offset": "0x559F0"}, "bx::MemoryReader::~MemoryReader": {"offset": "0x4C040"}, "bx::MemoryWriter::seek": {"offset": "0x55A70"}, "bx::MemoryWriter::write": {"offset": "0x57400"}, "bx::MemoryWriter::~MemoryWriter": {"offset": "0x4C060"}, "bx::MpScUnboundedBlockingQueue<void>::~MpScUnboundedBlockingQueue<void>": {"offset": "0xA6C10"}, "bx::MpScUnboundedQueueT<void>::~MpScUnboundedQueueT<void>": {"offset": "0xA6C30"}, "bx::Mutex::Mutex": {"offset": "0xA6430"}, "bx::Mutex::lock": {"offset": "0xA6460"}, "bx::Mutex::unlock": {"offset": "0xA6470"}, "bx::Mutex::~Mutex": {"offset": "0xA6450"}, "bx::MutexScope::~MutexScope": {"offset": "0x4C080"}, "bx::Prettify": {"offset": "0xA5AE0"}, "bx::Semaphore::Semaphore": {"offset": "0xA6A50"}, "bx::Semaphore::post": {"offset": "0xA6A90"}, "bx::Semaphore::wait": {"offset": "0xA6AA0"}, "bx::Semaphore::~Semaphore": {"offset": "0xA6A80"}, "bx::SizerWriter::seek": {"offset": "0xA26D0"}, "bx::SizerWriter::write": {"offset": "0xA3A60"}, "bx::SizerWriter::~SizerWriter": {"offset": "0xA2160"}, "bx::StaticMemoryBlock::getSize": {"offset": "0x51480"}, "bx::StaticMemoryBlock::more": {"offset": "0x52EE0"}, "bx::StaticMemoryBlockWriter::~StaticMemoryBlockWriter": {"offset": "0x4C150"}, "bx::StringT<&bgfx::g_allocator>::set": {"offset": "0x55AF0"}, "bx::Thread::Thread": {"offset": "0xA6AD0"}, "bx::Thread::init": {"offset": "0xA6DD0"}, "bx::Thread::setThreadName": {"offset": "0xA6E50"}, "bx::Thread::~Thread": {"offset": "0xA6CA0"}, "bx::ThreadInternal::threadFunc": {"offset": "0xA6F50"}, "bx::WriteExponent": {"offset": "0xA5C30"}, "bx::`anonymous namespace'::write": {"offset": "0xA37A0"}, "bx::alignedAlloc": {"offset": "0x4C440"}, "bx::createHandleAlloc": {"offset": "0x4D790"}, "bx::debugBreak": {"offset": "0xA6300"}, "bx::debugOutput": {"offset": "0xA6310"}, "bx::debugPrintf": {"offset": "0xA6320"}, "bx::debugPrintfVargs": {"offset": "0xA6350"}, "bx::dlclose": {"offset": "0xB3550"}, "bx::dlopen": {"offset": "0xB3560"}, "bx::dlsym": {"offset": "0xB3580"}, "bx::exp": {"offset": "0xB3170"}, "bx::findIdentifierMatch": {"offset": "0xA23E0"}, "bx::fromString": {"offset": "0xA5CE0"}, "bx::getHPCounter": {"offset": "0xA6F90"}, "bx::getHPFrequency": {"offset": "0xA6FB0"}, "bx::getProcessMemoryUsed": {"offset": "0xB3620"}, "bx::halfFromFloat": {"offset": "0x4A670"}, "bx::halfToFloat": {"offset": "0x4A7F0"}, "bx::hash<bx::HashMurmur2A>": {"offset": "0x4AD50"}, "bx::isNumeric": {"offset": "0xA2570"}, "bx::log": {"offset": "0xB32D0"}, "bx::memCmp": {"offset": "0x575C0"}, "bx::memCopy": {"offset": "0x57670"}, "bx::memMove": {"offset": "0x57680"}, "bx::memSet": {"offset": "0x57690"}, "bx::mtxOrtho": {"offset": "0xB3400"}, "bx::normalizeFilePath": {"offset": "0xA64D0"}, "bx::packA8": {"offset": "0xA88C0"}, "bx::packBgra4": {"offset": "0x5D550"}, "bx::packBgra8": {"offset": "0xA7460"}, "bx::packR16": {"offset": "0xA7780"}, "bx::packR16F": {"offset": "0xA78A0"}, "bx::packR16I": {"offset": "0xA7860"}, "bx::packR16S": {"offset": "0xA77F0"}, "bx::packR16U": {"offset": "0xA7860"}, "bx::packR24": {"offset": "0xA8750"}, "bx::packR24G8": {"offset": "0xA87E0"}, "bx::packR32F": {"offset": "0xA7F70"}, "bx::packR32I": {"offset": "0xA7F70"}, "bx::packR32U": {"offset": "0xA7F70"}, "bx::packR5G6B5": {"offset": "0xA81C0"}, "bx::packR8": {"offset": "0xA8920"}, "bx::packR8I": {"offset": "0xA8A00"}, "bx::packR8S": {"offset": "0xA8990"}, "bx::packR8U": {"offset": "0xA8A00"}, "bx::packRG11B10F": {"offset": "0xA8690"}, "bx::packRg16": {"offset": "0xA78F0"}, "bx::packRg16F": {"offset": "0xA7AF0"}, "bx::packRg16I": {"offset": "0xA7A70"}, "bx::packRg16S": {"offset": "0xA79B0"}, "bx::packRg16U": {"offset": "0xA7A70"}, "bx::packRg32F": {"offset": "0xA7F80"}, "bx::packRg32I": {"offset": "0xA7F80"}, "bx::packRg32U": {"offset": "0xA7F80"}, "bx::packRg8": {"offset": "0xA8A50"}, "bx::packRg8I": {"offset": "0xA7020"}, "bx::packRg8S": {"offset": "0xA8B10"}, "bx::packRg8U": {"offset": "0xA7020"}, "bx::packRgb10A2": {"offset": "0xA8530"}, "bx::packRgb5a1": {"offset": "0xA83D0"}, "bx::packRgb8": {"offset": "0xA7090"}, "bx::packRgb8I": {"offset": "0xA7290"}, "bx::packRgb8S": {"offset": "0xA7180"}, "bx::packRgb8U": {"offset": "0xA7290"}, "bx::packRgb9E5F": {"offset": "0xA7F90"}, "bx::packRgba16": {"offset": "0xA7B80"}, "bx::packRgba16F": {"offset": "0xA7EB0"}, "bx::packRgba16I": {"offset": "0xA7E00"}, "bx::packRgba16S": {"offset": "0xA7CB0"}, "bx::packRgba16U": {"offset": "0xA7E00"}, "bx::packRgba32F": {"offset": "0xA81B0"}, "bx::packRgba32I": {"offset": "0xA81B0"}, "bx::packRgba32U": {"offset": "0xA81B0"}, "bx::packRgba4": {"offset": "0xA82F0"}, "bx::packRgba8": {"offset": "0xA7330"}, "bx::packRgba8I": {"offset": "0xA76E0"}, "bx::packRgba8S": {"offset": "0xA7590"}, "bx::packRgba8U": {"offset": "0xA76E0"}, "bx::prettify": {"offset": "0xA2580"}, "bx::quickSort": {"offset": "0xA68B0"}, "bx::quickSortR": {"offset": "0xA6930"}, "bx::radixSort": {"offset": "0x53380"}, "bx::radixSort<unsigned int>": {"offset": "0x4B020"}, "bx::radixSort<unsigned short>": {"offset": "0x4AE20"}, "bx::round": {"offset": "0x5EB20"}, "bx::snprintf": {"offset": "0xA2750"}, "bx::strCat": {"offset": "0xA2900"}, "bx::strCmp": {"offset": "0xA29A0"}, "bx::strCmp<&bx::toLower>": {"offset": "0xA2080"}, "bx::strCmpI": {"offset": "0xA2A20"}, "bx::strCopy": {"offset": "0xA2A50"}, "bx::strFind": {"offset": "0xA2C00"}, "bx::strFindEol": {"offset": "0xA2CC0"}, "bx::strFindNl": {"offset": "0xA30B0"}, "bx::strLTrimSpace": {"offset": "0xA34B0"}, "bx::strLen": {"offset": "0xA35A0"}, "bx::swap": {"offset": "0x576A0"}, "bx::toString": {"offset": "0xA60C0"}, "bx::toUpper": {"offset": "0xA35D0"}, "bx::uint64_cntlz": {"offset": "0xA6210"}, "bx::unpackA8": {"offset": "0xA8900"}, "bx::unpackBgra8": {"offset": "0xA7530"}, "bx::unpackR16": {"offset": "0xA77C0"}, "bx::unpackR16F": {"offset": "0xA78C0"}, "bx::unpackR16I": {"offset": "0xA7870"}, "bx::unpackR16S": {"offset": "0xA7830"}, "bx::unpackR16U": {"offset": "0xA7890"}, "bx::unpackR24": {"offset": "0xA87A0"}, "bx::unpackR24G8": {"offset": "0xA8860"}, "bx::unpackR32F": {"offset": "0xA7F70"}, "bx::unpackR32I": {"offset": "0xA7F70"}, "bx::unpackR32U": {"offset": "0xA7F70"}, "bx::unpackR5G6B5": {"offset": "0xA8280"}, "bx::unpackR8": {"offset": "0xA8960"}, "bx::unpackR8I": {"offset": "0xA8A10"}, "bx::unpackR8S": {"offset": "0xA89D0"}, "bx::unpackR8U": {"offset": "0xA8A30"}, "bx::unpackRG11B10F": {"offset": "0xA86E0"}, "bx::unpackRg16": {"offset": "0xA7970"}, "bx::unpackRg16F": {"offset": "0xA7B30"}, "bx::unpackRg16I": {"offset": "0xA7A90"}, "bx::unpackRg16S": {"offset": "0xA7A20"}, "bx::unpackRg16U": {"offset": "0xA7AC0"}, "bx::unpackRg32F": {"offset": "0xA7F80"}, "bx::unpackRg32I": {"offset": "0xA7F80"}, "bx::unpackRg32U": {"offset": "0xA7F80"}, "bx::unpackRg8": {"offset": "0xA8AD0"}, "bx::unpackRg8I": {"offset": "0xA7030"}, "bx::unpackRg8S": {"offset": "0xA6FD0"}, "bx::unpackRg8U": {"offset": "0xA7060"}, "bx::unpackRgb10A2": {"offset": "0xA8610"}, "bx::unpackRgb5a1": {"offset": "0xA84B0"}, "bx::unpackRgb8": {"offset": "0xA7130"}, "bx::unpackRgb8I": {"offset": "0xA72B0"}, "bx::unpackRgb8S": {"offset": "0xA7220"}, "bx::unpackRgb8U": {"offset": "0xA72F0"}, "bx::unpackRgb9E5F": {"offset": "0xA8100"}, "bx::unpackRgba16": {"offset": "0xA7C50"}, "bx::unpackRgba16F": {"offset": "0xA7F10"}, "bx::unpackRgba16I": {"offset": "0xA7E30"}, "bx::unpackRgba16S": {"offset": "0xA7D80"}, "bx::unpackRgba16U": {"offset": "0xA7E70"}, "bx::unpackRgba32F": {"offset": "0xA81B0"}, "bx::unpackRgba32I": {"offset": "0xA81B0"}, "bx::unpackRgba32U": {"offset": "0xA81B0"}, "bx::unpackRgba4": {"offset": "0x61F00"}, "bx::unpackRgba8": {"offset": "0xA7400"}, "bx::unpackRgba8I": {"offset": "0xA7700"}, "bx::unpackRgba8S": {"offset": "0xA7660"}, "bx::unpackRgba8U": {"offset": "0xA7740"}, "bx::vsnprintf": {"offset": "0xA35F0"}, "bx::write": {"offset": "0xA52B0"}, "bx::writeRep": {"offset": "0xA5320"}, "capture_current_context": {"offset": "0xCDCE8"}, "capture_previous_context": {"offset": "0xCDD58"}, "cef::logging::LogMessage::LogMessage": {"offset": "0xCA040"}, "cef::logging::LogMessage::~LogMessage": {"offset": "0xCA0A0"}, "citizen::Game::Game": {"offset": "0x1D040"}, "citizen::Game::Run": {"offset": "0x21CB0"}, "citizen::Game::~Game": {"offset": "0xE530"}, "citizen::GameImpl::GameImpl": {"offset": "0x1D090"}, "citizen::GameImpl::GetIPC": {"offset": "0x21850"}, "citizen::GameImpl::Run": {"offset": "0x21CC0"}, "citizen::GameImpl::RunMod": {"offset": "0x226D0"}, "citizen::GameWindow::Create": {"offset": "0x328B0"}, "citizen::ModData::~ModData": {"offset": "0x1EAC0"}, "citizen::Win32GameWindow::Close": {"offset": "0x31EB0"}, "citizen::Win32GameWindow::CreateTextures": {"offset": "0x32930"}, "citizen::Win32GameWindow::GetMetrics": {"offset": "0x32B60"}, "citizen::Win32GameWindow::GetNativeHandle": {"offset": "0x32B80"}, "citizen::Win32GameWindow::HandleSizeEvent": {"offset": "0x32CD0"}, "citizen::Win32GameWindow::ProcessEvents": {"offset": "0x33030"}, "citizen::Win32GameWindow::ProcessEventsOnce": {"offset": "0xA440"}, "citizen::Win32GameWindow::Render": {"offset": "0x331D0"}, "citizen::Win32GameWindow::Win32GameWindow": {"offset": "0x2F840"}, "citizen::Win32GameWindow::WindowProcedure": {"offset": "0x33EB0"}, "citizen::Win32GameWindow::WindowProcedureWrapper": {"offset": "0x35120"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13540"}, "console::Printf<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x2E8A0"}, "console::Printfv": {"offset": "0x21AF0"}, "dllmain_crt_dispatch": {"offset": "0xCD7D0"}, "dllmain_crt_process_attach": {"offset": "0xCD820"}, "dllmain_crt_process_detach": {"offset": "0xCD938"}, "dllmain_dispatch": {"offset": "0xCD9BC"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD7E0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA500"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x3EB70"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x3DE60"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x3ECA0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x3DEC0"}, "fmt::v8::detail::add_compare": {"offset": "0x3E2E0"}, "fmt::v8::detail::assert_fail": {"offset": "0x3E420"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x3E470"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x3E640"}, "fmt::v8::detail::bigint::square": {"offset": "0x3F060"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x3DE60"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2D10"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x3F320"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x15B00"}, "fmt::v8::detail::compare": {"offset": "0x3E5A0"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x408C0"}, "fmt::v8::detail::count_digits": {"offset": "0xD5C0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x3A6D0"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x3A7E0"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2DC0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x3EA40"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x3C650"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x3EE40"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x3C680"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x3D450"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x3D020"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x3EDC0"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x3A890"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x3A890"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x40950"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x40A10"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x3E9D0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2DF0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x3BD40"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x30C0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x3BC20"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned __int64,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,0>": {"offset": "0x40CA0"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned __int64>": {"offset": "0x40BA0"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned int>": {"offset": "0x40A90"}, "fmt::v8::detail::format_float<double>": {"offset": "0x3A1E0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x3BE80"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x31A0"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x40D60"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x3C1E0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x32C0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x32C0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x3420"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x40ED0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x3680"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x41150"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE350"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x49CF0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x3C890"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x3CB10"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x3CDA0"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x3CF10"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA560"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xA560"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3750"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE190"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4D50"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x431C0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5D50"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5900"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x6190"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x3DBA0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x3DA80"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5830"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x43E10"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,bool,0>": {"offset": "0x44BB0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x44330"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x43EE0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x44770"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x44C70"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x44DB0"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x65D0"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x44EF0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6610"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x44FE0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x67A0"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x45190"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x7160"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6B70"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x45D60"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x456A0"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA0F0"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0xA0F0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7890"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x463A0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7CB0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7E80"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x8020"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x8020"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x46830"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x81B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x83D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x8550"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9CE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x86E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8900"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8BA0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8DC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8F40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x9160"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x9380"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9500"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9720"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x98A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9AC0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x469B0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x46B50"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x46CF0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x46E80"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x47020"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x471C0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x47360"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x47510"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x476B0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9E70"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x47850"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x47A40"}, "fmt::v8::format_error::format_error": {"offset": "0xA2F0"}, "fmt::v8::format_error::~format_error": {"offset": "0xA690"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x41470"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3BC0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42480"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x39A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42250"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3870"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42140"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3780"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42010"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3BC0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42480"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3A90"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42350"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3CF0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x425C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4850"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x42F40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4280"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x429E0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x1BE00"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x43BA0"}, "fprintf": {"offset": "0xE410"}, "fwEvent<>::ConnectInternal": {"offset": "0x10FF0"}, "fwEvent<wchar_t const *,wchar_t const *>::ConnectInternal": {"offset": "0x10FF0"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA6B0"}, "fwRefContainer<nui::GITexture>::~fwRefContainer<nui::GITexture>": {"offset": "0x10260"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0x10260"}, "fwRefCountable::AddRef": {"offset": "0x49DB0"}, "fwRefCountable::Release": {"offset": "0x49DC0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x49DA0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x31AA0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x2DFD0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::Call": {"offset": "0x31690"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x2DCC0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::Call": {"offset": "0x20A30"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x12C60"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x20E40"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x2F5C0"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0x32BB0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0x32C50"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0x338B0"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x33A90"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0x33D70"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0x33E90"}, "internal::ConsoleVariableEntry<int>::ConsoleVariableEntry<int>": {"offset": "0x2F340"}, "internal::ConsoleVariableEntry<int>::GetOfflineValue": {"offset": "0x32B90"}, "internal::ConsoleVariableEntry<int>::GetValue": {"offset": "0x32C10"}, "internal::ConsoleVariableEntry<int>::SaveOfflineValue": {"offset": "0x338A0"}, "internal::ConsoleVariableEntry<int>::SetRawValue": {"offset": "0x338C0"}, "internal::ConsoleVariableEntry<int>::SetValue": {"offset": "0x33C70"}, "internal::ConsoleVariableEntry<int>::UpdateTrackingVariable": {"offset": "0x33E70"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1BF20"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::GetOfflineValue": {"offset": "0x21900"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::GetValue": {"offset": "0x219C0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SaveOfflineValue": {"offset": "0x22F30"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SetRawValue": {"offset": "0x22F60"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SetValue": {"offset": "0x23380"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::UpdateTrackingVariable": {"offset": "0x23500"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1E0A0"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x322F0"}, "internal::Constraints<int,void>::Compare": {"offset": "0x31EE0"}, "internal::MarkConsoleVarModified": {"offset": "0x32E40"}, "internal::UnparseArgument<int>": {"offset": "0x2E9C0"}, "ipc::Endpoint::Call<>": {"offset": "0x2D050"}, "ipc::Endpoint::Call<int,int>": {"offset": "0x2D260"}, "ipc::Endpoint::Call<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x12790"}, "ipc::Endpoint::Call<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,int,int>": {"offset": "0x2D6C0"}, "ipc::Endpoint::Call<unsigned int>": {"offset": "0x2D4A0"}, "ipc::internal::HandlerFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x12E10"}, "ipc::internal::HandlerFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x13090"}, "ipc::internal::HandlerFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int)> >::CallInternal<1,1,std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> >": {"offset": "0x129F0"}, "ipc::internal::HandlerFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int)> >::CallInternal<2,2,std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int &&> >": {"offset": "0x12AD0"}, "ipc::internal::HandlerFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x21280"}, "launch::GetLaunchModeKey": {"offset": "0x21860"}, "launch::GetProductKey": {"offset": "0x21920"}, "launch::IsSDKGuest": {"offset": "0x21A70"}, "msgpack::v1::container_size_overflow::container_size_overflow": {"offset": "0x1DC50"}, "msgpack::v1::container_size_overflow::~container_size_overflow": {"offset": "0xA690"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_array": {"offset": "0x28C60"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_int32<int>": {"offset": "0x192D0"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint32<unsigned int>": {"offset": "0x2ED50"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_str": {"offset": "0x28E80"}, "msgpack::v1::sbuffer::write": {"offset": "0x2C640"}, "msgpack::v1::sbuffer::~sbuffer": {"offset": "0x1EC30"}, "msgpack::v1::type_error::type_error": {"offset": "0x1E070"}, "msgpack::v1::type_error::~type_error": {"offset": "0xA690"}, "msgpack::v2::object::convert<int>": {"offset": "0x16290"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[22],char const *>": {"offset": "0x15CB0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[23],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15D80"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[24],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15D80"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[25],char const *>": {"offset": "0x15CB0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[26],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char>": {"offset": "0x15E40"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[29],char const *>": {"offset": "0x15CB0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[29],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[5],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15F50"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[39],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15D80"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[52],char const *>": {"offset": "0x15CB0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[6],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[12]>": {"offset": "0x15BC0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[12],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[3],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x16060"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x161C0"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::format_buffer": {"offset": "0x27AD0"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2": {"offset": "0x28010"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2<double>": {"offset": "0x17770"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2_digit_gen": {"offset": "0x28260"}, "nlohmann::json_abi_v3_11_2::detail::exception::exception": {"offset": "0x1DCA0"}, "nlohmann::json_abi_v3_11_2::detail::exception::name": {"offset": "0x28700"}, "nlohmann::json_abi_v3_11_2::detail::exception::what": {"offset": "0x2C620"}, "nlohmann::json_abi_v3_11_2::detail::invalid_iterator::invalid_iterator": {"offset": "0x1DD20"}, "nlohmann::json_abi_v3_11_2::detail::invalid_iterator::~invalid_iterator": {"offset": "0x1EBF0"}, "nlohmann::json_abi_v3_11_2::detail::other_error::create<std::nullptr_t,0>": {"offset": "0x16360"}, "nlohmann::json_abi_v3_11_2::detail::other_error::other_error": {"offset": "0x1DEE0"}, "nlohmann::json_abi_v3_11_2::detail::other_error::~other_error": {"offset": "0x1EBF0"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::create<std::nullptr_t,0>": {"offset": "0x165A0"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::out_of_range": {"offset": "0x1DF10"}, "nlohmann::json_abi_v3_11_2::detail::out_of_range::~out_of_range": {"offset": "0x1EBF0"}, "nlohmann::json_abi_v3_11_2::detail::output_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~output_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x10370"}, "nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::write_character": {"offset": "0x2C740"}, "nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::write_characters": {"offset": "0x2C780"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::create<std::nullptr_t,0>": {"offset": "0x167D0"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::parse_error": {"offset": "0x1DFC0"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::position_string": {"offset": "0x297E0"}, "nlohmann::json_abi_v3_11_2::detail::parse_error::~parse_error": {"offset": "0x1EBF0"}, "nlohmann::json_abi_v3_11_2::detail::type_error::create<std::nullptr_t,0>": {"offset": "0x16A60"}, "nlohmann::json_abi_v3_11_2::detail::type_error::type_error": {"offset": "0x1E000"}, "nlohmann::json_abi_v3_11_2::detail::type_error::~type_error": {"offset": "0x1EBF0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA370"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA410"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1C80"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB4E0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA440"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC630"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC6F0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC960"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xCE00"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA450"}, "rapidjson::internal::DigitGen": {"offset": "0xB790"}, "rapidjson::internal::Grisu2": {"offset": "0xC240"}, "rapidjson::internal::Prettify": {"offset": "0xC7A0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2710"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x27E0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA410"}, "rapidjson::internal::WriteExponent": {"offset": "0xCD70"}, "rapidjson::internal::u32toa": {"offset": "0xD8D0"}, "rapidjson::internal::u64toa": {"offset": "0xDB40"}, "se::Object::~Object": {"offset": "0xA560"}, "seCheckPrivilege": {"offset": "0x35C00"}, "snprintf": {"offset": "0x2C790"}, "sscanf": {"offset": "0x38D00"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<HWND__ * const,citizen::Win32GameWindow *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<HWND__ * const,citizen::Win32GameWindow *>,void *> > >": {"offset": "0x30860"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> >,void *> > >": {"offset": "0xC7AF0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>,void *> > >": {"offset": "0x1E2F0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA480"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<vfs::ManagerServer::MountPoint,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<vfs::ManagerServer::MountPoint,void *> > >": {"offset": "0x36D30"}, "std::_Convert_wide_to_narrow<std::char_traits<char>,std::allocator<char> >": {"offset": "0x35EB0"}, "std::_Default_allocator_traits<std::allocator<astc_codec::IntermediateEndpointData> >::construct<astc_codec::IntermediateEndpointData,astc_codec::IntermediateEndpointData const &>": {"offset": "0xC5260"}, "std::_Destroy_range<std::allocator<astc_codec::IntermediateEndpointData> >": {"offset": "0xC4F60"}, "std::_Destroy_range<std::allocator<fwRefContainer<vfs::Device> > >": {"offset": "0x36090"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2EA90"}, "std::_Facet_Register": {"offset": "0xCCC54"}, "std::_Func_class<bgfx::TextureHandle,FrontendNuiTexture *>::~_Func_class<bgfx::TextureHandle,FrontendNuiTexture *>": {"offset": "0x10230"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x10230"}, "std::_Func_class<bool,wchar_t const *,wchar_t const *>::~_Func_class<bool,wchar_t const *,wchar_t const *>": {"offset": "0x10230"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x10230"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x10230"}, "std::_Func_class<void,int const &>::~_Func_class<void,int const &>": {"offset": "0x10230"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>": {"offset": "0x10230"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x10230"}, "std::_Func_class<void>::_Reset_move": {"offset": "0x117F0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x10230"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Copy": {"offset": "0xEC40"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Delete_this": {"offset": "0xED00"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Do_call": {"offset": "0xECD0"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Move": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_0857a9bc3a63a5f981792fe3ba8c6308>,void>::_Target_type": {"offset": "0xECF0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0x35240"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0x356A0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0x35240"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0x35920"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x23970"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x23EC0"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x24DA0"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Copy": {"offset": "0x239F0"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Do_call": {"offset": "0x23F10"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Move": {"offset": "0x239F0"}, "std::_Func_impl_no_alloc<<lambda_2d93986de81652020adc1c10c1cbba64>,void>::_Target_type": {"offset": "0x24DB0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Copy": {"offset": "0x35260"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Delete_this": {"offset": "0x23DC0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Do_call": {"offset": "0x35780"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Target_type": {"offset": "0x35930"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Copy": {"offset": "0x23A10"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Delete_this": {"offset": "0xF2F0"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Do_call": {"offset": "0x23FF0"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Move": {"offset": "0x23A10"}, "std::_Func_impl_no_alloc<<lambda_49bc29df5ef1c9fd30572dc1450a4842>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int>::_Target_type": {"offset": "0x24DC0"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Copy": {"offset": "0xF290"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Delete_this": {"offset": "0xF2F0"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Do_call": {"offset": "0xF2B0"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Move": {"offset": "0xF290"}, "std::_Func_impl_no_alloc<<lambda_49cc56eaaf180f80d3da54392a236286>,bgfx::TextureHandle,FrontendNuiTexture *>::_Target_type": {"offset": "0xF2E0"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x23A30"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x24000"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x24DD0"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x23AB0"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x24050"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x23AB0"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x24DE0"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Copy": {"offset": "0x23AD0"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Do_call": {"offset": "0x24150"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Move": {"offset": "0x23AD0"}, "std::_Func_impl_no_alloc<<lambda_702b2ef4b1a4ee5eb05cd4c555b67bb8>,void>::_Target_type": {"offset": "0x24DF0"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Copy": {"offset": "0xED50"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Delete_this": {"offset": "0xED00"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Do_call": {"offset": "0xEDE0"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Move": {"offset": "0xEDA0"}, "std::_Func_impl_no_alloc<<lambda_72462e41a1313ca7595f9f1b43bcf853>,void>::_Target_type": {"offset": "0xEF20"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x352C0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x35790"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35940"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x35340"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x357E0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35950"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Copy": {"offset": "0xE9D0"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Delete_this": {"offset": "0xEBC0"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Do_call": {"offset": "0xEA70"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_7d6aeca7e4d2a325a53db3c90716b4b4>,void>::_Target_type": {"offset": "0xEBA0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x353C0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x23EC0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35960"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Copy": {"offset": "0x23B70"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Delete_this": {"offset": "0xF2F0"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Do_call": {"offset": "0x24210"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Move": {"offset": "0x23B70"}, "std::_Func_impl_no_alloc<<lambda_83215204cfd5d7f47e37515b1b1992f4>,bool,wchar_t const *,wchar_t const *>::_Target_type": {"offset": "0x24E10"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Copy": {"offset": "0x23B90"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Delete_this": {"offset": "0x23DC0"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Do_call": {"offset": "0x24230"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Target_type": {"offset": "0x24E20"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Copy": {"offset": "0xEF30"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Delete_this": {"offset": "0xF080"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Do_call": {"offset": "0xEFC0"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Move": {"offset": "0xEF80"}, "std::_Func_impl_no_alloc<<lambda_8d215c8ebd35918aa8dcf54639f44090>,bgfx::TextureHandle,FrontendNuiTexture *>::_Target_type": {"offset": "0xF070"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x23BF0"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x24240"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x23BF0"}, "std::_Func_impl_no_alloc<<lambda_9a29539d468c643684081aa4f04246d8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x24E30"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Copy": {"offset": "0x117B0"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Do_call": {"offset": "0x117D0"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Move": {"offset": "0x117B0"}, "std::_Func_impl_no_alloc<<lambda_a3b32c6402ea8a29c0fb91fccefb152a>,bool>::_Target_type": {"offset": "0x11860"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Copy": {"offset": "0x23C10"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Do_call": {"offset": "0x24250"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Move": {"offset": "0x23C10"}, "std::_Func_impl_no_alloc<<lambda_b20d3320c0dae91ba1b6614ebafbb56d>,void>::_Target_type": {"offset": "0x24E40"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0x35440"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0x23DC0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0x35830"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0x35970"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Copy": {"offset": "0x354A0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Do_call": {"offset": "0x35840"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Move": {"offset": "0x354A0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Target_type": {"offset": "0x35980"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Copy": {"offset": "0xF120"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Do_call": {"offset": "0xF140"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Move": {"offset": "0xF120"}, "std::_Func_impl_no_alloc<<lambda_e1107e445f9a57932d9bbad6e483ffcb>,bgfx::TextureHandle,FrontendNuiTexture *>::_Target_type": {"offset": "0xF180"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Copy": {"offset": "0xF1A0"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Delete_this": {"offset": "0xED00"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Do_call": {"offset": "0xF230"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Move": {"offset": "0xF1F0"}, "std::_Func_impl_no_alloc<<lambda_e53e06857a4b8a0a6fa4a7061e30490e>,void>::_Target_type": {"offset": "0xF240"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Copy": {"offset": "0x23C30"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Delete_this": {"offset": "0xF2F0"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Do_call": {"offset": "0x24260"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Move": {"offset": "0x23C30"}, "std::_Func_impl_no_alloc<<lambda_e62b8390c3777b7f55ef12d1274ada8f>,void>::_Target_type": {"offset": "0x24E50"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Copy": {"offset": "0x23C50"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Delete_this": {"offset": "0xF190"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Do_call": {"offset": "0x243B0"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Move": {"offset": "0x23C50"}, "std::_Func_impl_no_alloc<<lambda_ef9858e838e5c8a8a323f827e229c82b>,void>::_Target_type": {"offset": "0x24E60"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x354C0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x23D60"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x23EC0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEBB0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xEA60"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x35990"}, "std::_Generic_error_category::message": {"offset": "0x35BA0"}, "std::_Generic_error_category::name": {"offset": "0x35BF0"}, "std::_Guess_median_unchecked<int *,std::less<void> >": {"offset": "0xC3E40"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0xCADB0"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x14DE0"}, "std::_Make_ec": {"offset": "0x381B0"}, "std::_Maklocstr<char>": {"offset": "0xE460"}, "std::_Maklocstr<wchar_t>": {"offset": "0x3A5C0"}, "std::_Move_unchecked<int *,int *>": {"offset": "0xC41C0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::BitQuantizationMap<6> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::BitQuantizationMap<6> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count<astc_codec::`anonymous namespace'::BitQuantizationMap<8> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::BitQuantizationMap<8> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight> >::_Destroy": {"offset": "0xC9C70"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xEA60"}, "std::_Ref_count_obj2<FrontendNuiTexture *>::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count_obj2<FrontendNuiTexture *>::_Destroy": {"offset": "0xA440"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0x355F0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Destroy": {"offset": "0x35540"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Destroy": {"offset": "0x23E40"}, "std::_Ref_count_obj2<nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Delete_this": {"offset": "0xFBB0"}, "std::_Ref_count_obj2<nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Destroy": {"offset": "0x23E50"}, "std::_Sort_unchecked<int *,std::less<void> >": {"offset": "0xC42C0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x38630"}, "std::_System_error::_System_error": {"offset": "0x30630"}, "std::_System_error_category::default_error_condition": {"offset": "0x387B0"}, "std::_System_error_category::message": {"offset": "0x38830"}, "std::_System_error_category::name": {"offset": "0x388E0"}, "std::_System_error_message::~_System_error_message": {"offset": "0x36E70"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::BitQuantizationMap<6> >::~_Temporary_owner<astc_codec::`anonymous namespace'::BitQuantizationMap<6> >": {"offset": "0xC7B10"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::BitQuantizationMap<8> >::~_Temporary_owner<astc_codec::`anonymous namespace'::BitQuantizationMap<8> >": {"offset": "0xC7B10"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue> >::~_Temporary_owner<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintValue> >": {"offset": "0xC7B10"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight> >::~_Temporary_owner<astc_codec::`anonymous namespace'::QuintQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedQuintWeight> >": {"offset": "0xC7B10"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue> >::~_Temporary_owner<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritValue> >": {"offset": "0xC7B10"}, "std::_Temporary_owner<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight> >::~_Temporary_owner<astc_codec::`anonymous namespace'::TritQuantizationMap<&astc_codec::`anonymous namespace'::GetUnquantizedTritWeight> >": {"offset": "0xC7B10"}, "std::_Throw_bad_array_new_length": {"offset": "0xD1A0"}, "std::_Throw_bad_cast": {"offset": "0x3E2C0"}, "std::_Throw_system_error": {"offset": "0x359A0"}, "std::_Throw_system_error_from_std_win_error": {"offset": "0x38540"}, "std::_Throw_tree_length_error": {"offset": "0xD1C0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x3DE20"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x3DE20"}, "std::_Tree<std::_Tmap_traits<int,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>,std::less<int>,std::allocator<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > >,0> >::_Find_hint<int>": {"offset": "0xC6CA0"}, "std::_Tree<std::_Tmap_traits<int,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>,std::less<int>,std::allocator<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > >,0> >::~_Tree<std::_Tmap_traits<int,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>,std::less<int>,std::allocator<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > >,0> >": {"offset": "0xC7B40"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,citizen::ModData,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> >,0> >::_Find_hint<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14A70"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,citizen::ModData,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14D10"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x29B0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2C30"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA4A0"}, "std::_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >::~_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >": {"offset": "0xCB400"}, "std::_Tree<std::_Tset_traits<vfs::ManagerServer::MountPoint,vfs::ManagerServer::MountPointComparator,std::allocator<vfs::ManagerServer::MountPoint>,0> >::_Emplace<vfs::ManagerServer::MountPoint const &>": {"offset": "0x360F0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>,void *> > >": {"offset": "0x1E2F0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<vfs::ManagerServer::MountPoint,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<vfs::ManagerServer::MountPoint,void *> > >": {"offset": "0x36D30"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<HWND__ * const,citizen::Win32GameWindow *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<HWND__ * const,citizen::Win32GameWindow *>,void *> > >": {"offset": "0x2EC70"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<HWND__ * const,citizen::Win32GameWindow *> > >::_Insert_node": {"offset": "0xCF40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> >,void *> > >": {"offset": "0xC6BF0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > > >::_Insert_node": {"offset": "0xCF40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>,void *> > >": {"offset": "0x14970"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> > >::_Insert_node": {"offset": "0xCF40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2950"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCF40"}, "std::_Tree_val<std::_Tree_simple_types<tbb::detail::d1::global_control *> >::_Erase_tree<tbb::detail::d1::tbb_allocator<std::_Tree_node<tbb::detail::d1::global_control *,void *> > >": {"offset": "0xCB390"}, "std::_Tree_val<std::_Tree_simple_types<vfs::ManagerServer::MountPoint> >::_Erase_tree<std::allocator<std::_Tree_node<vfs::ManagerServer::MountPoint,void *> > >": {"offset": "0x364C0"}, "std::_Tree_val<std::_Tree_simple_types<vfs::ManagerServer::MountPoint> >::_Extract": {"offset": "0x24630"}, "std::_Tree_val<std::_Tree_simple_types<vfs::ManagerServer::MountPoint> >::_Insert_node": {"offset": "0xCF40"}, "std::_Tree_val<std::_Tree_simple_types<vfs::ManagerServer::MountPoint> >::_Lrotate": {"offset": "0x24C30"}, "std::_Tree_val<std::_Tree_simple_types<vfs::ManagerServer::MountPoint> >::_Rrotate": {"offset": "0x24D20"}, "std::_Uninitialized_backout_al<std::allocator<fwRefContainer<vfs::Device> > >::~_Uninitialized_backout_al<std::allocator<fwRefContainer<vfs::Device> > >": {"offset": "0x36D60"}, "std::_Uninitialized_copy<fwRefContainer<vfs::Device> *,fwRefContainer<vfs::Device> *,std::allocator<fwRefContainer<vfs::Device> > >": {"offset": "0x36840"}, "std::_Uninitialized_move<astc_codec::IntermediateEndpointData *,std::allocator<astc_codec::IntermediateEndpointData> >": {"offset": "0xC51E0"}, "std::_Uninitialized_move<fwRefContainer<vfs::Device> *,std::allocator<fwRefContainer<vfs::Device> > >": {"offset": "0x36840"}, "std::_Uninitialized_move<int *,std::allocator<int> >": {"offset": "0xC46B0"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2ECD0"}, "std::_Xlen_string": {"offset": "0xD1E0"}, "std::allocator<astc_codec::IntermediateEndpointData>::deallocate": {"offset": "0x35AC0"}, "std::allocator<char>::allocate": {"offset": "0xD200"}, "std::allocator<char>::deallocate": {"offset": "0x25220"}, "std::allocator<fwRefContainer<vfs::Device> >::allocate": {"offset": "0x25000"}, "std::allocator<fwRefContainer<vfs::Device> >::deallocate": {"offset": "0x252B0"}, "std::allocator<int>::allocate": {"offset": "0x24F90"}, "std::allocator<int>::deallocate": {"offset": "0x25260"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x35A50"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x35AC0"}, "std::allocator<std::pair<std::array<int,4>,std::array<int,4> > >::allocate": {"offset": "0x35A50"}, "std::allocator<std::pair<std::array<int,4>,std::array<int,4> > >::deallocate": {"offset": "0x35AC0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x25220"}, "std::allocator<unsigned int>::allocate": {"offset": "0x24F90"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x25260"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD260"}, "std::allocator<wchar_t>::deallocate": {"offset": "0x493F0"}, "std::array<int,4>::_Xran": {"offset": "0xC6BD0"}, "std::bad_alloc::bad_alloc": {"offset": "0x1DBA0"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA690"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA280"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA690"}, "std::bad_cast::bad_cast": {"offset": "0x3DDF0"}, "std::bad_cast::~bad_cast": {"offset": "0xA690"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x1EBB0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x1EC40"}, "std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xC9F90"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2890"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x150D0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x15240"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x153D0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>": {"offset": "0x15520"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x36690"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x250E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD450"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::back": {"offset": "0x25170"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1CDD0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0x11D00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::reserve": {"offset": "0x29D00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::resize": {"offset": "0x29E20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA560"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0xFC60"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>": {"offset": "0x36540"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA5C0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD2D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xFD30"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x49630"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x49790"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::resize": {"offset": "0x388F0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA5C0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x49430"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x495A0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x49920"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x49AA0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::str": {"offset": "0xC23B0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x49BB0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::~basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x47CC0"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x17310"}, "std::error_category::default_error_condition": {"offset": "0x35B10"}, "std::error_category::equivalent": {"offset": "0x35B40"}, "std::exception::exception": {"offset": "0xA2B0"}, "std::exception::what": {"offset": "0xE330"}, "std::filesystem::_Convert_wide_to_narrow_replace_chars<std::char_traits<char>,std::allocator<char> >": {"offset": "0x35FA0"}, "std::filesystem::_Throw_fs_error": {"offset": "0x384C0"}, "std::filesystem::absolute": {"offset": "0x38650"}, "std::filesystem::filesystem_error::_Pretty_message": {"offset": "0x381D0"}, "std::filesystem::filesystem_error::filesystem_error": {"offset": "0x36BF0"}, "std::filesystem::filesystem_error::what": {"offset": "0x38980"}, "std::filesystem::filesystem_error::~filesystem_error": {"offset": "0x36E80"}, "std::filesystem::path::generic_string<char,std::char_traits<char>,std::allocator<char>,0>": {"offset": "0x368C0"}, "std::filesystem::path::path<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>": {"offset": "0x35DB0"}, "std::filesystem::path::~path": {"offset": "0xA6B0"}, "std::forward<char const (&)[12]>": {"offset": "0x17690"}, "std::forward<char const (&)[15]>": {"offset": "0x17690"}, "std::function<bgfx::TextureHandle __cdecl(FrontendNuiTexture *)>::~function<bgfx::TextureHandle __cdecl(FrontendNuiTexture *)>": {"offset": "0x10230"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x10230"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x10230"}, "std::function<bool __cdecl(wchar_t const *,wchar_t const *)>::~function<bool __cdecl(wchar_t const *,wchar_t const *)>": {"offset": "0x10230"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x10230"}, "std::function<void __cdecl(int const &)>::~function<void __cdecl(int const &)>": {"offset": "0x10230"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1CE10"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x10230"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,int,int)>": {"offset": "0x10230"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)>": {"offset": "0x1CE10"}, "std::function<void __cdecl(void)>::swap": {"offset": "0x2C2D0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x10230"}, "std::get<0,<lambda_e0160eff7135e8eced6228db90b70fc3> >": {"offset": "0x17690"}, "std::invalid_argument::invalid_argument": {"offset": "0xCA620"}, "std::invalid_argument::~invalid_argument": {"offset": "0xA690"}, "std::invoke<<lambda_e0160eff7135e8eced6228db90b70fc3> >": {"offset": "0x19230"}, "std::length_error::length_error": {"offset": "0xCA6B0"}, "std::length_error::~length_error": {"offset": "0xA690"}, "std::locale::~locale": {"offset": "0x3DF20"}, "std::lock_guard<std::recursive_mutex>::~lock_guard<std::recursive_mutex>": {"offset": "0x36DC0"}, "std::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>": {"offset": "0xCB9C0"}, "std::logic_error::logic_error": {"offset": "0xCA700"}, "std::make_error_code": {"offset": "0x35B80"}, "std::map<int,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>,std::less<int>,std::allocator<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > > >::map<int,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>,std::less<int>,std::allocator<std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> > > >": {"offset": "0xC79A0"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,citizen::ModData,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,citizen::ModData,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData> > >": {"offset": "0x1E660"}, "std::move<<lambda_e0160eff7135e8eced6228db90b70fc3> &>": {"offset": "0x17690"}, "std::mutex::~mutex": {"offset": "0x30950"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x3E850"}, "std::numpunct<char>::do_falsename": {"offset": "0x3E870"}, "std::numpunct<char>::do_grouping": {"offset": "0x3E8F0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x3E930"}, "std::numpunct<char>::do_truename": {"offset": "0x3E950"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x3DC40"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x3E0D0"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x3E860"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x3E8B0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x3E8F0"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x3E940"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x3E990"}, "std::out_of_range::out_of_range": {"offset": "0xCA7E0"}, "std::out_of_range::~out_of_range": {"offset": "0xA690"}, "std::pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> >::~pair<int const ,std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap> >": {"offset": "0xC7B70"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,citizen::ModData>": {"offset": "0x1E690"}, "std::recursive_mutex::~recursive_mutex": {"offset": "0x30950"}, "std::runtime_error::runtime_error": {"offset": "0xCA830"}, "std::runtime_error::~runtime_error": {"offset": "0xA690"}, "std::set<vfs::ManagerServer::MountPoint,vfs::ManagerServer::MountPointComparator,std::allocator<vfs::ManagerServer::MountPoint> >::~set<vfs::ManagerServer::MountPoint,vfs::ManagerServer::MountPointComparator,std::allocator<vfs::ManagerServer::MountPoint> >": {"offset": "0x36DD0"}, "std::shared_ptr<FrontendNuiTexture *>::~shared_ptr<FrontendNuiTexture *>": {"offset": "0x10370"}, "std::shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>::~shared_ptr<astc_codec::`anonymous namespace'::QuantizationMap>": {"offset": "0xC7BC0"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x10370"}, "std::shared_ptr<internal::ConsoleVariableEntry<int> >::~shared_ptr<internal::ConsoleVariableEntry<int> >": {"offset": "0x10370"}, "std::shared_ptr<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~shared_ptr<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x10370"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x10370"}, "std::shared_ptr<nlohmann::json_abi_v3_11_2::detail::output_adapter_protocol<char> >::~shared_ptr<nlohmann::json_abi_v3_11_2::detail::output_adapter_protocol<char> >": {"offset": "0x10370"}, "std::system_error::system_error": {"offset": "0x306E0"}, "std::system_error::~system_error": {"offset": "0xA690"}, "std::thread::_Invoke<std::tuple<<lambda_503baef742c31b1926f3416bd59129bc> >,0>": {"offset": "0x15050"}, "std::thread::_Invoke<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,0>": {"offset": "0x15090"}, "std::thread::~thread": {"offset": "0x1EC90"}, "std::to_string": {"offset": "0x2C410"}, "std::unique_lock<std::mutex>::~unique_lock<std::mutex>": {"offset": "0x103C0"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0xCBAA0"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x1E930"}, "std::unique_ptr<citizen::GameWindow,std::default_delete<citizen::GameWindow> >::~unique_ptr<citizen::GameWindow,std::default_delete<citizen::GameWindow> >": {"offset": "0xE530"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0xE530"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >": {"offset": "0x1E870"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_928879d0336420accbd7d87a3ec03e52> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_928879d0336420accbd7d87a3ec03e52> >": {"offset": "0x1E870"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_a886e99c8d250c772cbca4cc3a028cee> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_a886e99c8d250c772cbca4cc3a028cee> >": {"offset": "0x1E870"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >": {"offset": "0x1E870"}, "std::unique_ptr<std::tuple<<lambda_503baef742c31b1926f3416bd59129bc> >,std::default_delete<std::tuple<<lambda_503baef742c31b1926f3416bd59129bc> > > >::~unique_ptr<std::tuple<<lambda_503baef742c31b1926f3416bd59129bc> >,std::default_delete<std::tuple<<lambda_503baef742c31b1926f3416bd59129bc> > > >": {"offset": "0x1E8D0"}, "std::unique_ptr<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > > >::get": {"offset": "0x27D20"}, "std::unique_ptr<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > > >::unique_ptr<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > > ><std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > >,0>": {"offset": "0x11DF0"}, "std::unique_ptr<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > > >::~unique_ptr<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> >,std::default_delete<std::tuple<<lambda_e0160eff7135e8eced6228db90b70fc3> > > >": {"offset": "0x1E8F0"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x3D820"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x3D990"}, "std::weak_ptr<FrontendNuiTexture *>::~weak_ptr<FrontendNuiTexture *>": {"offset": "0x10430"}, "tbb::detail::d0::raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >::~raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >": {"offset": "0x102A0"}, "tbb::detail::d1::unique_scoped_lock<tbb::detail::d1::spin_mutex>::~unique_scoped_lock<tbb::detail::d1::spin_mutex>": {"offset": "0xCB470"}, "tbb::detail::d2::concurrent_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::clear": {"offset": "0x11890"}, "tbb::detail::d2::micro_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::prepare_page": {"offset": "0x11B90"}, "tbb::detail::r1::AvailableHwConcurrency": {"offset": "0xCB6C0"}, "tbb::detail::r1::PrintExtraVersionInfo": {"offset": "0xCB4E0"}, "tbb::detail::r1::`dynamic initializer for '__TBB_InitOnceHiddenInstance''": {"offset": "0x1AD0"}, "tbb::detail::r1::`dynamic initializer for 'allowed_parallelism_ctl''": {"offset": "0x1960"}, "tbb::detail::r1::`dynamic initializer for 'lifetime_ctl''": {"offset": "0x19B0"}, "tbb::detail::r1::`dynamic initializer for 'stack_size_ctl''": {"offset": "0x1A00"}, "tbb::detail::r1::`dynamic initializer for 'terminate_on_exception_ctl''": {"offset": "0x1A50"}, "tbb::detail::r1::allocate_memory": {"offset": "0xCADE0"}, "tbb::detail::r1::allowed_parallelism_control::active_value": {"offset": "0xCB110"}, "tbb::detail::r1::allowed_parallelism_control::apply_active": {"offset": "0xCB100"}, "tbb::detail::r1::allowed_parallelism_control::default_value": {"offset": "0xCB070"}, "tbb::detail::r1::allowed_parallelism_control::is_first_arg_preferred": {"offset": "0xCB0F0"}, "tbb::detail::r1::arena::has_enqueued_tasks": {"offset": "0xCC4B0"}, "tbb::detail::r1::bad_last_alloc::bad_last_alloc": {"offset": "0xCA5B0"}, "tbb::detail::r1::bad_last_alloc::what": {"offset": "0xCAD20"}, "tbb::detail::r1::bad_last_alloc::~bad_last_alloc": {"offset": "0xA690"}, "tbb::detail::r1::cache_aligned_allocate": {"offset": "0xCAE10"}, "tbb::detail::r1::cache_aligned_deallocate": {"offset": "0xCAE70"}, "tbb::detail::r1::clear_address_waiter_table": {"offset": "0xCC240"}, "tbb::detail::r1::concurrent_monitor_mutex::get_semaphore": {"offset": "0xCBC30"}, "tbb::detail::r1::control_storage::active_value": {"offset": "0xCAFE0"}, "tbb::detail::r1::control_storage::apply_active": {"offset": "0xCAFC0"}, "tbb::detail::r1::control_storage::is_first_arg_preferred": {"offset": "0xCAFD0"}, "tbb::detail::r1::deallocate_memory": {"offset": "0xCAE80"}, "tbb::detail::r1::detect_cpu_features": {"offset": "0xCB5B0"}, "tbb::detail::r1::do_throw<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0xCA1B0"}, "tbb::detail::r1::do_throw<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0xCA1E0"}, "tbb::detail::r1::do_throw<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0xCA210"}, "tbb::detail::r1::do_throw<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0xCA240"}, "tbb::detail::r1::do_throw<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0xCA270"}, "tbb::detail::r1::do_throw<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0xCA2A0"}, "tbb::detail::r1::do_throw<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0xCA2D0"}, "tbb::detail::r1::do_throw<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0xCA300"}, "tbb::detail::r1::do_throw<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0xCA330"}, "tbb::detail::r1::do_throw<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0xCA360"}, "tbb::detail::r1::do_throw<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0xCA390"}, "tbb::detail::r1::do_throw<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0xCA3C0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0xCA3F0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0xCA410"}, "tbb::detail::r1::do_throw_noexcept<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0xCA430"}, "tbb::detail::r1::do_throw_noexcept<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0xCA450"}, "tbb::detail::r1::do_throw_noexcept<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0xCA470"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0xCA490"}, "tbb::detail::r1::do_throw_noexcept<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0xCA4B0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0xCA4D0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0xCA4F0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0xCA510"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0xCA530"}, "tbb::detail::r1::do_throw_noexcept<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0xCA550"}, "tbb::detail::r1::dummy_allocate_binding_handler": {"offset": "0xEA60"}, "tbb::detail::r1::dummy_apply_affinity": {"offset": "0xA440"}, "tbb::detail::r1::dummy_deallocate_binding_handler": {"offset": "0xA440"}, "tbb::detail::r1::dummy_destroy_system_topology": {"offset": "0xA440"}, "tbb::detail::r1::dummy_get_default_concurrency": {"offset": "0xCC1A0"}, "tbb::detail::r1::dummy_restore_affinity": {"offset": "0xA440"}, "tbb::detail::r1::dynamic_link": {"offset": "0xCB4D0"}, "tbb::detail::r1::dynamic_unlink": {"offset": "0xA440"}, "tbb::detail::r1::dynamic_unlink_all": {"offset": "0xA440"}, "tbb::detail::r1::gcc_rethrow_exception_broken": {"offset": "0xF110"}, "tbb::detail::r1::get_address_waiter_table": {"offset": "0xCC3E0"}, "tbb::detail::r1::governor::acquire_resources": {"offset": "0xCC1B0"}, "tbb::detail::r1::governor::default_num_threads": {"offset": "0xCBBB0"}, "tbb::detail::r1::governor::release_resources": {"offset": "0xCC200"}, "tbb::detail::r1::handle_perror": {"offset": "0xCABA0"}, "tbb::detail::r1::initialize_allocate_handler": {"offset": "0xCAD50"}, "tbb::detail::r1::initialize_cache_aligned_allocate_handler": {"offset": "0xCAD80"}, "tbb::detail::r1::initialize_cache_aligned_allocator": {"offset": "0xCAE90"}, "tbb::detail::r1::initialize_hardware_concurrency_info": {"offset": "0xCB780"}, "tbb::detail::r1::lifetime_control::apply_active": {"offset": "0xCB220"}, "tbb::detail::r1::lifetime_control::default_value": {"offset": "0xEA60"}, "tbb::detail::r1::lifetime_control::is_first_arg_preferred": {"offset": "0xF110"}, "tbb::detail::r1::market::add_ref_unsafe": {"offset": "0xCBAB0"}, "tbb::detail::r1::market::app_parallelism_limit": {"offset": "0xCB490"}, "tbb::detail::r1::market::release": {"offset": "0xCBCD0"}, "tbb::detail::r1::market::set_active_num_workers": {"offset": "0xCBE50"}, "tbb::detail::r1::market::update_allotment": {"offset": "0xCC090"}, "tbb::detail::r1::missing_wait::missing_wait": {"offset": "0xCA780"}, "tbb::detail::r1::missing_wait::what": {"offset": "0xCAD30"}, "tbb::detail::r1::missing_wait::~missing_wait": {"offset": "0xA690"}, "tbb::detail::r1::runtime_warning": {"offset": "0xCB600"}, "tbb::detail::r1::stack_size_control::apply_active": {"offset": "0xCAFC0"}, "tbb::detail::r1::stack_size_control::default_value": {"offset": "0xCB210"}, "tbb::detail::r1::std_cache_aligned_allocate": {"offset": "0xCAFA0"}, "tbb::detail::r1::std_cache_aligned_deallocate": {"offset": "0xCAFB0"}, "tbb::detail::r1::terminate_on_exception": {"offset": "0xCB4B0"}, "tbb::detail::r1::terminate_on_exception_control::default_value": {"offset": "0xEA60"}, "tbb::detail::r1::throw_exception": {"offset": "0xCAC70"}, "tbb::detail::r1::unsafe_wait::unsafe_wait": {"offset": "0xCA8C0"}, "tbb::detail::r1::unsafe_wait::~unsafe_wait": {"offset": "0xA690"}, "tbb::detail::r1::user_abort::user_abort": {"offset": "0xCA950"}, "tbb::detail::r1::user_abort::what": {"offset": "0xCAD40"}, "tbb::detail::r1::user_abort::~user_abort": {"offset": "0xA690"}, "tinystl::buffer_insert_common<bgfx::DxbcSignature::Element,bgfx::TinyStlAllocator>": {"offset": "0xB3D30"}, "tinystl::buffer_insert_common<bgfx::NonLocalAllocator::Free,bgfx::TinyStlAllocator>": {"offset": "0x4A9D0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D11BlendState *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D11DepthStencilState *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D11InputLayout *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D11RasterizerState *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D11SamplerState *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,ID3D12PipelineState *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,IDirect3DVertexDeclaration9 *> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::gl::UniformStateCache::f3x3> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::gl::UniformStateCache::f4> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::gl::UniformStateCache::f4x4> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::vk::VkDescriptorSetLayout> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::vk::VkPipeline> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::vk::VkRenderPass> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,bgfx::vk::VkSampler> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,int> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,unsigned int> *,bgfx::TinyStlAllocator>": {"offset": "0x4AB30"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned __int64,unsigned short> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned int,unsigned int> *,bgfx::TinyStlAllocator>": {"offset": "0x578B0"}, "tinystl::buffer_resize<tinystl::unordered_hash_node<unsigned short,void> *,bgfx::TinyStlAllocator>": {"offset": "0x4AB30"}, "tinystl::list<bgfx::NonLocalAllocator::Free,bgfx::TinyStlAllocator>::~list<bgfx::NonLocalAllocator::Free,bgfx::TinyStlAllocator>": {"offset": "0x4BCE0"}, "tinystl::move_construct_impl<bgfx::DxbcSignature::Element>": {"offset": "0xB3EF0"}, "tinystl::move_construct_impl<bgfx::NonLocalAllocator::Free>": {"offset": "0x4ADE0"}, "tinystl::move_impl<bgfx::NonLocalAllocator::Free>": {"offset": "0x4AE00"}, "tinystl::pair<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int>::~pair<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int>": {"offset": "0xB3FC0"}, "tinystl::stringT<bgfx::TinyStlAllocator>::append": {"offset": "0xB41B0"}, "tinystl::stringT<bgfx::TinyStlAllocator>::reserve": {"offset": "0xB6DB0"}, "tinystl::stringT<bgfx::TinyStlAllocator>::~stringT<bgfx::TinyStlAllocator>": {"offset": "0xB3FF0"}, "tinystl::unordered_map<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int,bgfx::TinyStlAllocator>::find": {"offset": "0xB4E90"}, "tinystl::unordered_map<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int,bgfx::TinyStlAllocator>::insert": {"offset": "0xB4F80"}, "tinystl::unordered_map<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int,bgfx::TinyStlAllocator>::~unordered_map<tinystl::stringT<bgfx::TinyStlAllocator>,unsigned int,bgfx::TinyStlAllocator>": {"offset": "0xB4020"}, "tinystl::unordered_map<unsigned __int64,ID3D11BlendState *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D11BlendState *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,ID3D11DepthStencilState *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D11DepthStencilState *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,ID3D11InputLayout *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D11InputLayout *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,ID3D11RasterizerState *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D11RasterizerState *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,ID3D11SamplerState *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D11SamplerState *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,ID3D12PipelineState *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,ID3D12PipelineState *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,IDirect3DVertexDeclaration9 *,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,IDirect3DVertexDeclaration9 *,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f3x3,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f3x3,bgfx::TinyStlAllocator>": {"offset": "0x84690"}, "tinystl::unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f4,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f4,bgfx::TinyStlAllocator>": {"offset": "0x84720"}, "tinystl::unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f4x4,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,bgfx::gl::UniformStateCache::f4x4,bgfx::TinyStlAllocator>": {"offset": "0x847B0"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkDescriptorSetLayout,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkDescriptorSetLayout,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkPipeline,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkPipeline,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkRenderPass,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkRenderPass,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkSampler,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,bgfx::vk::VkSampler,bgfx::TinyStlAllocator>::insert": {"offset": "0x5CE20"}, "tinystl::unordered_map<unsigned __int64,int,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,int,bgfx::TinyStlAllocator>": {"offset": "0x84600"}, "tinystl::unordered_map<unsigned __int64,unsigned int,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,unsigned int,bgfx::TinyStlAllocator>": {"offset": "0x4BD20"}, "tinystl::unordered_map<unsigned __int64,unsigned short,bgfx::TinyStlAllocator>::clear": {"offset": "0x59120"}, "tinystl::unordered_map<unsigned __int64,unsigned short,bgfx::TinyStlAllocator>::erase": {"offset": "0x68750"}, "tinystl::unordered_map<unsigned __int64,unsigned short,bgfx::TinyStlAllocator>::insert": {"offset": "0x6B270"}, "tinystl::unordered_map<unsigned __int64,unsigned short,bgfx::TinyStlAllocator>::~unordered_map<unsigned __int64,unsigned short,bgfx::TinyStlAllocator>": {"offset": "0x647F0"}, "tinystl::unordered_map<unsigned int,unsigned int,bgfx::TinyStlAllocator>::clear": {"offset": "0x85580"}, "tinystl::unordered_map<unsigned int,unsigned int,bgfx::TinyStlAllocator>::insert": {"offset": "0x8AD00"}, "tinystl::unordered_set<unsigned short,bgfx::TinyStlAllocator>::clear": {"offset": "0x4CFA0"}, "tinystl::unordered_set<unsigned short,bgfx::TinyStlAllocator>::~unordered_set<unsigned short,bgfx::TinyStlAllocator>": {"offset": "0x4BDE0"}, "utf8::exception::exception": {"offset": "0x47B40"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x40560"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x41B10"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x47BD0"}, "utf8::invalid_code_point::what": {"offset": "0x49CC0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA690"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x47C40"}, "utf8::invalid_utf8::what": {"offset": "0x49CD0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA690"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x47CA0"}, "utf8::not_enough_room::what": {"offset": "0x49CE0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA690"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x415F0"}, "va_impl<wchar_t,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> > >": {"offset": "0x41810"}, "vfs::LocalDevice::Close": {"offset": "0x38DF0"}, "vfs::LocalDevice::CloseBulk": {"offset": "0x389F0"}, "vfs::LocalDevice::Create": {"offset": "0x38EA0"}, "vfs::LocalDevice::ExtensionCtl": {"offset": "0x39080"}, "vfs::LocalDevice::FindClose": {"offset": "0x39350"}, "vfs::LocalDevice::FindFirst": {"offset": "0x39360"}, "vfs::LocalDevice::FindNext": {"offset": "0x39670"}, "vfs::LocalDevice::Flush": {"offset": "0x39770"}, "vfs::LocalDevice::GetAbsolutePath": {"offset": "0x37A60"}, "vfs::LocalDevice::GetAttributes": {"offset": "0x397B0"}, "vfs::LocalDevice::GetLength": {"offset": "0x39870"}, "vfs::LocalDevice::GetModifiedTime": {"offset": "0x398A0"}, "vfs::LocalDevice::Open": {"offset": "0x39930"}, "vfs::LocalDevice::OpenBulk": {"offset": "0x39A50"}, "vfs::LocalDevice::Read": {"offset": "0x39B40"}, "vfs::LocalDevice::ReadBulk": {"offset": "0x39BC0"}, "vfs::LocalDevice::RemoveFile": {"offset": "0x39D80"}, "vfs::LocalDevice::RenameFile": {"offset": "0x39E50"}, "vfs::LocalDevice::Seek": {"offset": "0x39FD0"}, "vfs::LocalDevice::Truncate": {"offset": "0x3A050"}, "vfs::LocalDevice::Write": {"offset": "0x3A090"}, "vfs::LocalDevice::WriteBulk": {"offset": "0x3A110"}, "vfs::MakeMemoryDevice": {"offset": "0x38AF0"}, "vfs::ManagerServer::FindDevice": {"offset": "0x37130"}, "vfs::ManagerServer::GetDevice": {"offset": "0x37A90"}, "vfs::ManagerServer::GetNativeDevice": {"offset": "0x37CD0"}, "vfs::ManagerServer::ManagerServer": {"offset": "0x36A80"}, "vfs::ManagerServer::Mount": {"offset": "0x37D10"}, "vfs::ManagerServer::MountPoint::~MountPoint": {"offset": "0x36E00"}, "vfs::ManagerServer::Unmount": {"offset": "0x38020"}, "vfs::MemoryDevice::Close": {"offset": "0x389D0"}, "vfs::MemoryDevice::CloseBulk": {"offset": "0x389F0"}, "vfs::MemoryDevice::FindClose": {"offset": "0xA440"}, "vfs::MemoryDevice::FindFirst": {"offset": "0x38A90"}, "vfs::MemoryDevice::FindNext": {"offset": "0xF110"}, "vfs::MemoryDevice::Flush": {"offset": "0xE780"}, "vfs::MemoryDevice::GetAbsolutePath": {"offset": "0x37A60"}, "vfs::MemoryDevice::GetLength": {"offset": "0x38AE0"}, "vfs::MemoryDevice::Open": {"offset": "0x38B60"}, "vfs::MemoryDevice::OpenBulk": {"offset": "0x38BD0"}, "vfs::MemoryDevice::Read": {"offset": "0x38BF0"}, "vfs::MemoryDevice::ReadBulk": {"offset": "0x38C40"}, "vfs::MemoryDevice::Seek": {"offset": "0x38C80"}, "vfs::MemoryDevice::SetPathPrefix": {"offset": "0xA440"}, "vva": {"offset": "0x49CA0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1510"}}}