@echo off
title FiveM Cheat - Teste com Offsets Reais
color 0A

echo ========================================
echo   FiveM Cheat - Teste com Offsets Reais
echo ========================================
echo.
echo Usando 155 offsets extraidos automaticamente!
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

REM Verificar se FiveM está rodando
echo [INFO] Verificando se FiveM esta rodando...
tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GameProcess.exe" 2>NUL | find /I /N "FiveM_GameProcess.exe">NUL
set "fivem_game_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 if %fivem_game_running% neq 0 (
    echo [WARNING] FiveM nao detectado!
    echo.
    echo INSTRUCOES PARA TESTAR:
    echo 1. Abra o FiveM
    echo 2. Entre em um servidor (preferencialmente privado)
    echo 3. Aguarde carregar completamente no jogo
    echo 4. Execute este script novamente
    echo.
    echo Pressione qualquer tecla para continuar mesmo assim...
    pause >nul
) else (
    echo [OK] FiveM detectado rodando
)

echo.

REM Verificar se offsets_auto.h existe
if not exist "examples\offsets_auto.h" (
    echo [ERROR] Arquivo offsets_auto.h nao encontrado!
    echo Execute primeiro: python extract_offsets.py
    pause
    exit /b 1
)

echo [OK] Offsets reais encontrados (155 offsets)
echo.

REM Configurar ImGui se necessário
if not exist "external\imgui" (
    echo [INFO] Configurando Dear ImGui...
    call setup_imgui.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao configurar ImGui!
        pause
        exit /b 1
    )
    echo [OK] ImGui configurado
)

REM Compilar cheat com offsets reais
echo ========================================
echo COMPILANDO CHEAT COM OFFSETS REAIS...
echo ========================================

cd examples

echo [INFO] Compilando FiveMSimpleCheat.dll...
echo [INFO] Usando offsets_auto.h com 155 offsets reais
echo.

call build_cheat.bat
if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao!
    echo.
    echo POSSIVEIS CAUSAS:
    echo - Visual Studio nao instalado
    echo - Ferramentas de build ausentes
    echo - Erro nos offsets
    echo.
    cd ..
    pause
    exit /b 1
)

if exist "FiveMSimpleCheat.dll" (
    echo [OK] FiveMSimpleCheat.dll compilado com sucesso!
    echo [INFO] Tamanho: 
    dir FiveMSimpleCheat.dll | find "FiveMSimpleCheat.dll"
) else (
    echo [ERROR] FiveMSimpleCheat.dll nao foi criado!
    cd ..
    pause
    exit /b 1
)

cd ..

REM Compilar injetor se necessário
if not exist "SimpleInjector.exe" (
    echo [INFO] Compilando injetor...
    cd tools
    call build_injector.bat
    cd ..
)

REM Mostrar informações dos offsets
echo.
echo ========================================
echo INFORMACOES DOS OFFSETS REAIS
echo ========================================
echo.
echo Offsets principais extraidos:
echo - GetPlayerPed: 0x1639CE8
echo - GetAllPhysicalPlayers: 0x118B6EB  
echo - CWorld: 0x737F0C
echo - g_vehiclePool: 0xD7383C
echo - WeaponDamageModifier: 0x415BD3
echo - InfiniteAmmo: 0x10695C1
echo - VehicleDamageModifier: 0xEF6849
echo - IsNetworkGame: 0x108E8D
echo.
echo Total: 155 offsets reais extraidos dos seus arquivos JSON
echo.

REM Injetar cheat
echo ========================================
echo INJETANDO CHEAT NO FIVEM...
echo ========================================

if exist "SimpleInjector.exe" (
    echo [INFO] Usando SimpleInjector.exe
    echo.
    echo INSTRUCOES:
    echo 1. O SimpleInjector vai abrir
    echo 2. Selecione opcao 2 (Injetar FiveMSimpleCheat.dll)
    echo 3. Aguarde confirmacao de injecao bem-sucedida
    echo.
    echo Pressione qualquer tecla para abrir o injetor...
    pause >nul
    start SimpleInjector.exe
) else (
    echo [INFO] Usando metodo PowerShell alternativo...
    
    powershell -Command "& { Write-Host '[INFO] Procurando processo FiveM...'; $procs = Get-Process | Where-Object { $_.ProcessName -like '*FiveM*' }; if ($procs) { foreach ($proc in $procs) { Write-Host '[FOUND]' $proc.ProcessName '(PID:' $proc.Id ')'; } } else { Write-Host '[WARNING] Nenhum processo FiveM encontrado'; } }"
    
    echo.
    echo [INFO] Use Process Hacker ou outro injetor para injetar:
    echo Arquivo: examples\FiveMSimpleCheat.dll
    echo Processo: FiveM_GameProcess.exe ou FiveM_GTAProcess.exe
)

echo.
echo ========================================
echo INSTRUCOES DE TESTE
echo ========================================
echo.
echo CHEAT COMPILADO COM OFFSETS REAIS!
echo.
echo CONTROLES NO FIVEM:
echo.
echo [INSERT] - Abrir/fechar menu principal
echo [F1]     - Toggle ESP (ver jogadores)
echo [F2]     - Toggle God Mode (vida infinita)
echo [F3]     - Teleport para waypoint
echo [F4]     - Reparar veiculo atual
echo.
echo FUNCIONALIDADES DISPONIVEIS:
echo.
echo 1. ESP AVANCADO:
echo    ✓ Caixas ao redor dos jogadores
echo    ✓ Informacoes de vida/armadura
echo    ✓ Distancia em tempo real
echo    ✓ Nomes dos jogadores
echo.
echo 2. PLAYER HACKS:
echo    ✓ God Mode (vida/armadura infinita)
echo    ✓ Teleport para coordenadas
echo    ✓ Heal instantaneo
echo    ✓ Stamina infinita
echo.
echo 3. WEAPON HACKS:
echo    ✓ Munição infinita
echo    ✓ Sem recarga
echo    ✓ Dano aumentado
echo    ✓ Sem recuo
echo.
echo 4. VEHICLE HACKS:
echo    ✓ Reparar veiculo
echo    ✓ Velocidade aumentada
echo    ✓ Veiculo indestrutivel
echo    ✓ Freio de mao desabilitado
echo.
echo TESTE SUGERIDO:
echo.
echo 1. Entre em um servidor FiveM
echo 2. Pressione INSERT para abrir o menu
echo 3. Teste ESP (F1) para ver outros jogadores
echo 4. Teste God Mode (F2) e deixe NPCs te atacarem
echo 5. Teste teleport e outras funcionalidades
echo.
echo IMPORTANTE:
echo ✓ Offsets baseados nos seus arquivos JSON reais
echo ✓ 155 offsets extraidos automaticamente
echo ✓ Compativel com a versao atual do FiveM
echo ✓ Use apenas em servidores privados/teste
echo.

REM Criar log detalhado
echo FiveM Cheat Test - %date% %time% > test_real_offsets_log.txt
echo. >> test_real_offsets_log.txt
echo Status: Compilado com offsets reais >> test_real_offsets_log.txt
echo Offsets: 155 extraidos automaticamente >> test_real_offsets_log.txt
echo Arquivo: examples\FiveMSimpleCheat.dll >> test_real_offsets_log.txt
echo Header: examples\offsets_auto.h >> test_real_offsets_log.txt
echo. >> test_real_offsets_log.txt
echo Principais offsets: >> test_real_offsets_log.txt
echo - GetPlayerPed: 0x1639CE8 >> test_real_offsets_log.txt
echo - GetAllPhysicalPlayers: 0x118B6EB >> test_real_offsets_log.txt
echo - CWorld: 0x737F0C >> test_real_offsets_log.txt
echo - g_vehiclePool: 0xD7383C >> test_real_offsets_log.txt
echo - WeaponDamageModifier: 0x415BD3 >> test_real_offsets_log.txt
echo. >> test_real_offsets_log.txt

echo ========================================
echo TESTE INICIADO COM OFFSETS REAIS!
echo Log salvo em: test_real_offsets_log.txt
echo ========================================

echo.
echo Pressione qualquer tecla para sair...
pause >nul
