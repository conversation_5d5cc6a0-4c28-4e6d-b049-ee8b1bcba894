cmake_minimum_required(VERSION 3.16)
project(FiveMCheat_FINAL)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Windows
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Criar a DLL do cheat simples
add_library(FiveMCheat_FINAL SHARED simple_cheat_final.cpp)

# Incluir diretórios
target_include_directories(FiveMCheat_FINAL PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Bibliotecas do Windows
target_link_libraries(FiveMCheat_FINAL
    user32
    kernel32
)

# Configurações de compilação
if(MSVC)
    target_compile_options(FiveMCheat_FINAL PRIVATE /W3)
    target_compile_definitions(FiveMCheat_FINAL PRIVATE _CRT_SECURE_NO_WARNINGS)
endif()

# Configurações de saída
set_target_properties(FiveMCheat_FINAL PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
    LIBRARY_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    LIBRARY_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# Copiar DLL para diretório raiz após build
add_custom_command(TARGET FiveMCheat_FINAL POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:FiveMCheat_FINAL> ${CMAKE_CURRENT_SOURCE_DIR}/../FiveMCheat_FINAL.dll
    COMMENT "Copying cheat DLL to root directory"
)
