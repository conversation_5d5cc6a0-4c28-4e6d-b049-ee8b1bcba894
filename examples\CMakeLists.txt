cmake_minimum_required(VERSION 3.16)
project(FiveMSimpleCheat)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Windows
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Incluir Dear ImGui
set(IMGUI_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../external/imgui")

# Verificar se ImGui existe
if(NOT EXISTS "${IMGUI_DIR}")
    message(FATAL_ERROR "Dear ImGui not found! Please run setup_imgui.bat first")
endif()

# Arquivos do ImGui
set(IMGUI_SOURCES
    ${IMGUI_DIR}/imgui.cpp
    ${IMGUI_DIR}/imgui_demo.cpp
    ${IMGUI_DIR}/imgui_draw.cpp
    ${IMGUI_DIR}/imgui_tables.cpp
    ${IMGUI_DIR}/imgui_widgets.cpp
    ${IMGUI_DIR}/backends/imgui_impl_win32.cpp
    ${IMGUI_DIR}/backends/imgui_impl_dx11.cpp
)

# Arquivos do projeto
set(SOURCES
    simple_cheat.cpp
    ${IMGUI_SOURCES}
)

# Criar a DLL
add_library(FiveMSimpleCheat SHARED ${SOURCES})

# Incluir diretórios
target_include_directories(FiveMSimpleCheat PRIVATE
    ${IMGUI_DIR}
    ${IMGUI_DIR}/backends
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Bibliotecas do Windows
target_link_libraries(FiveMSimpleCheat
    d3d11
    dxgi
    user32
    kernel32
    gdi32
    winspool
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
)

# Configurações de compilação
if(MSVC)
    target_compile_options(FiveMSimpleCheat PRIVATE /W3)
    target_compile_definitions(FiveMSimpleCheat PRIVATE _CRT_SECURE_NO_WARNINGS)
endif()

# Configurações de saída
set_target_properties(FiveMSimpleCheat PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# Configurações específicas para Release/Debug
set_target_properties(FiveMSimpleCheat PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
    LIBRARY_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    LIBRARY_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# Copiar DLL para diretório raiz após build
add_custom_command(TARGET FiveMSimpleCheat POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:FiveMSimpleCheat> ${CMAKE_CURRENT_SOURCE_DIR}/FiveMSimpleCheat.dll
    COMMENT "Copying DLL to root directory"
)
