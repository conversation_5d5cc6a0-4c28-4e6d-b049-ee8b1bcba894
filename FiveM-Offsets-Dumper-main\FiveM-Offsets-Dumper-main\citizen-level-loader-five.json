{"citizen-level-loader-five.dll": {"<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>::~<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>": {"offset": "0x162A0"}, "<lambda_2808195e57b922385f6a3659947f3d55>::~<lambda_2808195e57b922385f6a3659947f3d55>": {"offset": "0x162A0"}, "<lambda_2e44b0312f796ca6921f39821df9e8e6>::~<lambda_2e44b0312f796ca6921f39821df9e8e6>": {"offset": "0x162A0"}, "<lambda_2e6bd70d060755d2cb2428aaf05f8600>::~<lambda_2e6bd70d060755d2cb2428aaf05f8600>": {"offset": "0x162A0"}, "<lambda_33efee440faf72f6b444c2d5c0a041af>::~<lambda_33efee440faf72f6b444c2d5c0a041af>": {"offset": "0x162A0"}, "<lambda_4d8e460f665ab8e258229b895cd0ba00>::~<lambda_4d8e460f665ab8e258229b895cd0ba00>": {"offset": "0x162A0"}, "<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>::~<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>": {"offset": "0x162D0"}, "<lambda_73a7ee34c2ef86ba909f7ef5855c6173>::~<lambda_73a7ee34c2ef86ba909f7ef5855c6173>": {"offset": "0x16380"}, "<lambda_8032a27510d7fde61c3215c64b2377c7>::<lambda_8032a27510d7fde61c3215c64b2377c7>": {"offset": "0x157E0"}, "<lambda_8032a27510d7fde61c3215c64b2377c7>::~<lambda_8032a27510d7fde61c3215c64b2377c7>": {"offset": "0x162D0"}, "<lambda_be3e5d9dce35d2c8dbfa8485373731d5>::~<lambda_be3e5d9dce35d2c8dbfa8485373731d5>": {"offset": "0x163B0"}, "<lambda_f25c37099038263181b5186a3fa41b37>::~<lambda_f25c37099038263181b5186a3fa41b37>": {"offset": "0x163D0"}, "AllocateBuffer": {"offset": "0x318F0"}, "CFileLoader__BuildContentChangeSetActionList_Hook": {"offset": "0x19DC0"}, "CfxState::CfxState": {"offset": "0x250E0"}, "Component::As": {"offset": "0xE430"}, "Component::IsA": {"offset": "0xE4F0"}, "Component::SetCommandLine": {"offset": "0xA350"}, "Component::SetUserData": {"offset": "0xE500"}, "ComponentInstance::DoGameLoad": {"offset": "0xE4D0"}, "ComponentInstance::Initialize": {"offset": "0xE4E0"}, "ComponentInstance::Shutdown": {"offset": "0xE500"}, "Concurrency::cancellation_token::~cancellation_token": {"offset": "0x16E90"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Allocate_page": {"offset": "0x106F0"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Assign_and_destroy_item": {"offset": "0x10640"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Copy_item": {"offset": "0x105D0"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Deallocate_page": {"offset": "0x10780"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::_Move_item": {"offset": "0x10620"}, "Concurrency::concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >::~concurrent_queue<std::function<void __cdecl(void)>,std::allocator<std::function<void __cdecl(void)> > >": {"offset": "0x16560"}, "Concurrency::create_task<Concurrency::task_completion_event<fwRefContainer<fx::Resource> > >": {"offset": "0x14AA0"}, "Concurrency::details::_CancellationTokenCallback<<lambda_be3e5d9dce35d2c8dbfa8485373731d5> >::_Exec": {"offset": "0xECA0"}, "Concurrency::details::_CancellationTokenRegistration::_Invoke": {"offset": "0x1D5A0"}, "Concurrency::details::_CancellationTokenState::_DeregisterCallback": {"offset": "0x1CC70"}, "Concurrency::details::_CancellationTokenState::_RegisterCallback": {"offset": "0x1D9A0"}, "Concurrency::details::_ContextCallback::~_ContextCallback": {"offset": "0x16A30"}, "Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore::_Callback": {"offset": "0x1BBC0"}, "Concurrency::details::_DefaultPPLTaskScheduler::schedule": {"offset": "0x118C0"}, "Concurrency::details::_DefaultTaskHelper::_NoCallOnDefaultTask_ErrorImpl": {"offset": "0x1D970"}, "Concurrency::details::_ExceptionHolder::~_ExceptionHolder": {"offset": "0x16A50"}, "Concurrency::details::_Internal_task_options::~_Internal_task_options": {"offset": "0x16B70"}, "Concurrency::details::_RefCounter::_Destroy": {"offset": "0x11B80"}, "Concurrency::details::_ScheduleFuncWithAutoInline": {"offset": "0x1E1C0"}, "Concurrency::details::_TaskCollectionBaseImpl::_Complete": {"offset": "0x1BD60"}, "Concurrency::details::_TaskCollectionBaseImpl::~_TaskCollectionBaseImpl": {"offset": "0x16C10"}, "Concurrency::details::_TaskCreationCallstack::_TaskCreationCallstack": {"offset": "0x15E80"}, "Concurrency::details::_TaskCreationCallstack::~_TaskCreationCallstack": {"offset": "0x16C80"}, "Concurrency::details::_TaskProcHandle::_RunChoreBridge": {"offset": "0x1DDC0"}, "Concurrency::details::_TaskProcHandle::~_TaskProcHandle": {"offset": "0x16CE0"}, "Concurrency::details::_TaskProcThunk::_Bridge": {"offset": "0x1BB60"}, "Concurrency::details::_TaskProcThunk::_Holder::~_Holder": {"offset": "0x16B20"}, "Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> >::_CancelAndRunContinuations": {"offset": "0xE6B0"}, "Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> >::_FinalizeAndRunContinuations": {"offset": "0x1D490"}, "Concurrency::details::_Task_impl<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::_CancelAndRunContinuations": {"offset": "0xE990"}, "Concurrency::details::_Task_impl<unsigned char>::_CancelAndRunContinuations": {"offset": "0x10C40"}, "Concurrency::details::_Task_impl<unsigned char>::_FinalizeAndRunContinuations": {"offset": "0x1D3D0"}, "Concurrency::details::_Task_impl_base::_CancelWithException": {"offset": "0x1BC00"}, "Concurrency::details::_Task_impl_base::_RegisterCancellation": {"offset": "0x1DA70"}, "Concurrency::details::_Task_impl_base::_RunTaskContinuations": {"offset": "0x1DE00"}, "Concurrency::details::_Task_impl_base::_ScheduleContinuation": {"offset": "0x1DF10"}, "Concurrency::details::_Task_impl_base::_ScheduleContinuationTask": {"offset": "0x1E110"}, "Concurrency::details::_Task_impl_base::_ScheduleTask": {"offset": "0x1E310"}, "Concurrency::details::_Task_impl_base::_Task_impl_base": {"offset": "0x15F40"}, "Concurrency::details::_Task_impl_base::~_Task_impl_base": {"offset": "0x16CF0"}, "Concurrency::details::_Task_ptr<fwRefContainer<fx::Resource> >::_Make": {"offset": "0x1D800"}, "Concurrency::details::_Task_ptr<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::_Make": {"offset": "0x1D690"}, "Concurrency::details::_ThenImplOptions::_CreateOptions": {"offset": "0x1C6F0"}, "Concurrency::details::_ThenImplOptions::~_ThenImplOptions": {"offset": "0x16DF0"}, "Concurrency::get_ambient_scheduler": {"offset": "0x1ED70"}, "Concurrency::invalid_operation::invalid_operation": {"offset": "0x160D0"}, "Concurrency::invalid_operation::~invalid_operation": {"offset": "0xA5A0"}, "Concurrency::scheduler_ptr::~scheduler_ptr": {"offset": "0x16800"}, "Concurrency::task<fwRefContainer<fx::Resource> >::_CreateImpl": {"offset": "0x1C590"}, "Concurrency::task<fwRefContainer<fx::Resource> >::_ThenImpl<fwRefContainer<fx::Resource>,std::function<void __cdecl(fwRefContainer<fx::Resource>)> >": {"offset": "0x146D0"}, "Concurrency::task<fwRefContainer<fx::Resource> >::task<fwRefContainer<fx::Resource> ><Concurrency::task_completion_event<fwRefContainer<fx::Resource> > >": {"offset": "0x12DE0"}, "Concurrency::task<fwRefContainer<fx::Resource> >::then<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41> >": {"offset": "0x14F90"}, "Concurrency::task<fwRefContainer<fx::Resource> >::then<<lambda_d967bf8448627ea93d482cbe6d99ec74> >": {"offset": "0x15300"}, "Concurrency::task<fwRefContainer<fx::Resource> >::~task<fwRefContainer<fx::Resource> >": {"offset": "0x16800"}, "Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::_CreateImpl": {"offset": "0x1C430"}, "Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::~task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >": {"offset": "0x16800"}, "Concurrency::task<unsigned char>::_CreateImpl": {"offset": "0x1C200"}, "Concurrency::task<unsigned char>::~task<unsigned char>": {"offset": "0x16800"}, "Concurrency::task<void>::~task<void>": {"offset": "0x16800"}, "Concurrency::task_completion_event<fwRefContainer<fx::Resource> >::_RegisterTask": {"offset": "0x1DBD0"}, "Concurrency::task_completion_event<fwRefContainer<fx::Resource> >::set": {"offset": "0x1F0D0"}, "Concurrency::task_completion_event<fwRefContainer<fx::Resource> >::~task_completion_event<fwRefContainer<fx::Resource> >": {"offset": "0x16800"}, "Concurrency::task_continuation_context::~task_continuation_context": {"offset": "0x17060"}, "Concurrency::task_from_result<fwRefContainer<fx::Resource> >": {"offset": "0x14DC0"}, "Concurrency::task_options::~task_options": {"offset": "0x17080"}, "ConsoleCommand::ConsoleCommand<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a> >": {"offset": "0x122E0"}, "ConsoleCommand::ConsoleCommand<<lambda_5b01ea14a0d8675608e7eaf63353aa31> >": {"offset": "0x124B0"}, "ConsoleCommand::ConsoleCommand<<lambda_8032a27510d7fde61c3215c64b2377c7> >": {"offset": "0x12680"}, "ConsoleCommand::ConsoleCommand<<lambda_b28df9c4adb24802a81f0c00866e474b> >": {"offset": "0x12870"}, "ConsoleCommand::ConsoleCommand<<lambda_be93f5d8ec84ac26296852c624e37bf8> >": {"offset": "0x12A40"}, "ConsoleCommand::ConsoleCommand<<lambda_ef907bc4d68e732434c0f4e349592b6f> >": {"offset": "0x12C10"}, "ConsoleCommandManager::GetDefaultInstance": {"offset": "0x1B030"}, "CoreGetComponentRegistry": {"offset": "0x1A9E0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x1AA70"}, "CreateComponent": {"offset": "0xE510"}, "CreateTrampolineFunction": {"offset": "0x31C40"}, "DllMain": {"offset": "0x43110"}, "DoLoadLevel": {"offset": "0x1AB00"}, "DoNtRaiseException": {"offset": "0x2EBE0"}, "DoesLevelHashMatch": {"offset": "0x1AFC0"}, "EnableAllHooksLL": {"offset": "0x30E50"}, "EnableHook": {"offset": "0x30FA0"}, "EnableHookLL": {"offset": "0x310C0"}, "FatalErrorNoExceptRealV": {"offset": "0xBA20"}, "FatalErrorRealV": {"offset": "0xBA50"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x2180"}, "FreeBuffer": {"offset": "0x31920"}, "Freeze": {"offset": "0x311D0"}, "GetAbsoluteCitPath": {"offset": "0x25AA0"}, "GetMemoryBlock": {"offset": "0x319A0"}, "GlobalErrorHandler": {"offset": "0xBC90"}, "HookFunction::Run": {"offset": "0xEF60"}, "HookFunctionBase::Register": {"offset": "0x2FB10"}, "HookFunctionBase::RunAll": {"offset": "0x2FB30"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x24D10"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x251A0"}, "ICoreGameInit::ClearVariable": {"offset": "0x1A770"}, "ICoreGameInit::SetVariable": {"offset": "0x1BA20"}, "InitFunction::Run": {"offset": "0xE540"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x2DEC0"}, "InitFunctionBase::Register": {"offset": "0x2ED50"}, "InitFunctionBase::RunAll": {"offset": "0x2EDA0"}, "InitializeBuffer": {"offset": "0xA350"}, "Instance<ICoreGameInit>::Get": {"offset": "0x1AFD0"}, "IsCodePadding": {"offset": "0x31FF0"}, "IsExecutableAddress": {"offset": "0x31C00"}, "IsLevelApplicable": {"offset": "0x1AFC0"}, "LoadLevel": {"offset": "0x1B170"}, "MH_CreateHook": {"offset": "0x31380"}, "MH_EnableHook": {"offset": "0x31610"}, "MH_Initialize": {"offset": "0x31620"}, "MakeRelativeCitPath": {"offset": "0xC330"}, "ProcessThreadIPs": {"offset": "0x316C0"}, "RaiseDebugException": {"offset": "0x2ECC0"}, "SPResourceMounter::HandlesScheme": {"offset": "0x10F20"}, "SPResourceMounter::LoadResource": {"offset": "0x10F60"}, "ScopedError::~ScopedError": {"offset": "0xA540"}, "SetCoreGameMode": {"offset": "0x1B3B0"}, "SpawnThread::DoRun": {"offset": "0xF640"}, "SysError": {"offset": "0xC8B0"}, "ToNarrow": {"offset": "0x2EDD0"}, "ToWide": {"offset": "0x2EEC0"}, "TraceRealV": {"offset": "0x2F1D0"}, "Unfreeze": {"offset": "0x31870"}, "Win32TrapAndJump64": {"offset": "0x30E30"}, "_DllMainCRTStartup": {"offset": "0x42908"}, "_Init_thread_abort": {"offset": "0x41D4C"}, "_Init_thread_footer": {"offset": "0x41D7C"}, "_Init_thread_header": {"offset": "0x41DDC"}, "_Init_thread_notify": {"offset": "0x41E44"}, "_Init_thread_wait": {"offset": "0x41E88"}, "_RTC_Initialize": {"offset": "0x43160"}, "_RTC_Terminate": {"offset": "0x4319C"}, "_Smtx_lock_exclusive": {"offset": "0x41AD4"}, "_Smtx_unlock_exclusive": {"offset": "0x41ADC"}, "__ArrayUnwind": {"offset": "0x429B4"}, "__GSHandlerCheck": {"offset": "0x42468"}, "__GSHandlerCheckCommon": {"offset": "0x42488"}, "__GSHandlerCheck_EH": {"offset": "0x424E4"}, "__GSHandlerCheck_SEH": {"offset": "0x42B94"}, "__chkstk": {"offset": "0x42C50"}, "__crt_debugger_hook": {"offset": "0x42EBC"}, "__dyn_tls_init": {"offset": "0x422B0"}, "__dyn_tls_on_demand_init": {"offset": "0x42318"}, "__isa_available_init": {"offset": "0x42D10"}, "__local_stdio_printf_options": {"offset": "0xE310"}, "__local_stdio_scanf_options": {"offset": "0x43134"}, "__raise_securityfailure": {"offset": "0x42A18"}, "__report_gsfailure": {"offset": "0x42A4C"}, "__scrt_acquire_startup_lock": {"offset": "0x41F30"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x41F6C"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x41FA0"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x41FB8"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x41FE0"}, "__scrt_dllmain_exception_filter": {"offset": "0x41FF8"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x42058"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x42088"}, "__scrt_fastfail": {"offset": "0x42EC4"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x43158"}, "__scrt_initialize_crt": {"offset": "0x4209C"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x4313C"}, "__scrt_initialize_onexit_tables": {"offset": "0x420E8"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x41C54"}, "__scrt_initialize_type_info": {"offset": "0x425CC"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x42174"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x4518C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x43058"}, "__scrt_release_startup_lock": {"offset": "0x4220C"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE500"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE500"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE500"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE500"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE500"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE430"}, "__scrt_throw_std_bad_alloc": {"offset": "0x43030"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD0B0"}, "__scrt_uninitialize_crt": {"offset": "0x42230"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x41D24"}, "__scrt_uninitialize_type_info": {"offset": "0x425DC"}, "__security_check_cookie": {"offset": "0x42580"}, "__security_init_cookie": {"offset": "0x43064"}, "__std_find_trivial_1": {"offset": "0x418E0"}, "__std_find_trivial_2": {"offset": "0x419B0"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x41B10"}, "__std_system_error_allocate_message": {"offset": "0x41BAC"}, "__std_system_error_deallocate_message": {"offset": "0x41C40"}, "_get_startup_argv_mode": {"offset": "0x43050"}, "_guard_check_icall_nop": {"offset": "0xA350"}, "_guard_dispatch_icall_nop": {"offset": "0x43300"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x43320"}, "_onexit": {"offset": "0x4225C"}, "_wwassert": {"offset": "0x25E20"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x4524A"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x451A4"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x451BB"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x451D4"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x451E8"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1250"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x12B0"}, "`dynamic initializer for '_init_instance_18''": {"offset": "0x1280"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x12E0"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x1310"}, "`dynamic initializer for '_init_instance_42''": {"offset": "0x1340"}, "`dynamic initializer for '_init_instance_43''": {"offset": "0x1370"}, "`dynamic initializer for '_init_instance_44''": {"offset": "0x13A0"}, "`dynamic initializer for 'g_nextLevelPath''": {"offset": "0x13D0"}, "`dynamic initializer for 'g_onShutdownQueue''": {"offset": "0x13E0"}, "`dynamic initializer for 'g_overrideNextLoadedLevel''": {"offset": "0x1420"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1430"}, "`dynamic initializer for 'initFunction''": {"offset": "0x1470"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x16C0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1670"}, "`dynamic initializer for 'spawnThread''": {"offset": "0x1630"}, "`dynamic initializer for 'xbr::virt::Base<5,2802,2,6>::ms_initFunction''": {"offset": "0x1210"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>,<lambda_26ec4e4340cdafc7ef3f6a4c13e55374> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>,<lambda_2808195e57b922385f6a3659947f3d55> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>,<lambda_2e44b0312f796ca6921f39821df9e8e6> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>,<lambda_2e6bd70d060755d2cb2428aaf05f8600> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>,<lambda_33efee440faf72f6b444c2d5c0a041af> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>,<lambda_4d8e460f665ab8e258229b895cd0ba00> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >,<lambda_615bbf6626b6c0c29a9b7fffd3b38f41> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >,<lambda_73a7ee34c2ef86ba909f7ef5855c6173> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >,<lambda_73a7ee34c2ef86ba909f7ef5855c6173> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16AE0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>,<lambda_8032a27510d7fde61c3215c64b2377c7> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16B00"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>,<lambda_8032a27510d7fde61c3215c64b2377c7> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x16B00"}, "atexit": {"offset": "0x42298"}, "capture_previous_context": {"offset": "0x42B20"}, "dllmain_crt_dispatch": {"offset": "0x425E8"}, "dllmain_crt_process_attach": {"offset": "0x42638"}, "dllmain_crt_process_detach": {"offset": "0x42750"}, "dllmain_dispatch": {"offset": "0x427D4"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xD6F0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA410"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x243B0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x23640"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x244E0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x236A0"}, "fmt::v8::detail::add_compare": {"offset": "0x23A90"}, "fmt::v8::detail::assert_fail": {"offset": "0x23C60"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x23CB0"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x23E80"}, "fmt::v8::detail::bigint::square": {"offset": "0x24900"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x23640"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2C20"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x24BC0"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x1FD40"}, "fmt::v8::detail::compare": {"offset": "0x23DE0"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x26C60"}, "fmt::v8::detail::count_digits": {"offset": "0xD4D0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x1FE70"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x1FF80"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2CD0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x24280"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x21DF0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x246E0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x21E20"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x22BF0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x227C0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x24660"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x20030"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x20030"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x26CF0"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x26DB0"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x24210"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2D00"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x214E0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x2FD0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x213C0"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned __int64,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,0>": {"offset": "0x27040"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned __int64>": {"offset": "0x26F40"}, "fmt::v8::detail::format_decimal<wchar_t,unsigned int>": {"offset": "0x26E30"}, "fmt::v8::detail::format_float<double>": {"offset": "0x1F780"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x21620"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x30B0"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x27100"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x21980"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x31D0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x31D0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x3330"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x27270"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x3590"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x274F0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE260"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x2FA10"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x22030"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x222B0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x22540"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x226B0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA470"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xA470"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x3660"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE0A0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4C60"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x29560"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5C60"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5810"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x60A0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x23340"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x23220"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5740"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2A1B0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,bool,0>": {"offset": "0x2AF50"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x2A6D0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x2A280"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x2AB10"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B010"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B150"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x64E0"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B290"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6520"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x2B380"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x66B0"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x2B530"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x7070"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6A80"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2C100"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x2BA40"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA000"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0xA000"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x77A0"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x2C740"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7BC0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7D90"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x7F30"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x7F30"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x2CBD0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x80C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x82E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x8460"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9BF0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x85F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8810"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8AB0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8CD0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8E50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x9070"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x9290"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9410"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9630"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x97B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x99D0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x2CD50"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x2CEF0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x2D090"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x2D220"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x2D3C0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x2D560"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x2D700"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x2D8B0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x2DA50"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9D80"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x2DBF0"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x2DDE0"}, "fmt::v8::format_error::format_error": {"offset": "0xA200"}, "fmt::v8::format_error::~format_error": {"offset": "0xA5A0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x27810"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3AD0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x28820"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x38B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x285F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3780"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x284E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3690"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x283B0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3AD0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x28820"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x39A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x286F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C00"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x28960"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4760"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x292E0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4190"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x28D80"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x29F40"}, "fprintf": {"offset": "0xE320"}, "fwEvent<>::ConnectInternal": {"offset": "0x1A810"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA5C0"}, "fwRefContainer<fx::Resource>::~fwRefContainer<fx::Resource>": {"offset": "0x16700"}, "fwRefContainer<fx::ResourceMounter>::~fwRefContainer<fx::ResourceMounter>": {"offset": "0x16700"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0x16700"}, "fwRefCountable::AddRef": {"offset": "0x2FAD0"}, "fwRefCountable::Release": {"offset": "0x2FAE0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x2FAC0"}, "fx::ResourceMounter::LoadResourceWithError": {"offset": "0xE5E0"}, "hde64_disasm": {"offset": "0x32030"}, "hook::AllocateFunctionStub": {"offset": "0x2FB90"}, "hook::TransformPattern": {"offset": "0x30AD0"}, "hook::get_pattern<void,50>": {"offset": "0x14BD0"}, "hook::pattern::EnsureMatches": {"offset": "0x30470"}, "hook::pattern::Initialize": {"offset": "0x307D0"}, "hook::pattern::~pattern": {"offset": "0x16F10"}, "hook::trampoline_raw": {"offset": "0x30D60"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::Call": {"offset": "0x19F20"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x13300"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x1A330"}, "launch::IsSDKGuest": {"offset": "0x25DA0"}, "ranges::_get_::get<0,ranges::variant<char32_t *,std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > > &>": {"offset": "0x395F0"}, "ranges::_get_::get<1,ranges::variant<char32_t *,std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > > &>": {"offset": "0x39490"}, "ranges::bad_variant_access::bad_variant_access": {"offset": "0x3A2D0"}, "ranges::bad_variant_access::~bad_variant_access": {"offset": "0xA5A0"}, "ranges::detail::move<ranges::detail::get_fn<char32_t * const,0> &>": {"offset": "0x14DB0"}, "ranges::detail::move<ranges::detail::get_fn<char32_t *,0> &>": {"offset": "0x14DB0"}, "ranges::detail::move<ranges::detail::get_fn<ranges::default_sentinel_t const ,1> &>": {"offset": "0x14DB0"}, "ranges::detail::move<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > const ,1> &>": {"offset": "0x14DB0"}, "ranges::detail::move<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,1> &>": {"offset": "0x14DB0"}, "ranges::detail::move<ranges::detail::indexed_element_fn &>": {"offset": "0x14DB0"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<char32_t * const,0>,ranges::detail::indexed_element_fn>": {"offset": "0x3A260"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<char32_t *,0>,ranges::detail::indexed_element_fn>": {"offset": "0x3A260"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<ranges::default_sentinel_t const ,1>,ranges::detail::indexed_element_fn>": {"offset": "0x3A260"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > const ,1>,ranges::detail::indexed_element_fn>": {"offset": "0x3A260"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,1>,ranges::detail::indexed_element_fn>": {"offset": "0x3A260"}, "ranges::detail::variant_visit_<ranges::detail::variant_data_<meta::list<ranges::detail::indexed_datum<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,std::integral_constant<unsigned __int64,1> > >,1>::type const ,ranges::detail::get_fn<char32_t * const,0>,ranges::detail::indexed_element_fn>": {"offset": "0x3A1E0"}, "ranges::detail::variant_visit_<ranges::detail::variant_data_<meta::list<ranges::detail::indexed_datum<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,std::integral_constant<unsigned __int64,1> > >,1>::type,ranges::detail::get_fn<char32_t *,0>,ranges::detail::indexed_element_fn>": {"offset": "0x3A1E0"}, "ranges::transform_view<ranges::split_view<ranges::ref_view<std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >,ranges::single_view<char32_t> >,`skyr::v1::`anonymous namespace'::domain_to_ascii'::`2'::<lambda_1> >::~transform_view<ranges::split_view<ranges::ref_view<std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >,ranges::single_view<char32_t> >,`skyr::v1::`anonymous namespace'::domain_to_ascii'::`2'::<lambda_1> >": {"offset": "0x3A3B0"}, "ranges::transform_view<ranges::split_when_view<std::basic_string_view<char,std::char_traits<char> >,ranges::views::split_when_base_fn::predicate_pred_<`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_1> > >,`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_2> >::~transform_view<ranges::split_when_view<std::basic_string_view<char,std::char_traits<char> >,ranges::views::split_when_base_fn::predicate_pred_<`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_1> > >,`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_2> >": {"offset": "0x32CD0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA280"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA320"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1B90"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB3F0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA350"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC540"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC600"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xC870"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xCD10"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA360"}, "rapidjson::internal::DigitGen": {"offset": "0xB6A0"}, "rapidjson::internal::Grisu2": {"offset": "0xC150"}, "rapidjson::internal::Prettify": {"offset": "0xC6B0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2620"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x26F0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA320"}, "rapidjson::internal::WriteExponent": {"offset": "0xCC80"}, "rapidjson::internal::u32toa": {"offset": "0xD7E0"}, "rapidjson::internal::u64toa": {"offset": "0xDA50"}, "skyr::v1::`anonymous namespace'::domain_to_ascii": {"offset": "0x3A790"}, "skyr::v1::`anonymous namespace'::is_double_dot_path_segment": {"offset": "0x3CCB0"}, "skyr::v1::`anonymous namespace'::is_url_code_point": {"offset": "0x3CFB0"}, "skyr::v1::`anonymous namespace'::is_windows_drive_letter": {"offset": "0x3D020"}, "skyr::v1::`anonymous namespace'::map_code_points<skyr::v1::unicode::transform_u32_range<skyr::v1::unicode::view_u8_range<std::basic_string_view<char,std::char_traits<char> > > > &>": {"offset": "0x396D0"}, "skyr::v1::`anonymous namespace'::parse_next": {"offset": "0x3C150"}, "skyr::v1::`anonymous namespace'::parse_opaque_host": {"offset": "0x414D0"}, "skyr::v1::`anonymous namespace'::shorten_path": {"offset": "0x40CB0"}, "skyr::v1::`anonymous namespace'::validate_label": {"offset": "0x3BA10"}, "skyr::v1::default_port": {"offset": "0x3CBA0"}, "skyr::v1::details::basic_parse": {"offset": "0x3BBD0"}, "skyr::v1::details::make_url": {"offset": "0x35370"}, "skyr::v1::details::to_u8<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15720"}, "skyr::v1::details::url_parse_error_category::message": {"offset": "0xFFF0"}, "skyr::v1::details::url_parse_error_category::name": {"offset": "0xFFE0"}, "skyr::v1::domain::~domain": {"offset": "0xA470"}, "skyr::v1::domain_to_ascii": {"offset": "0x3B3B0"}, "skyr::v1::host::host": {"offset": "0x34D80"}, "skyr::v1::host::serialize": {"offset": "0x35500"}, "skyr::v1::host::~host": {"offset": "0x35010"}, "skyr::v1::idna::code_point_status": {"offset": "0x3C380"}, "skyr::v1::idna::idna_code_point_map_iterator<skyr::v1::unicode::u8_range_iterator<std::_String_view_iterator<std::char_traits<char> > >,skyr::v1::unicode::sentinel>::increment": {"offset": "0x3B4C0"}, "skyr::v1::idna::map_code_point": {"offset": "0x3C3F0"}, "skyr::v1::ipv4_address::serialize": {"offset": "0x36750"}, "skyr::v1::ipv6_address::serialize": {"offset": "0x37F60"}, "skyr::v1::is_special": {"offset": "0x3CEA0"}, "skyr::v1::make_error_code": {"offset": "0x1EE30"}, "skyr::v1::opaque_host::~opaque_host": {"offset": "0xA470"}, "skyr::v1::parse_host": {"offset": "0x40D90"}, "skyr::v1::parse_ipv4_address": {"offset": "0x36150"}, "skyr::v1::parse_ipv6_address": {"offset": "0x37A00"}, "skyr::v1::percent_decode": {"offset": "0x339B0"}, "skyr::v1::percent_encoding::is_percent_encoded": {"offset": "0x3CDE0"}, "skyr::v1::percent_encoding::percent_encoded_char::~percent_encoded_char": {"offset": "0xA470"}, "skyr::v1::punycode_decode<char32_t,std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >": {"offset": "0x39B70"}, "skyr::v1::punycode_encode<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x39E40"}, "skyr::v1::serialize_excluding_fragment": {"offset": "0x355B0"}, "skyr::v1::unicode::details::from_four_byte_sequence<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x39270"}, "skyr::v1::unicode::find_code_point<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x39010"}, "skyr::v1::url::pathname": {"offset": "0x1EEB0"}, "skyr::v1::url::set_hash": {"offset": "0x35AD0"}, "skyr::v1::url::set_pathname": {"offset": "0x35C40"}, "skyr::v1::url::update_record": {"offset": "0x35E30"}, "skyr::v1::url::url": {"offset": "0x161C0"}, "skyr::v1::url::~url": {"offset": "0x17170"}, "skyr::v1::url_parser_context::parse_authority": {"offset": "0x3D0C0"}, "skyr::v1::url_parser_context::parse_cannot_be_a_base_url": {"offset": "0x3D6C0"}, "skyr::v1::url_parser_context::parse_file": {"offset": "0x3DAA0"}, "skyr::v1::url_parser_context::parse_file_host": {"offset": "0x3DE70"}, "skyr::v1::url_parser_context::parse_file_slash": {"offset": "0x3E2D0"}, "skyr::v1::url_parser_context::parse_fragment": {"offset": "0x3E450"}, "skyr::v1::url_parser_context::parse_hostname": {"offset": "0x3E770"}, "skyr::v1::url_parser_context::parse_no_scheme": {"offset": "0x3EB50"}, "skyr::v1::url_parser_context::parse_path": {"offset": "0x3ED10"}, "skyr::v1::url_parser_context::parse_path_or_authority": {"offset": "0x3F5C0"}, "skyr::v1::url_parser_context::parse_path_start": {"offset": "0x3F600"}, "skyr::v1::url_parser_context::parse_port": {"offset": "0x3F820"}, "skyr::v1::url_parser_context::parse_query": {"offset": "0x3FAD0"}, "skyr::v1::url_parser_context::parse_relative": {"offset": "0x3FDE0"}, "skyr::v1::url_parser_context::parse_relative_slash": {"offset": "0x40250"}, "skyr::v1::url_parser_context::parse_scheme": {"offset": "0x40370"}, "skyr::v1::url_parser_context::parse_scheme_start": {"offset": "0x40870"}, "skyr::v1::url_parser_context::parse_special_authority_ignore_slashes": {"offset": "0x409A0"}, "skyr::v1::url_parser_context::parse_special_authority_slashes": {"offset": "0x409E0"}, "skyr::v1::url_parser_context::parse_special_relative_or_authority": {"offset": "0x40AC0"}, "skyr::v1::url_parser_context::url_parser_context": {"offset": "0x3C8D0"}, "skyr::v1::url_parser_context::~url_parser_context": {"offset": "0x3BB50"}, "skyr::v1::url_record::is_special": {"offset": "0x3CE60"}, "skyr::v1::url_record::url_record": {"offset": "0x34E40"}, "skyr::v1::url_record::~url_record": {"offset": "0x17200"}, "skyr::v1::url_search_parameters::initialize": {"offset": "0x330F0"}, "skyr::v1::url_search_parameters::url_search_parameters": {"offset": "0x32C50"}, "snprintf": {"offset": "0x38560"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA390"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x16420"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA390"}, "std::_Buffered_inplace_merge_divide_and_conquer2<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x36C20"}, "std::_Buffered_inplace_merge_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x36D10"}, "std::_Buffered_inplace_merge_unchecked_impl<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x36E50"}, "std::_Buffered_merge_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x37210"}, "std::_Buffered_rotate_unchecked<std::pair<unsigned __int64,unsigned __int64> *>": {"offset": "0x37460"}, "std::_Chunked_merge_unchecked<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x37690"}, "std::_Codecvt_do_length<std::codecvt_utf8_utf16<wchar_t,1114111,0>,char,_Mbstatet>": {"offset": "0x13610"}, "std::_Destroy_in_place<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xA470"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x138C0"}, "std::_Destroy_range<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x32900"}, "std::_Destroy_range<std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > > > >": {"offset": "0x13950"}, "std::_Facet_Register": {"offset": "0x41B6C"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x16380"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x16380"}, "std::_Func_class<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::~_Func_class<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >": {"offset": "0x16380"}, "std::_Func_class<unsigned char,fwRefContainer<fx::Resource> >::~_Func_class<unsigned char,fwRefContainer<fx::Resource> >": {"offset": "0x16380"}, "std::_Func_class<void,fwRefContainer<fx::Resource> >::~_Func_class<void,fwRefContainer<fx::Resource> >": {"offset": "0x16380"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x16380"}, "std::_Func_class<void>::_Reset_move": {"offset": "0x1DCF0"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x16380"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Copy": {"offset": "0xEDF0"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Do_call": {"offset": "0xEE10"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Move": {"offset": "0xEDF0"}, "std::_Func_impl_no_alloc<<lambda_1a76ca8b9a658fb1ece320ff7f9e8901>,bool>::_Target_type": {"offset": "0xEE20"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11AA0"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x11B20"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_26ec4e4340cdafc7ef3f6a4c13e55374>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11B70"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11CC0"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x11B20"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_2808195e57b922385f6a3659947f3d55>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11D40"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11960"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x119E0"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_2e44b0312f796ca6921f39821df9e8e6>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11A30"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11BD0"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x11B20"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_2e6bd70d060755d2cb2428aaf05f8600>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11C50"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Copy": {"offset": "0x10590"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Do_call": {"offset": "0x105B0"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Move": {"offset": "0x10590"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Target_type": {"offset": "0x105C0"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Copy": {"offset": "0x11ED0"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Do_call": {"offset": "0xEFC0"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Move": {"offset": "0x11ED0"}, "std::_Func_impl_no_alloc<<lambda_32fe3cab21096b4529839bba82b83240>,void>::_Target_type": {"offset": "0x11EF0"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11DE0"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x119E0"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_33efee440faf72f6b444c2d5c0a041af>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11E60"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11D50"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11A40"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x119E0"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_4d8e460f665ab8e258229b895cd0ba00>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11DD0"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0xF630"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0xFF20"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0xF630"}, "std::_Func_impl_no_alloc<<lambda_5357d8c9084e6bc049fe5ad5da4f5f4a>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0xFF30"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0xFFC0"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0xEF00"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0xFFC0"}, "std::_Func_impl_no_alloc<<lambda_5b01ea14a0d8675608e7eaf63353aa31>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0xFFD0"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Copy": {"offset": "0x10BD0"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Delete_this": {"offset": "0x10DE0"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Do_call": {"offset": "0x10BE0"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >::_Target_type": {"offset": "0x10C30"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Copy": {"offset": "0x10980"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Delete_this": {"offset": "0x10AC0"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Do_call": {"offset": "0x10A00"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_73a7ee34c2ef86ba909f7ef5855c6173>,unsigned char,fwRefContainer<fx::Resource> >::_Target_type": {"offset": "0x10AB0"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Copy": {"offset": "0x11E70"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Do_call": {"offset": "0xEFC0"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Move": {"offset": "0x11E70"}, "std::_Func_impl_no_alloc<<lambda_75980998ae717a99edcd456dc7316440>,void>::_Target_type": {"offset": "0x11EC0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Copy": {"offset": "0xEFA0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Do_call": {"offset": "0xEFC0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Move": {"offset": "0xEFA0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Target_type": {"offset": "0xEFD0"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Copy": {"offset": "0x11F40"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Do_call": {"offset": "0x11F60"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Move": {"offset": "0x11F40"}, "std::_Func_impl_no_alloc<<lambda_7db710c0a2d4acaf3dd54d26a089b11c>,bool>::_Target_type": {"offset": "0x11F80"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Copy": {"offset": "0xEFE0"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Delete_this": {"offset": "0xF140"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Do_call": {"offset": "0xF060"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Move": {"offset": "0xE430"}, "std::_Func_impl_no_alloc<<lambda_8032a27510d7fde61c3215c64b2377c7>,void>::_Target_type": {"offset": "0xF130"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Copy": {"offset": "0xEC30"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Do_call": {"offset": "0xEC50"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Move": {"offset": "0xEC30"}, "std::_Func_impl_no_alloc<<lambda_a52b03b9b0f17ab1e04ecb2656dd98b9>,bool>::_Target_type": {"offset": "0xEC70"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Copy": {"offset": "0xEF70"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Do_call": {"offset": "0xEF80"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Move": {"offset": "0xEF70"}, "std::_Func_impl_no_alloc<<lambda_b28df9c4adb24802a81f0c00866e474b>,void>::_Target_type": {"offset": "0xEF90"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0xEEF0"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0xEF00"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0xEEF0"}, "std::_Func_impl_no_alloc<<lambda_be93f5d8ec84ac26296852c624e37bf8>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0xEF20"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Copy": {"offset": "0x11F00"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Do_call": {"offset": "0x11F20"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Move": {"offset": "0x11F00"}, "std::_Func_impl_no_alloc<<lambda_c0d7779ee2eda459a0d1e895b158fd5f>,void>::_Target_type": {"offset": "0x11F30"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Copy": {"offset": "0xEE80"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Do_call": {"offset": "0xEE90"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Move": {"offset": "0xEE80"}, "std::_Func_impl_no_alloc<<lambda_d967bf8448627ea93d482cbe6d99ec74>,tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,fwRefContainer<fx::Resource> >::_Target_type": {"offset": "0xEEE0"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Copy": {"offset": "0xEE30"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Do_call": {"offset": "0xEE50"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Move": {"offset": "0xEE30"}, "std::_Func_impl_no_alloc<<lambda_ef8e271afadfecc80391886720dac2a1>,bool>::_Target_type": {"offset": "0xEE70"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Copy": {"offset": "0xEF30"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Delete_this": {"offset": "0xEC90"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Do_call": {"offset": "0xEF40"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Move": {"offset": "0xEF30"}, "std::_Func_impl_no_alloc<<lambda_ef907bc4d68e732434c0f4e349592b6f>,void>::_Target_type": {"offset": "0xEF50"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Copy": {"offset": "0x10460"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Delete_this": {"offset": "0x10510"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Do_call": {"offset": "0x104E0"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Get": {"offset": "0xEC80"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Move": {"offset": "0x104A0"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Target_type": {"offset": "0x10500"}, "std::_Generic_error_category::message": {"offset": "0x24600"}, "std::_Generic_error_category::name": {"offset": "0x24650"}, "std::_Global_new<std::_Func_impl_no_alloc<<lambda_615bbf6626b6c0c29a9b7fffd3b38f41>,void,fwRefContainer<fx::Resource> >,<lambda_615bbf6626b6c0c29a9b7fffd3b38f41> const &>": {"offset": "0x13F60"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x13FD0"}, "std::_Insertion_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x377E0"}, "std::_Maklocstr<char>": {"offset": "0xE370"}, "std::_Maklocstr<wchar_t>": {"offset": "0x1FC30"}, "std::_Move_unchecked<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64> *>": {"offset": "0x378A0"}, "std::_Optimistic_temporary_buffer<std::pair<unsigned __int64,unsigned __int64> >::~_Optimistic_temporary_buffer<std::pair<unsigned __int64,unsigned __int64> >": {"offset": "0x379E0"}, "std::_Optional_construct_base<skyr::v1::host>::_Construct<skyr::v1::host const &>": {"offset": "0x341F0"}, "std::_Optional_construct_base<skyr::v1::host>::~_Optional_construct_base<skyr::v1::host>": {"offset": "0x34FE0"}, "std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x34FD0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE430"}, "std::_Ref_count_obj2<Concurrency::details::_ExceptionHolder>::_Delete_this": {"offset": "0xE560"}, "std::_Ref_count_obj2<Concurrency::details::_ExceptionHolder>::_Destroy": {"offset": "0xE670"}, "std::_Ref_count_obj2<Concurrency::details::_Task_completion_event_impl<fwRefContainer<fx::Resource> > >::_Delete_this": {"offset": "0xE560"}, "std::_Ref_count_obj2<Concurrency::details::_Task_completion_event_impl<fwRefContainer<fx::Resource> > >::_Destroy": {"offset": "0x11730"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > >::_Delete_this": {"offset": "0xE560"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > >::_Destroy": {"offset": "0xE550"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Delete_this": {"offset": "0xE560"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Destroy": {"offset": "0xE550"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<unsigned char> >::_Delete_this": {"offset": "0xE560"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<unsigned char> >::_Destroy": {"offset": "0xE550"}, "std::_Stable_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x378D0"}, "std::_String_val<std::_Simple_types<char32_t> >::_Xran": {"offset": "0x1E4E0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x1E4E0"}, "std::_System_error_category::default_error_condition": {"offset": "0x352F0"}, "std::_System_error_category::message": {"offset": "0x35440"}, "std::_System_error_category::name": {"offset": "0x354F0"}, "std::_System_error_message::~_System_error_message": {"offset": "0x35030"}, "std::_Throw_bad_array_new_length": {"offset": "0xD0B0"}, "std::_Throw_bad_cast": {"offset": "0x23A70"}, "std::_Throw_bad_optional_access": {"offset": "0x35290"}, "std::_Throw_bad_variant_access": {"offset": "0x352C0"}, "std::_Throw_range_error": {"offset": "0x1E390"}, "std::_Throw_tree_length_error": {"offset": "0xD0D0"}, "std::_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3A360"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x23600"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x23600"}, "std::_Traits_compare<std::char_traits<char> >": {"offset": "0x14870"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13E20"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x28C0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2B40"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA3B0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13BD0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Erase": {"offset": "0x1CE60"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13E20"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x13EF0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA390"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x16420"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x13D60"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x1D000"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xCE50"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x1D630"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x1DD60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xCE50"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2860"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xCE50"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x35000"}, "std::_Uninitialized_copy_n<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x34440"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x32B30"}, "std::_Uninitialized_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x32A90"}, "std::_Uninitialized_move<std::shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > > *,std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > > > >": {"offset": "0x148D0"}, "std::_Variant_base<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>::_Destroy": {"offset": "0x1CDD0"}, "std::_Variant_destroy_layer_<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>::~_Variant_destroy_layer_<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>": {"offset": "0x35010"}, "std::_Variant_dispatcher<std::integer_sequence<unsigned __int64,0> >::_Dispatch2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,`skyr::v1::host::serialize'::`2'::<lambda_1> const &,std::variant<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> const &,1>": {"offset": "0x34280"}, "std::_Variant_raw_visit1<2>::_Visit<std::_Variant_assign_visitor<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>,std::_Variant_storage_<0,skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> >": {"offset": "0x34750"}, "std::_Variant_raw_visit1<2>::_Visit<std::_Variant_assign_visitor<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>,std::_Variant_storage_<0,skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> const &>": {"offset": "0x344D0"}, "std::_Xlen_string": {"offset": "0xD0F0"}, "std::allocator<char32_t>::allocate": {"offset": "0x3A6D0"}, "std::allocator<char32_t>::deallocate": {"offset": "0x3A740"}, "std::allocator<char>::allocate": {"offset": "0xD110"}, "std::allocator<char>::deallocate": {"offset": "0x1E570"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x1E500"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x30D10"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x32F00"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x32FC0"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::allocate": {"offset": "0x32E90"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x32F70"}, "std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > > >::deallocate": {"offset": "0x1E5B0"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x30D10"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x1E570"}, "std::allocator<void *>::allocate": {"offset": "0x1E500"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD170"}, "std::allocator<wchar_t>::deallocate": {"offset": "0x2F670"}, "std::bad_alloc::bad_alloc": {"offset": "0x43010"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA5A0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA190"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA5A0"}, "std::bad_cast::bad_cast": {"offset": "0x235D0"}, "std::bad_cast::~bad_cast": {"offset": "0xA5A0"}, "std::bad_optional_access::bad_optional_access": {"offset": "0x34D00"}, "std::bad_optional_access::what": {"offset": "0x11830"}, "std::bad_optional_access::~bad_optional_access": {"offset": "0xA5A0"}, "std::bad_variant_access::bad_variant_access": {"offset": "0x34D60"}, "std::bad_variant_access::what": {"offset": "0x36050"}, "std::bad_variant_access::~bad_variant_access": {"offset": "0xA5A0"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x16BD0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x17010"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x27A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x14240"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x143B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x26550"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>": {"offset": "0x14540"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append'::`2'::<lambda_1>,char const *,unsigned __int64>": {"offset": "0x143B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert'::`2'::<lambda_1>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x34290"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::push_back'::`2'::<lambda_1>,char>": {"offset": "0x14240"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0xA470"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x23BD0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD360"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x15AB0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0x121F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::push_back": {"offset": "0x40C30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA470"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Reallocate_grow_by<`std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::insert'::`2'::<lambda_1>,unsigned __int64,unsigned __int64,char32_t>": {"offset": "0x38DB0"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Tidy_deallocate": {"offset": "0x3A660"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >": {"offset": "0x3A270"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::push_back": {"offset": "0x3B700"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::~basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >": {"offset": "0x3A370"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x1FB60"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA4D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD1E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x15BE0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x2F6B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x2F810"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA4D0"}, "std::basic_string_view<char,std::char_traits<char> >::_Xran": {"offset": "0x32E70"}, "std::basic_string_view<char,std::char_traits<char> >::find_first_of": {"offset": "0x33010"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_always_noconv": {"offset": "0xE4F0"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_encoding": {"offset": "0xE430"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_in": {"offset": "0xF180"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_length": {"offset": "0xF5D0"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_max_length": {"offset": "0xF5E0"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_out": {"offset": "0xF400"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_unshift": {"offset": "0xF5C0"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x14B90"}, "std::error_category::default_error_condition": {"offset": "0x116C0"}, "std::error_category::equivalent": {"offset": "0x116D0"}, "std::error_code::error_code<enum skyr::v1::url_parse_errc,0>": {"offset": "0x33D00"}, "std::exception::exception": {"offset": "0xA1C0"}, "std::exception::what": {"offset": "0xE240"}, "std::exception_ptr::~exception_ptr": {"offset": "0x16ED0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x16380"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x16380"}, "std::function<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> __cdecl(fwRefContainer<fx::Resource>)>::~function<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> __cdecl(fwRefContainer<fx::Resource>)>": {"offset": "0x16380"}, "std::function<unsigned char __cdecl(fwRefContainer<fx::Resource>)>::~function<unsigned char __cdecl(fwRefContainer<fx::Resource>)>": {"offset": "0x16380"}, "std::function<void __cdecl(fwRefContainer<fx::Resource>)>::~function<void __cdecl(fwRefContainer<fx::Resource>)>": {"offset": "0x16380"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x15CB0"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x16380"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x16380"}, "std::locale::~locale": {"offset": "0x16EE0"}, "std::lock_guard<std::mutex>::~lock_guard<std::mutex>": {"offset": "0x16740"}, "std::logic_error::logic_error": {"offset": "0x3A320"}, "std::move<tl::unexpected<fx::ResourceManagerError> &>": {"offset": "0x14DB0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x24090"}, "std::numpunct<char>::do_falsename": {"offset": "0x240B0"}, "std::numpunct<char>::do_grouping": {"offset": "0x24130"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x24170"}, "std::numpunct<char>::do_truename": {"offset": "0x24190"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x233E0"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x23880"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x240A0"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x240F0"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x24130"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x24180"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x241D0"}, "std::optional<skyr::v1::host>::~optional<skyr::v1::host>": {"offset": "0x34FE0"}, "std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x34FD0"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x16750"}, "std::range_error::range_error": {"offset": "0x16120"}, "std::range_error::~range_error": {"offset": "0xA5A0"}, "std::runtime_error::runtime_error": {"offset": "0x16170"}, "std::runtime_error::~runtime_error": {"offset": "0xA5A0"}, "std::shared_ptr<Concurrency::details::_ExceptionHolder>::~shared_ptr<Concurrency::details::_ExceptionHolder>": {"offset": "0x16800"}, "std::shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > >::~shared_ptr<Concurrency::details::_Task_impl<fwRefContainer<fx::Resource> > >": {"offset": "0x16800"}, "std::shared_ptr<Concurrency::details::_Task_impl_base>::~shared_ptr<Concurrency::details::_Task_impl_base>": {"offset": "0x16800"}, "std::shared_ptr<Concurrency::scheduler_interface>::~shared_ptr<Concurrency::scheduler_interface>": {"offset": "0x16800"}, "std::to_string": {"offset": "0x35D60"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0x16850"}, "std::unique_ptr<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore,std::default_delete<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore> >::~unique_ptr<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore,std::default_delete<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore> >": {"offset": "0x16880"}, "std::unique_ptr<Concurrency::details::_TaskProcHandle,std::default_delete<Concurrency::details::_TaskProcHandle> >::~unique_ptr<Concurrency::details::_TaskProcHandle,std::default_delete<Concurrency::details::_TaskProcHandle> >": {"offset": "0x16860"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x16860"}, "std::use_facet<std::ctype<char> >": {"offset": "0x36060"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x22FC0"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x23130"}, "std::weak_ptr<Concurrency::details::_Task_impl_base>::~weak_ptr<Concurrency::details::_Task_impl_base>": {"offset": "0x163B0"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::from_bytes": {"offset": "0x1E600"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::to_bytes": {"offset": "0x1F390"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >": {"offset": "0x15D30"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::~wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >": {"offset": "0x16990"}, "streaming::SetNextLevelPath": {"offset": "0x1B9F0"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::bad_expected_access<enum skyr::v1::domain_errc>": {"offset": "0x34C90"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::what": {"offset": "0x33CF0"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::~bad_expected_access<enum skyr::v1::domain_errc>": {"offset": "0xA5A0"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0x32C30"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::what": {"offset": "0x33CF0"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::~bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0xA5A0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::bad_expected_access<enum skyr::v1::url_parse_errc>": {"offset": "0x34CB0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::what": {"offset": "0x33CF0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::~bad_expected_access<enum skyr::v1::url_parse_errc>": {"offset": "0xA5A0"}, "tl::detail::and_then_impl<tl::expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>,`skyr::v1::details::make_url'::`2'::<lambda_1>,0,tl::expected<skyr::v1::url,enum skyr::v1::url_parse_errc> >": {"offset": "0x34A40"}, "tl::detail::expected_operations_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::assign<fwRefContainer<fx::Resource>,0>": {"offset": "0x14940"}, "tl::detail::expected_operations_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::geterr": {"offset": "0x14DB0"}, "tl::detail::expected_storage_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError,0,0>::~expected_storage_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError,0,0>": {"offset": "0x16670"}, "tl::detail::expected_storage_base<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc,0,1>::~expected_storage_base<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc,0,1>": {"offset": "0x16600"}, "tl::detail::expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc,0,1>::~expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc,0,1>": {"offset": "0x16600"}, "tl::detail::expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc,0,1>::~expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc,0,1>": {"offset": "0x16600"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::domain_errc> >": {"offset": "0x34C10"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc> >": {"offset": "0x32BB0"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::url_parse_errc> >": {"offset": "0x3C7F0"}, "tl::expected<char,enum skyr::v1::percent_encoding::percent_encode_errc>::err": {"offset": "0x14DB0"}, "tl::expected<char32_t,enum skyr::v1::domain_errc>::err": {"offset": "0x14DB0"}, "tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::~expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>": {"offset": "0x165E0"}, "tl::expected<skyr::v1::host,enum skyr::v1::url_parse_errc>::err": {"offset": "0x14DB0"}, "tl::expected<skyr::v1::host,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::host,enum skyr::v1::url_parse_errc>": {"offset": "0x34FE0"}, "tl::expected<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc>": {"offset": "0x165D0"}, "tl::expected<skyr::v1::url,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::url,enum skyr::v1::url_parse_errc>": {"offset": "0x165F0"}, "tl::expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>": {"offset": "0x35020"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::err": {"offset": "0x14DB0"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::value<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>": {"offset": "0x40D50"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>": {"offset": "0x16600"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0x165D0"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc>": {"offset": "0x165D0"}, "tl::expected<unsigned short,enum skyr::v1::url_parse_errc>::err": {"offset": "0x14DB0"}, "tl::unexpected<enum skyr::v1::domain_errc>::value": {"offset": "0x14DB0"}, "tl::unexpected<enum skyr::v1::percent_encoding::percent_encode_errc>::value": {"offset": "0x14DB0"}, "tl::unexpected<enum skyr::v1::url_parse_errc>::value": {"offset": "0x14DB0"}, "tl::unexpected<fx::ResourceManagerError>::~unexpected<fx::ResourceManagerError>": {"offset": "0xA470"}, "utf8::exception::exception": {"offset": "0x2DEE0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x26900"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x27EB0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x2DF70"}, "utf8::invalid_code_point::what": {"offset": "0x2F9E0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA5A0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x2DFE0"}, "utf8::invalid_utf8::what": {"offset": "0x2F9F0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA5A0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x2E040"}, "utf8::not_enough_room::what": {"offset": "0x2FA00"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA5A0"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x27990"}, "va_impl<wchar_t,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> > >": {"offset": "0x27BB0"}, "vfs::FindData::~FindData": {"offset": "0xA470"}, "vva": {"offset": "0x2F9C0"}, "xbr::GetGameBuild": {"offset": "0x1B090"}, "xbr::GetReplaceExecutableInit": {"offset": "0x2FBF0"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x2FE10"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1890"}}}