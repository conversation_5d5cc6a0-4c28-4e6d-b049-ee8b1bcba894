@echo off
title FiveM Cheat Testing Setup
color 0A

echo ========================================
echo    FiveM Cheat Testing Setup v1.0
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Este script precisa ser executado como ADMINISTRADOR!
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo [INFO] Executando como administrador... OK
echo.

REM Etapa 1: Verificar dependências
echo ========================================
echo ETAPA 1: Verificando dependencias...
echo ========================================

REM Verificar se Visual Studio está instalado
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2022 encontrado
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2019 encontrado
) else (
    echo [WARNING] Visual Studio nao encontrado!
    echo Instale Visual Studio 2022 Community antes de continuar
)

REM Verificar se FiveM está instalado
if exist "%LOCALAPPDATA%\FiveM\FiveM.exe" (
    echo [OK] FiveM encontrado
) else (
    echo [WARNING] FiveM nao encontrado no local padrao
    echo Certifique-se de que o FiveM esta instalado
)

echo.

REM Etapa 2: Compilar Offset Dumper
echo ========================================
echo ETAPA 2: Compilando Offset Dumper...
echo ========================================

if not exist "external\imgui" (
    echo [INFO] Configurando Dear ImGui...
    call setup_imgui.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao configurar ImGui!
        pause
        exit /b 1
    )
)

echo [INFO] Compilando FiveM Offset Dumper...
call build.bat
if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao do Offset Dumper!
    pause
    exit /b 1
)

if exist "FiveMOffsetDumper.dll" (
    echo [OK] FiveMOffsetDumper.dll compilado com sucesso!
) else (
    echo [ERROR] FiveMOffsetDumper.dll nao foi criado!
    pause
    exit /b 1
)

echo.

REM Etapa 3: Preparar exemplo de cheat
echo ========================================
echo ETAPA 3: Preparando exemplo de cheat...
echo ========================================

cd examples

echo [INFO] Compilando exemplo de cheat...
call build_cheat.bat
if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao do cheat de exemplo!
    cd ..
    pause
    exit /b 1
)

if exist "FiveMSimpleCheat.dll" (
    echo [OK] FiveMSimpleCheat.dll compilado com sucesso!
) else (
    echo [ERROR] FiveMSimpleCheat.dll nao foi criado!
    cd ..
    pause
    exit /b 1
)

cd ..
echo.

REM Etapa 4: Verificar injetores
echo ========================================
echo ETAPA 4: Verificando injetores...
echo ========================================

set "injector_found=false"

REM Verificar Process Hacker
if exist "C:\Program Files\Process Hacker 2\ProcessHacker.exe" (
    echo [OK] Process Hacker 2 encontrado
    set "injector_found=true"
) else if exist "C:\Program Files (x86)\Process Hacker 2\ProcessHacker.exe" (
    echo [OK] Process Hacker 2 encontrado
    set "injector_found=true"
)

if "%injector_found%"=="false" (
    echo [WARNING] Nenhum injetor conhecido encontrado!
    echo.
    echo Injetores recomendados:
    echo 1. Process Hacker 2 - https://processhacker.sourceforge.io/
    echo 2. Extreme Injector - https://github.com/master131/ExtremeInjector
    echo 3. Manual DLL Injector
    echo.
    echo Baixe e instale um injetor antes de continuar.
)

echo.

REM Etapa 5: Instruções finais
echo ========================================
echo ETAPA 5: Instrucoes para teste
echo ========================================
echo.
echo ARQUIVOS CRIADOS:
echo - FiveMOffsetDumper.dll (Dumper de offsets)
echo - examples\FiveMSimpleCheat.dll (Cheat de exemplo)
echo.
echo PROXIMOS PASSOS:
echo.
echo 1. ABRA O FIVEM:
echo    - Inicie o FiveM
echo    - Entre em um servidor (preferencialmente privado/teste)
echo    - Aguarde carregar completamente
echo.
echo 2. OBTER OFFSETS:
echo    - Abra Process Hacker como administrador
echo    - Encontre processo FiveM.exe ou FiveM_GTAProcess.exe
echo    - Injete FiveMOffsetDumper.dll
echo    - No jogo, pressione INSERT para abrir interface
echo    - Clique "Scan All Offsets"
echo    - Exporte para C++ Header (File ^> Export to C++ Header)
echo    - Salve como "offsets.h"
echo.
echo 3. ATUALIZAR CHEAT:
echo    - Copie offsets.h para pasta examples\
echo    - Recompile o cheat: cd examples ^&^& build_cheat.bat
echo.
echo 4. TESTAR CHEAT:
echo    - Injete examples\FiveMSimpleCheat.dll no FiveM
echo    - Pressione INSERT para menu
echo    - Pressione F1 para ESP
echo    - Pressione F2 para God Mode
echo.
echo CONTROLES DO CHEAT:
echo - INSERT: Abrir/fechar menu
echo - F1: Toggle ESP
echo - F2: Toggle God Mode
echo.
echo IMPORTANTE:
echo - Use apenas em servidores privados/teste
echo - Sempre teste com conta secundaria
echo - Offsets podem mudar com atualizacoes do FiveM
echo.

REM Criar arquivo de log
echo ========================================
echo ETAPA 6: Criando arquivo de log...
echo ========================================

echo FiveM Cheat Testing Setup - %date% %time% > test_log.txt
echo. >> test_log.txt
echo Arquivos criados: >> test_log.txt
if exist "FiveMOffsetDumper.dll" echo - FiveMOffsetDumper.dll >> test_log.txt
if exist "examples\FiveMSimpleCheat.dll" echo - examples\FiveMSimpleCheat.dll >> test_log.txt
echo. >> test_log.txt
echo Status: Setup concluido com sucesso >> test_log.txt

echo [OK] Log salvo em test_log.txt
echo.

REM Perguntar se quer abrir Process Hacker
echo ========================================
echo Deseja abrir o Process Hacker agora? (s/n)
set /p choice="> "

if /i "%choice%"=="s" (
    if exist "C:\Program Files\Process Hacker 2\ProcessHacker.exe" (
        echo [INFO] Abrindo Process Hacker...
        start "" "C:\Program Files\Process Hacker 2\ProcessHacker.exe"
    ) else if exist "C:\Program Files (x86)\Process Hacker 2\ProcessHacker.exe" (
        echo [INFO] Abrindo Process Hacker...
        start "" "C:\Program Files (x86)\Process Hacker 2\ProcessHacker.exe"
    ) else (
        echo [ERROR] Process Hacker nao encontrado!
    )
)

echo.
echo ========================================
echo Setup concluido! Leia TESTING_GUIDE.md para instrucoes detalhadas.
echo ========================================
pause
