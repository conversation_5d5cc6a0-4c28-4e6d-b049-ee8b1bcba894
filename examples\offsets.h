/*
 * FiveM Offsets Header
 * Gerado automaticamente pelo FiveM Offset Dumper
 * 
 * IMPORTANTE: Este<PERSON> são offsets de exemplo!
 * Use o offset dumper para obter os offsets reais para sua versão do FiveM
 */

#pragma once
#include <cstdint>

namespace FiveMOffsets {
    // Base do módulo (será definida em runtime)
    extern uintptr_t MODULE_BASE;
    
    namespace Player {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t LocalPlayer = 0x123456;      // Ponteiro para jogador local
        constexpr uintptr_t PlayerList = 0x234567;       // Array de todos os jogadores
        constexpr uintptr_t PlayerInfo = 0x345678;       // Estrutura de informações do jogador
        constexpr uintptr_t PlayerCoords = 0x90;         // Offset das coordenadas no jogador
        constexpr uintptr_t PlayerHealth = 0x280;        // Offset da vida no jogador
        constexpr uintptr_t PlayerArmor = 0x14C;         // Offset da armadura no jogador
    }
    
    namespace Vehicle {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t VehiclePool = 0x456789;      // Pool de veículos
        constexpr uintptr_t VehicleCoords = 0x90;        // Offset das coordenadas no veículo
        constexpr uintptr_t VehicleSpeed = 0x7C;         // Offset da velocidade no veículo
        constexpr uintptr_t VehicleHealth = 0x280;       // Offset da vida no veículo
        constexpr uintptr_t VehicleGravity = 0xBC;       // Offset da gravidade no veículo
    }
    
    namespace World {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t WorldPtr = 0x567890;         // Ponteiro para o mundo/estado do jogo
        constexpr uintptr_t GameState = 0x678901;        // Estado atual do jogo
        constexpr uintptr_t TimeScale = 0x789012;        // Escala de tempo do jogo
        constexpr uintptr_t Weather = 0x890123;          // Clima atual
    }
    
    namespace Weapon {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t WeaponManager = 0x901234;    // Gerenciador de armas
        constexpr uintptr_t CurrentWeapon = 0x20;        // Offset da arma atual
        constexpr uintptr_t WeaponAmmo = 0x18;           // Offset da munição da arma
        constexpr uintptr_t WeaponDamage = 0x98;         // Offset do multiplicador de dano
    }
    
    namespace Network {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t NetworkManager = 0x012345;   // Gerenciador de rede
        constexpr uintptr_t SessionInfo = 0x123450;      // Informações da sessão
        constexpr uintptr_t PlayerCount = 0x234501;      // Contagem de jogadores
    }
    
    namespace Misc {
        // Offsets de exemplo - DEVEM ser atualizados com o dumper!
        constexpr uintptr_t FrameCount = 0x345012;       // Contador de frames
        constexpr uintptr_t GameVersion = 0x450123;      // Versão do jogo
        constexpr uintptr_t FPS = 0x501234;              // FPS atual
        constexpr uintptr_t MenuState = 0x012340;        // Estado do menu
    }
}

/*
 * COMO USAR ESTE ARQUIVO:
 * 
 * 1. Execute o FiveM Offset Dumper no processo do FiveM
 * 2. Use a opção "Export to C++ Header" para gerar offsets atualizados
 * 3. Substitua este arquivo pelo gerado pelo dumper
 * 4. Recompile seu cheat
 * 
 * AVISO: Os offsets acima são apenas exemplos e NÃO funcionarão!
 * Você DEVE usar o offset dumper para obter os offsets corretos.
 * 
 * Offsets podem mudar com cada atualização do FiveM, então sempre
 * re-escaneie após atualizações.
 */
