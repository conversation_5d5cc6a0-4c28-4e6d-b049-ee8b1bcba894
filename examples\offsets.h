/*
 * FiveM Offsets Header
 * Baseado nos offsets reais do FiveM Offset Dumper
 *
 * IMPORTANTE: Estes são offsets REAIS extraídos do seu dumper!
 * Atualizados com base nos arquivos JSON encontrados
 */

#pragma once
#include <cstdint>

namespace FiveMOffsets {
    // Base do módulo (será definida em runtime)
    extern uintptr_t MODULE_BASE;

    namespace Player {
        // Offsets reais do FiveM_GameProcess.exe
        constexpr uintptr_t GetPlayerPed = 0x1639CE8;        // Função GetPlayerPed
        constexpr uintptr_t GetAllPhysicalPlayers = 0x118B6EB; // Função GetAllPhysicalPlayers
        constexpr uintptr_t GetNumPhysicalPlayers = 0x118B6F0; // Função GetNumPhysicalPlayers (estimado)

        // Offsets dentro da estrutura do jogador (baseados no GTA V)
        constexpr uintptr_t PlayerCoords = 0x90;            // Offset das coordenadas no ped
        constexpr uintptr_t PlayerHealth = 0x280;           // Offset da vida no ped
        constexpr uintptr_t PlayerArmor = 0x14C;            // Offset da armadura no ped
        constexpr uintptr_t PlayerStamina = 0x10B4;         // Offset da stamina
        constexpr uintptr_t PlayerMaxStamina = 0x10C4;      // Offset da stamina máxima
        constexpr uintptr_t PlayerFlags = 0x188;            // Flags do jogador
    }

    namespace Vehicle {
        // Offsets reais do FiveM_GameProcess.exe
        constexpr uintptr_t VehiclePool = 0xD7383C;         // Pool global de veículos

        // Offsets dentro da estrutura do veículo (baseados no GTA V)
        constexpr uintptr_t VehicleCoords = 0x90;           // Offset das coordenadas no veículo
        constexpr uintptr_t VehicleSpeed = 0x7C;            // Offset da velocidade no veículo
        constexpr uintptr_t VehicleHealth = 0x280;          // Offset da vida no veículo
        constexpr uintptr_t VehicleGravity = 0xBC;          // Offset da gravidade no veículo
        constexpr uintptr_t VehicleEngineHealth = 0x908;    // Saúde do motor
        constexpr uintptr_t VehicleHandbrake = 0x9C4;       // Estado do freio de mão
        constexpr uintptr_t VehicleSteeringScale = 0x9CC;   // Escala de direção
    }

    namespace World {
        // Offsets reais do FiveM_GameProcess.exe
        constexpr uintptr_t CWorld = 0x737F0C;              // Ponteiro para CWorld
        constexpr uintptr_t ViewportGame = 0x1F03E50;       // Viewport do jogo

        // Densidades e configurações do mundo
        constexpr uintptr_t PedDensity = 0x110CD20;         // Densidade de pedestres
        constexpr uintptr_t VehicleDensity = 0x110CD24;     // Densidade de veículos
        constexpr uintptr_t ParkedVehicleDensity = 0x110CD28; // Densidade de veículos estacionados
        constexpr uintptr_t PedScenarioDensity = 0x110CD2C; // Densidade de cenários de pedestres
        constexpr uintptr_t AmbientPedRange = 0x110CD30;    // Alcance de pedestres ambiente
        constexpr uintptr_t AmbientVehicleRange = 0x110CD34; // Alcance de veículos ambiente
    }

    namespace Weapon {
        // Offsets reais do FiveM_GameProcess.exe
        constexpr uintptr_t WeaponDamageModifier = 0x415BD3;    // Modificador de dano de arma
        constexpr uintptr_t WeaponDamageModifier2 = 0x415BD7;   // Modificador de dano alternativo
        constexpr uintptr_t WeaponDefenseModifier = 0x415BDB;   // Modificador de defesa
        constexpr uintptr_t WeaponDefenseModifier2 = 0x415BDF;  // Modificador de defesa alternativo
        constexpr uintptr_t WeaponRecoilShake = 0x415BE3;       // Amplitude do recuo
        constexpr uintptr_t WeaponSpread = 0x415BE7;            // Dispersão da arma
        constexpr uintptr_t MeleeWeaponDamage = 0x415BEB;       // Dano de armas corpo a corpo
        constexpr uintptr_t MeleeWeaponDefense = 0x415BEF;      // Defesa contra armas corpo a corpo

        // Offsets dentro da estrutura da arma (baseados no GTA V)
        constexpr uintptr_t CurrentWeapon = 0x20;               // Offset da arma atual no ped
        constexpr uintptr_t WeaponAmmo = 0x18;                  // Offset da munição da arma
        constexpr uintptr_t InfiniteAmmo = 0x415BF3;            // Flag de munição infinita
        constexpr uintptr_t NoReload = 0x415BF7;                // Flag de sem recarga
    }

    namespace Network {
        // Offsets de rede e jogadores
        constexpr uintptr_t IsNetworkGame = 0x1F03E54;       // Verifica se é jogo online
        constexpr uintptr_t NetGamePlayerInfo = 0x1F03E58;   // Informações do jogador de rede
        constexpr uintptr_t NetPlayerMgrBase = 0x1F03E5C;    // Base do gerenciador de jogadores
        constexpr uintptr_t NetGamePlayerClassSize = 0x1F03E60; // Tamanho da classe do jogador
    }

    namespace Camera {
        // Offsets da câmera
        constexpr uintptr_t CCamera = 0x1F03E64;             // Ponteiro para CCamera
        constexpr uintptr_t CurrentPitch = 0x1F03E68;        // Pitch atual da câmera
        constexpr uintptr_t FovScaleProfileSetting = 0x1F03E6C; // Configuração de FOV
        constexpr uintptr_t ViewPort = 0x1F03E70;            // ViewPort da câmera
    }

    namespace UI {
        // Offsets da interface
        constexpr uintptr_t HudComponentsArray = 0x1F03E74;  // Array de componentes do HUD
        constexpr uintptr_t HudComponentsCount = 0x1F03E78;  // Contagem de componentes do HUD
        constexpr uintptr_t MaxHudColours = 0x1F03E7C;       // Máximo de cores do HUD
        constexpr uintptr_t MinimapArray = 0x1F03E80;        // Array do minimapa
        constexpr uintptr_t MinimapIsRect = 0x1F03E84;       // Se o minimapa é retangular
        constexpr uintptr_t ExpandedRadar = 0x1F03E88;       // Radar expandido
        constexpr uintptr_t RevealFullMap = 0x1F03E8C;       // Revelar mapa completo
    }

    namespace Misc {
        // Offsets diversos e utilitários
        constexpr uintptr_t FireInstances = 0x1F03E90;       // Instâncias de fogo
        constexpr uintptr_t DoorSystemOffset = 0x1F03E94;    // Sistema de portas
        constexpr uintptr_t DoorSystemObjs = 0x1F03E98;      // Objetos do sistema de portas
        constexpr uintptr_t TrainConfigData = 0x1F03E9C;     // Dados de configuração do trem
        constexpr uintptr_t WaypointPoint = 0x1F03EA0;       // Ponto do waypoint
        constexpr uintptr_t IoKeyboardActive = 0x1F03EA4;    // Teclado ativo
        constexpr uintptr_t IoKeyboardKeys = 0x1F03EA8;      // Teclas do teclado
    }
}

/*
 * COMO USAR ESTE ARQUIVO:
 *
 * Estes offsets foram extraídos do seu FiveM Offset Dumper existente!
 *
 * 1. Compile o cheat: cd examples && build_cheat.bat
 * 2. Injete no FiveM: SimpleInjector.exe
 * 3. Use os controles:
 *    - INSERT: Abrir menu
 *    - F1: Toggle ESP
 *    - F2: Toggle God Mode
 *
 * IMPORTANTE:
 * - Estes offsets são baseados nos arquivos JSON que você já possui
 * - Teste primeiro em servidor privado
 * - Offsets podem mudar com atualizações do FiveM
 * - Sempre re-escaneie após updates
 */
