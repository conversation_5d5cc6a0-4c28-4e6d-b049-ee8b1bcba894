#include "PatternScanner.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <Psapi.h>

#undef min
#undef max

PatternScanner::PatternScanner() {
}

PatternScanner::~PatternScanner() {
}

uintptr_t PatternScanner::FindPattern(const std::string& pattern, uintptr_t startAddress, size_t searchSize) {
    try {
        auto bytes = PatternToBytes(pattern);
        if (bytes.empty()) {
            m_lastError = "Invalid pattern format";
            return 0;
        }
        
        // Create mask for wildcards
        std::vector<bool> mask;
        std::istringstream iss(pattern);
        std::string token;
        
        while (iss >> token) {
            mask.push_back(token != "??" && token != "?");
        }
        
        if (startAddress == 0) {
            // Get main module info
            HMODULE hModule = GetModuleHandle(nullptr);
            MODULEINFO modInfo;
            if (!GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
                m_lastError = "Failed to get module information";
                return 0;
            }
            startAddress = reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
            searchSize = modInfo.SizeOfImage;
        }
        
        return ScanMemoryRegion(bytes, mask, startAddress, searchSize);
        
    } catch (const std::exception& e) {
        m_lastError = std::string("Exception in FindPattern: ") + e.what();
        return 0;
    }
}

uintptr_t PatternScanner::FindPatternInModule(const std::string& pattern, const std::string& moduleName) {
    HMODULE hModule = GetModuleHandleA(moduleName.c_str());
    if (!hModule) {
        m_lastError = "Module not found: " + moduleName;
        return 0;
    }
    
    return FindPatternInModule(pattern, hModule);
}

uintptr_t PatternScanner::FindPatternInModule(const std::string& pattern, HMODULE hModule) {
    MODULEINFO modInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
        m_lastError = "Failed to get module information";
        return 0;
    }
    
    uintptr_t startAddress = reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
    size_t searchSize = modInfo.SizeOfImage;
    
    return FindPattern(pattern, startAddress, searchSize);
}

std::vector<uintptr_t> PatternScanner::FindAllPatterns(const std::string& pattern, uintptr_t startAddress, size_t searchSize) {
    std::vector<uintptr_t> results;
    
    try {
        auto bytes = PatternToBytes(pattern);
        if (bytes.empty()) {
            m_lastError = "Invalid pattern format";
            return results;
        }
        
        // Create mask for wildcards
        std::vector<bool> mask;
        std::istringstream iss(pattern);
        std::string token;
        
        while (iss >> token) {
            mask.push_back(token != "??" && token != "?");
        }
        
        if (startAddress == 0) {
            HMODULE hModule = GetModuleHandle(nullptr);
            MODULEINFO modInfo;
            if (!GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
                m_lastError = "Failed to get module information";
                return results;
            }
            startAddress = reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
            searchSize = modInfo.SizeOfImage;
        }
        
        // Scan for all occurrences
        uintptr_t currentAddress = startAddress;
        size_t remainingSize = searchSize;
        
        while (remainingSize >= bytes.size()) {
            uintptr_t found = ScanMemoryRegion(bytes, mask, currentAddress, remainingSize);
            if (found == 0) break;
            
            results.push_back(found);
            
            // Move past this result
            size_t offset = found - currentAddress + 1;
            currentAddress += offset;
            remainingSize -= offset;
        }
        
    } catch (const std::exception& e) {
        m_lastError = std::string("Exception in FindAllPatterns: ") + e.what();
    }
    
    return results;
}

std::vector<uint8_t> PatternScanner::PatternToBytes(const std::string& pattern) {
    std::vector<uint8_t> bytes;
    std::istringstream iss(pattern);
    std::string token;

    while (iss >> token) {
        if (token == "??" || token == "?") {
            bytes.push_back(0x00); // Placeholder for wildcard
        } else {
            try {
                bytes.push_back(static_cast<uint8_t>(std::stoul(token, nullptr, 16)));
            } catch (...) {
                return {}; // Invalid pattern
            }
        }
    }

    return bytes;
}

bool PatternScanner::CompareBytes(const uint8_t* data, const std::vector<uint8_t>& pattern, const std::vector<bool>& mask) {
    for (size_t i = 0; i < pattern.size(); ++i) {
        if (mask[i] && data[i] != pattern[i]) {
            return false;
        }
    }
    return true;
}

bool PatternScanner::IsMemoryReadable(uintptr_t address, size_t size) {
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }

    return (mbi.State == MEM_COMMIT) &&
           (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) != 0;
}

uintptr_t PatternScanner::ScanMemoryRegion(const std::vector<uint8_t>& pattern, const std::vector<bool>& mask,
                                          uintptr_t startAddress, size_t size) {
    if (pattern.empty() || size < pattern.size()) {
        return 0;
    }

    const size_t chunkSize = 0x1000; // 4KB chunks

    for (size_t offset = 0; offset < size - pattern.size(); offset += chunkSize) {
        size_t currentChunkSize = std::min(chunkSize, size - offset);
        uintptr_t currentAddress = startAddress + offset;

        if (!IsMemoryReadable(currentAddress, currentChunkSize)) {
            continue;
        }

        __try {
            const uint8_t* data = reinterpret_cast<const uint8_t*>(currentAddress);

            for (size_t i = 0; i <= currentChunkSize - pattern.size(); ++i) {
                if (CompareBytes(data + i, pattern, mask)) {
                    return currentAddress + i;
                }
            }
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            // Memory access violation, skip this region
            continue;
        }
    }

    return 0;
}
