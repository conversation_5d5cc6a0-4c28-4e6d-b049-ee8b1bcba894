cmake_minimum_required(VERSION 3.16)
project(FiveMCheat)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Criar executavel do cheat
add_library(FiveMCheat_FINAL SHARED
    simple_cheat_final.cpp
)

# Linkar bibliotecas necessarias
target_link_libraries(FiveMCheat_FINAL
    user32
)

# Definir output directory
set_target_properties(FiveMCheat_FINAL PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)
