{"conhost-v2.dll": {"<lambda_02837e64370d6fef96f08c65622c99b1>::~<lambda_02837e64370d6fef96f08c65622c99b1>": {"offset": "0x10550"}, "<lambda_2c71f2734007fd255e94b39244217c06>::~<lambda_2c71f2734007fd255e94b39244217c06>": {"offset": "0x10580"}, "<lambda_41af1ba482b752c39807db2ef15caf48>::~<lambda_41af1ba482b752c39807db2ef15caf48>": {"offset": "0xB6E0"}, "<lambda_47435eb07b572c7d5654305a814d3474>::~<lambda_47435eb07b572c7d5654305a814d3474>": {"offset": "0x10550"}, "<lambda_55cf421c54d17ec848e1c39d1a1f440e>::~<lambda_55cf421c54d17ec848e1c39d1a1f440e>": {"offset": "0x10580"}, "<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x10580"}, "<lambda_7604da78e8f33a312c72ae5a43f9b93b>::~<lambda_7604da78e8f33a312c72ae5a43f9b93b>": {"offset": "0x10580"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x10580"}, "<lambda_86be70dd2992469b5252a9192288f807>::~<lambda_86be70dd2992469b5252a9192288f807>": {"offset": "0xB6E0"}, "<lambda_86d4d591f8ee155bf9fb023697a78601>::~<lambda_86d4d591f8ee155bf9fb023697a78601>": {"offset": "0x10550"}, "<lambda_980c07fc34548288b7608f6341665abc>::~<lambda_980c07fc34548288b7608f6341665abc>": {"offset": "0x10550"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xB6E0"}, "<lambda_f17ebabe33bc32be107ce6fff046b802>::~<lambda_f17ebabe33bc32be107ce6fff046b802>": {"offset": "0x10580"}, "BuildFont": {"offset": "0x32AF0"}, "CfxBigConsole::CfxBigConsole": {"offset": "0x1F050"}, "CfxBigConsole::ClearLog": {"offset": "0x24F70"}, "CfxBigConsole::Draw": {"offset": "0x25D80"}, "CfxBigConsole::EndWindow": {"offset": "0x26F80"}, "CfxBigConsole::ExecCommand": {"offset": "0x27110"}, "CfxBigConsole::FilterLog": {"offset": "0x27550"}, "CfxBigConsole::OnAddLog": {"offset": "0x287B0"}, "CfxBigConsole::PreStartWindow": {"offset": "0x28DD0"}, "CfxBigConsole::RunCommandQueue": {"offset": "0x28FD0"}, "CfxBigConsole::StartWindow": {"offset": "0x2A040"}, "CfxBigConsole::TextEditCallback": {"offset": "0x2A260"}, "CfxBigConsole::TextEditCallbackStub": {"offset": "0x2A810"}, "CfxBigConsole::~CfxBigConsole": {"offset": "0x20B90"}, "CfxState::CfxState": {"offset": "0x467E0"}, "CfxWinConsole::EndWindow": {"offset": "0x26FA0"}, "CfxWinConsole::PreStartWindow": {"offset": "0xF770"}, "CfxWinConsole::StartWindow": {"offset": "0x2A190"}, "Component::As": {"offset": "0xF6A0"}, "Component::IsA": {"offset": "0xF760"}, "Component::SetCommandLine": {"offset": "0xB5C0"}, "Component::SetUserData": {"offset": "0xF770"}, "ComponentInstance::DoGameLoad": {"offset": "0xF740"}, "ComponentInstance::Initialize": {"offset": "0xF750"}, "ComponentInstance::Shutdown": {"offset": "0xF770"}, "ConHost::IsConsoleOpen": {"offset": "0x33330"}, "ConHost::Print": {"offset": "0x33E50"}, "ConHost::SetCursorMode": {"offset": "0x10EE0"}, "ConHost::`dynamic initializer for 'OnDrawGui''": {"offset": "0x2160"}, "ConHost::`dynamic initializer for 'OnInvokeNative''": {"offset": "0x2170"}, "ConHost::`dynamic initializer for 'OnShouldDrawGui''": {"offset": "0x2180"}, "ConVar<bool>::ConVar<bool>": {"offset": "0x1DCE0"}, "ConVar<bool>::~ConVar<bool>": {"offset": "0x1FF50"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Allocate_page": {"offset": "0x2B3D0"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Assign_and_destroy_item": {"offset": "0x2B9C0"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Copy_item": {"offset": "0x2CC40"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Deallocate_page": {"offset": "0x2CC60"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Move_item": {"offset": "0x2EFC0"}, "Concurrency::concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~concurrent_queue<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x20710"}, "ConsoleArgumentType<DevGuiPath,void>::Parse": {"offset": "0x37350"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x28CE0"}, "ConsoleArgumentType<int,void>::Parse": {"offset": "0x28C50"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0x11A70"}, "ConsoleCommand::ConsoleCommand<<lambda_41af1ba482b752c39807db2ef15caf48> >": {"offset": "0x11C50"}, "ConsoleCommand::ConsoleCommand<<lambda_4f01932d3df21bafe14b0b705d1c2d2b> >": {"offset": "0x34910"}, "ConsoleCommand::ConsoleCommand<<lambda_5b1c43db5646bdcc3820d1877854a8bd> >": {"offset": "0x11EA0"}, "ConsoleCommand::ConsoleCommand<<lambda_8255e8b86698f5bb3d273a330684cd3f> >": {"offset": "0x34AE0"}, "ConsoleCommand::ConsoleCommand<<lambda_86be70dd2992469b5252a9192288f807> >": {"offset": "0x12080"}, "ConsoleCommand::ConsoleCommand<<lambda_bc85ef5bc811bb4046f609d5aec41b07> >": {"offset": "0x34CB0"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0x122D0"}, "ConsoleCommand::ConsoleCommand<<lambda_c30a8a1d6decbd9789e47aecee8410f4> >": {"offset": "0xFC80"}, "ConsoleCommand::ConsoleCommand<<lambda_cd07e93ce4e084374db77c62c4f7d117> >": {"offset": "0x12520"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x20CC0"}, "ConsoleFlagsToString": {"offset": "0x25940"}, "ConsoleHasMouse": {"offset": "0x32E00"}, "CoreGetComponentRegistry": {"offset": "0x25C60"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x25CF0"}, "CreateComponent": {"offset": "0xF780"}, "CreateFontTexture": {"offset": "0x32F60"}, "CreateVariableEntry<bool>": {"offset": "0x13790"}, "CreateVariableEntry<int>": {"offset": "0x131E0"}, "CreateVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x134C0"}, "DevGuiNode::~DevGuiNode": {"offset": "0x35780"}, "DevGuiPath::~DevGuiPath": {"offset": "0x35770"}, "DevGui_Draw": {"offset": "0x36740"}, "DevGui_InstantiatePath": {"offset": "0x370E0"}, "DllMain": {"offset": "0x4B93C"}, "DloadAcquireSectionWriteAccess": {"offset": "0x4BB44"}, "DloadGetSRWLockFunctionPointers": {"offset": "0x4BBF0"}, "DloadMakePermanentImageCommit": {"offset": "0x4BC90"}, "DloadObtainSection": {"offset": "0x4BD28"}, "DloadProtectSection": {"offset": "0x4BDC4"}, "DloadReleaseSectionWriteAccess": {"offset": "0x4BE54"}, "DoNtRaiseException": {"offset": "0x48AE0"}, "DrawConsole": {"offset": "0x26920"}, "DrawDevGui": {"offset": "0x37310"}, "DrawMiniConsole": {"offset": "0x26F20"}, "DrawWinConsole": {"offset": "0x26F50"}, "EnsureConsoles": {"offset": "0x26FB0"}, "FatalErrorNoExceptRealV": {"offset": "0xCC90"}, "FatalErrorRealV": {"offset": "0xCCC0"}, "FiveMConsoleBase::AddLog": {"offset": "0x24060"}, "FiveMConsoleBase::ClearLog": {"offset": "0x24F90"}, "FiveMConsoleBase::DrawItem": {"offset": "0x26950"}, "FiveMConsoleBase::FilterLog": {"offset": "0xF770"}, "FiveMConsoleBase::FiveMConsoleBase": {"offset": "0x1F5D0"}, "FiveMConsoleBase::GetLineAtIdx": {"offset": "0x27920"}, "FiveMConsoleBase::GetNumLines": {"offset": "0x27990"}, "FiveMConsoleBase::GetTextOffset": {"offset": "0x279E0"}, "FiveMConsoleBase::OnAddLog": {"offset": "0xB5C0"}, "FiveMConsoleBase::RunCommandQueue": {"offset": "0xB5C0"}, "FiveMConsoleBase::Strnicmp": {"offset": "0x2A1E0"}, "FiveMConsoleBase::UpdateLog": {"offset": "0xB5C0"}, "FiveMConsoleBase::~FiveMConsoleBase": {"offset": "0x20D50"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x33F0"}, "GetAbsoluteCitPath": {"offset": "0x47080"}, "GetAdapter": {"offset": "0x3B800"}, "GetAdapterLUID": {"offset": "0x3B930"}, "GetConsoleContext": {"offset": "0x27870"}, "GetConsoleFontSmall": {"offset": "0x33040"}, "GetConsoleFontTiny": {"offset": "0x33050"}, "GlobalErrorHandler": {"offset": "0xCF00"}, "HandleFxDKInput": {"offset": "0x33140"}, "HookFunction::Run": {"offset": "0x31760"}, "HookFunctionBase::Register": {"offset": "0x49900"}, "HookFunctionBase::RunAll": {"offset": "0x49920"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x46410"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x468A0"}, "HostSharedData<ReverseGameData>::HostSharedData<ReverseGameData>": {"offset": "0x31AF0"}, "HostSharedData<TickCountData>::HostSharedData<TickCountData>": {"offset": "0x1E640"}, "HueToRGB": {"offset": "0x27B80"}, "ImFontGlyphRangesBuilder::~ImFontGlyphRangesBuilder": {"offset": "0x32010"}, "ImGui_ImplDX11_CreateDeviceObjects": {"offset": "0x3CF90"}, "ImGui_ImplDX11_CreateWindow": {"offset": "0x3D560"}, "ImGui_ImplDX11_DestroyWindow": {"offset": "0x3D6F0"}, "ImGui_ImplDX11_Init": {"offset": "0x3D780"}, "ImGui_ImplDX11_InvalidateDeviceObjects": {"offset": "0x3D940"}, "ImGui_ImplDX11_NewFrame": {"offset": "0x3DA60"}, "ImGui_ImplDX11_RecreateFontsTexture": {"offset": "0x3DAC0"}, "ImGui_ImplDX11_RenderDrawData": {"offset": "0x3DB20"}, "ImGui_ImplDX11_RenderWindow": {"offset": "0x3E5B0"}, "ImGui_ImplDX11_SetWindowSize": {"offset": "0x3E630"}, "ImGui_ImplDX11_SetupRenderState": {"offset": "0x3E720"}, "ImGui_ImplDX11_SwapBuffers": {"offset": "0x3E920"}, "ImGui_ImplWin32_AddKeyEvent": {"offset": "0x3E940"}, "ImGui_ImplWin32_CreateWindow": {"offset": "0x3E9B0"}, "ImGui_ImplWin32_DestroyWindow": {"offset": "0x3EC20"}, "ImGui_ImplWin32_GetDpiScaleForMonitor": {"offset": "0x3ECC0"}, "ImGui_ImplWin32_GetWindowDpiScale": {"offset": "0x3EE30"}, "ImGui_ImplWin32_GetWindowFocus": {"offset": "0x3EE80"}, "ImGui_ImplWin32_GetWindowMinimized": {"offset": "0x3EEC0"}, "ImGui_ImplWin32_GetWindowPos": {"offset": "0x3EF00"}, "ImGui_ImplWin32_GetWindowSize": {"offset": "0x3EF70"}, "ImGui_ImplWin32_InitPlatformInterface": {"offset": "0x3F000"}, "ImGui_ImplWin32_NewFrame": {"offset": "0x3F280"}, "ImGui_ImplWin32_OnChangedViewport": {"offset": "0xB5C0"}, "ImGui_ImplWin32_SetWindowAlpha": {"offset": "0x3F4D0"}, "ImGui_ImplWin32_SetWindowFocus": {"offset": "0x3F5B0"}, "ImGui_ImplWin32_SetWindowPos": {"offset": "0x3F600"}, "ImGui_ImplWin32_SetWindowSize": {"offset": "0x3F6B0"}, "ImGui_ImplWin32_SetWindowTitle": {"offset": "0x3F770"}, "ImGui_ImplWin32_ShowWindow": {"offset": "0x3F850"}, "ImGui_ImplWin32_UpdateGamepads": {"offset": "0x3F8B0"}, "ImGui_ImplWin32_UpdateMonitors": {"offset": "0x3FDC0"}, "ImGui_ImplWin32_UpdateMonitors_EnumFunc": {"offset": "0x3FE40"}, "ImGui_ImplWin32_UpdateMouseCursor": {"offset": "0x400B0"}, "ImGui_ImplWin32_UpdateMouseData": {"offset": "0x40230"}, "ImGui_ImplWin32_UpdateWindow": {"offset": "0x403C0"}, "ImGui_ImplWin32_VirtualKeyToImGuiKey": {"offset": "0x40570"}, "ImGui_ImplWin32_WndProcHandler": {"offset": "0x40AC0"}, "ImGui_ImplWin32_WndProcHandler_PlatformWindow": {"offset": "0x410C0"}, "InitFunction::Run": {"offset": "0xF7B0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x488E0"}, "InitFunctionBase::Register": {"offset": "0x48C50"}, "InitFunctionBase::RunAll": {"offset": "0x48CA0"}, "InputTarget::KeyDown": {"offset": "0xB5C0"}, "InputTarget::MouseDown": {"offset": "0xB5C0"}, "InputTarget::MouseMove": {"offset": "0xB5C0"}, "InputTarget::MouseUp": {"offset": "0xB5C0"}, "InputTarget::MouseWheel": {"offset": "0xB5C0"}, "IsNonProduction": {"offset": "0x27C20"}, "IsWindows10OrGreater": {"offset": "0x3B9E0"}, "MakeRelativeCitPath": {"offset": "0xD5A0"}, "Microsoft::WRL::ComPtr<IDXGIAdapter3>::~ComPtr<IDXGIAdapter3>": {"offset": "0x38780"}, "Microsoft::WRL::ComPtr<IDXGIAdapter>::~ComPtr<IDXGIAdapter>": {"offset": "0x38780"}, "Microsoft::WRL::ComPtr<IDXGIDevice>::~ComPtr<IDXGIDevice>": {"offset": "0x38780"}, "MiniConsole::ClearLog": {"offset": "0x25000"}, "MiniConsole::Draw": {"offset": "0x262F0"}, "MiniConsole::FilterLog": {"offset": "0x276A0"}, "MiniConsole::MakeRegex": {"offset": "0x27FA0"}, "MiniConsole::MiniConsole": {"offset": "0x1F790"}, "MiniConsole::OnAddLog": {"offset": "0x287D0"}, "MiniConsole::UpdateLog": {"offset": "0x2A820"}, "OnConsoleFrameDraw": {"offset": "0x33350"}, "OnConsoleWndProc": {"offset": "0x33CE0"}, "OpenLogFile": {"offset": "0x288F0"}, "ProcessWndProc": {"offset": "0x10E90"}, "ProgramArguments::ProgramArguments<char const *,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x345E0"}, "ProgramArguments::~ProgramArguments": {"offset": "0x35770"}, "RaiseDebugException": {"offset": "0x48BC0"}, "ReleaseCaptureStub": {"offset": "0x33E60"}, "RenderDrawListInternal": {"offset": "0x33E80"}, "ReverseGameData::ReverseGameData": {"offset": "0x31F00"}, "RunConsoleGameFrame": {"offset": "0x29470"}, "RunConsoleWndProc": {"offset": "0x34250"}, "ScopedError::~ScopedError": {"offset": "0xB7B0"}, "SendPrintMessage": {"offset": "0x294F0"}, "SysError": {"offset": "0xDB20"}, "TextSelect::copy": {"offset": "0x3BBF0"}, "TextSelect::drawSelection": {"offset": "0x3BED0"}, "TextSelect::getSelection": {"offset": "0x3C3B0"}, "TextSelect::handleMouseDown": {"offset": "0x3C4A0"}, "TextSelect::selectAll": {"offset": "0x3CA20"}, "TextSelect::update": {"offset": "0x3CC10"}, "TextSelect::~TextSelect": {"offset": "0x20EC0"}, "ToNarrow": {"offset": "0x48CD0"}, "ToWide": {"offset": "0x48DC0"}, "TraceRealV": {"offset": "0x490D0"}, "Win32TrapAndJump64": {"offset": "0x49F20"}, "_DllMainCRTStartup": {"offset": "0x4B2F0"}, "_Init_thread_abort": {"offset": "0x4A594"}, "_Init_thread_footer": {"offset": "0x4A5C4"}, "_Init_thread_header": {"offset": "0x4A624"}, "_Init_thread_notify": {"offset": "0x4A68C"}, "_Init_thread_wait": {"offset": "0x4A6D0"}, "_IsWindowsVersionOrGreater": {"offset": "0x41170"}, "_RTC_Initialize": {"offset": "0x4B98C"}, "_RTC_Terminate": {"offset": "0x4B9C8"}, "__ArrayUnwind": {"offset": "0x4AF04"}, "__GSHandlerCheck": {"offset": "0x4ACB0"}, "__GSHandlerCheckCommon": {"offset": "0x4ACD0"}, "__GSHandlerCheck_EH": {"offset": "0x4AD2C"}, "__GSHandlerCheck_SEH": {"offset": "0x4B4AC"}, "__chkstk": {"offset": "0x4AF80"}, "__crt_debugger_hook": {"offset": "0x4B6E0"}, "__delayLoadHelper2": {"offset": "0x4BEE8"}, "__dyn_tls_init": {"offset": "0x4AAF8"}, "__dyn_tls_on_demand_init": {"offset": "0x4AB60"}, "__isa_available_init": {"offset": "0x4B534"}, "__local_stdio_printf_options": {"offset": "0xF580"}, "__local_stdio_scanf_options": {"offset": "0x4B960"}, "__raise_securityfailure": {"offset": "0x4B330"}, "__report_gsfailure": {"offset": "0x4B364"}, "__scrt_acquire_startup_lock": {"offset": "0x4A778"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x4A7B4"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x4A7E8"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x4A800"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x4A828"}, "__scrt_dllmain_exception_filter": {"offset": "0x4A840"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x4A8A0"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x4A8D0"}, "__scrt_fastfail": {"offset": "0x4B6E8"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x4B984"}, "__scrt_initialize_crt": {"offset": "0x4A8E4"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x4B968"}, "__scrt_initialize_onexit_tables": {"offset": "0x4A930"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x4A49C"}, "__scrt_initialize_type_info": {"offset": "0x4AE0C"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x4A9BC"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x4E17C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x4B884"}, "__scrt_release_startup_lock": {"offset": "0x4AA54"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xF770"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xF770"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xF770"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xF770"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xF770"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xF6A0"}, "__scrt_throw_std_bad_alloc": {"offset": "0x4B854"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xE320"}, "__scrt_uninitialize_crt": {"offset": "0x4AA78"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x4A56C"}, "__scrt_uninitialize_type_info": {"offset": "0x4AE1C"}, "__security_check_cookie": {"offset": "0x4ADC0"}, "__security_init_cookie": {"offset": "0x4B890"}, "__std_find_trivial_1": {"offset": "0x4A210"}, "__std_find_trivial_2": {"offset": "0x4A2E0"}, "_get_startup_argv_mode": {"offset": "0x4B87C"}, "_guard_check_icall_nop": {"offset": "0xB5C0"}, "_guard_dispatch_icall_nop": {"offset": "0x4C1D0"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x4C1F0"}, "_onexit": {"offset": "0x4AAA4"}, "_snprintf": {"offset": "0x313F0"}, "_wwassert": {"offset": "0x47380"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x4E1EC"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x4E24B"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x4E262"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x4E27B"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x4E28F"}, "`dynamic initializer for 'HostSharedData<ReverseGameData>::m_fakeData''": {"offset": "0x2110"}, "`dynamic initializer for 'HostSharedData<TickCountData>::m_fakeData''": {"offset": "0x12F0"}, "`dynamic initializer for 'OnPushNetMetrics''": {"offset": "0x2670"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1320"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x1350"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x1380"}, "`dynamic initializer for '_init_instance_28''": {"offset": "0x2680"}, "`dynamic initializer for '_init_instance_29''": {"offset": "0x26B0"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0x13B0"}, "`dynamic initializer for '_init_instance_30''": {"offset": "0x26E0"}, "`dynamic initializer for 'devGuiRoot''": {"offset": "0x24E0"}, "`dynamic initializer for 'g_conHostMutex''": {"offset": "0x2220"}, "`dynamic initializer for 'g_consoleThread''": {"offset": "0x12A0"}, "`dynamic initializer for 'g_consoles''": {"offset": "0x13E0"}, "`dynamic initializer for 'g_consolesMutex''": {"offset": "0x1420"}, "`dynamic initializer for 'g_prodCommandsWhitelist''": {"offset": "0x1450"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x2250"}, "`dynamic initializer for 'initFunction''": {"offset": "0x2080"}, "`dynamic initializer for 'initFunctionCon''": {"offset": "0x20C0"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x2AB0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x2A60"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,0,6>::ms_initFunction''": {"offset": "0x2630"}, "`dynamic initializer for 'xbr::virt::Base<4,2802,0,6>::ms_initFunction''": {"offset": "0x2120"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>,<lambda_02837e64370d6fef96f08c65622c99b1> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>,<lambda_2c71f2734007fd255e94b39244217c06> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>,<lambda_47435eb07b572c7d5654305a814d3474> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>,<lambda_55cf421c54d17ec848e1c39d1a1f440e> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>,<lambda_7604da78e8f33a312c72ae5a43f9b93b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>,<lambda_86be70dd2992469b5252a9192288f807> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>,<lambda_86be70dd2992469b5252a9192288f807> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>,<lambda_86d4d591f8ee155bf9fb023697a78601> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>,<lambda_980c07fc34548288b7608f6341665abc> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x20F40"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>,<lambda_f17ebabe33bc32be107ce6fff046b802> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x105B0"}, "atexit": {"offset": "0x4AAE0"}, "boost::algorithm::detail::find_format_all_impl2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_equal>,boost::algorithm::detail::const_formatF<boost::iterator_range<char const *> >,boost::iterator_range<std::_String_iterator<std::_String_val<std::_Simple_types<char> > > >,boost::iterator_range<char const *> >": {"offset": "0x16AD0"}, "boost::circular_buffer<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::destroy_content": {"offset": "0x301C0"}, "boost::circular_buffer<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back": {"offset": "0x30950"}, "boost::circular_buffer<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~circular_buffer<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x20620"}, "boost::circular_buffer<std::chrono::duration<__int64,std::ratio<1,1000> >,std::allocator<std::chrono::duration<__int64,std::ratio<1,1000> > > >::~circular_buffer<std::chrono::duration<__int64,std::ratio<1,1000> >,std::allocator<std::chrono::duration<__int64,std::ratio<1,1000> > > >": {"offset": "0x20680"}, "boost::exception::exception": {"offset": "0x1FD50"}, "boost::exception_detail::clone_base::clone_base": {"offset": "0x1FD40"}, "boost::exception_detail::clone_base::~clone_base": {"offset": "0x20F60"}, "boost::exception_detail::refcount_ptr<boost::exception_detail::error_info_container>::~refcount_ptr<boost::exception_detail::error_info_container>": {"offset": "0x20860"}, "boost::throw_exception<std::length_error>": {"offset": "0x17DA0"}, "boost::wrapexcept<std::length_error>::clone": {"offset": "0x2FF30"}, "boost::wrapexcept<std::length_error>::deleter::~deleter": {"offset": "0x20F70"}, "boost::wrapexcept<std::length_error>::rethrow": {"offset": "0x30DA0"}, "boost::wrapexcept<std::length_error>::wrapexcept<std::length_error>": {"offset": "0x1EFD0"}, "boost::wrapexcept<std::length_error>::~wrapexcept<std::length_error>": {"offset": "0x20B30"}, "capture_previous_context": {"offset": "0x4B438"}, "console::GetDefaultContext": {"offset": "0x278D0"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13A70"}, "console::Printf<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13CC0"}, "console::Printfv": {"offset": "0x28E10"}, "dllmain_crt_dispatch": {"offset": "0x4AFD0"}, "dllmain_crt_process_attach": {"offset": "0x4B020"}, "dllmain_crt_process_detach": {"offset": "0x4B138"}, "dllmain_dispatch": {"offset": "0x4B1BC"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xE960"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xB680"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x45C30"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x44FD0"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x30630"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x20580"}, "fmt::v8::detail::add_compare": {"offset": "0x453A0"}, "fmt::v8::detail::assert_fail": {"offset": "0x454E0"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x45530"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x45700"}, "fmt::v8::detail::bigint::square": {"offset": "0x46000"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x44FD0"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x3E90"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x462C0"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x16840"}, "fmt::v8::detail::compare": {"offset": "0x45660"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x16900"}, "fmt::v8::detail::count_digits": {"offset": "0xE740"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x41870"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x41980"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x3F40"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x45B00"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x437F0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x45DE0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x43820"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x445F0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x441C0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x45D60"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x41A30"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x41A30"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x16990"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x16A50"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x45A90"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x3F70"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x42EE0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x4240"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x42DC0"}, "fmt::v8::detail::format_float<double>": {"offset": "0x41380"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x43020"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x4320"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x16F60"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x43380"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x4440"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x4440"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x45A0"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x17650"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x4800"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x178D0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xF4D0"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x31340"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x43A30"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x43CB0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x43F40"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x440B0"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xB6E0"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0xB6E0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x48D0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xF310"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5ED0"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x193F0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x6ED0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x6A80"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x7310"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x44D40"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x44C20"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x69B0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1A150"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x1A670"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x1A220"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x1AAB0"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1AEF0"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1B030"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x7750"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1B170"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x7790"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1B260"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x7920"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x1B410"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x82E0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x7CF0"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x1BF40"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x1B920"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xB270"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0xB270"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x8A10"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x1C560"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x8E30"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x9000"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x91A0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x91A0"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x1C9F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x9330"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x9550"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x96D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0xAE60"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x9860"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x9A80"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x9D20"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x9F40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0xA0C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0xA2E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0xA500"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0xA680"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0xA8A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0xAA20"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0xAC40"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x1CB70"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x1CD10"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x1CEB0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x1D040"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x1D1E0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x1D380"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x1D520"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x1D6D0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x1D870"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xAFF0"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x1DA10"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x1DC00"}, "fmt::v8::format_error::format_error": {"offset": "0xB470"}, "fmt::v8::format_error::~format_error": {"offset": "0xB810"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x47F10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4D40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18580"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4B20"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18350"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x49F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18240"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4900"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18110"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4D40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18580"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18450"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4E70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x186C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x59D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x19170"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x5400"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x18C10"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x19DD0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<wchar_t>,wchar_t>": {"offset": "0x19EF0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x487B0"}, "fprintf": {"offset": "0xF590"}, "fwEvent<>::ConnectInternal": {"offset": "0x10BA0"}, "fwEvent<HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::ConnectInternal": {"offset": "0x10BA0"}, "fwEvent<bool *>::ConnectInternal": {"offset": "0x10BA0"}, "fwEvent<int &>::ConnectInternal": {"offset": "0x10BA0"}, "fwEvent<int,int>::ConnectInternal": {"offset": "0x10BA0"}, "fwEvent<std::basic_string_view<char,std::char_traits<char> > >::ConnectInternal": {"offset": "0x10BA0"}, "fwPlatformString::fwPlatformString": {"offset": "0x1FDB0"}, "fwPlatformString::~fwPlatformString": {"offset": "0xB830"}, "fwRefCountable::AddRef": {"offset": "0x498C0"}, "fwRefCountable::Release": {"offset": "0x498D0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x498B0"}, "getCharIndex": {"offset": "0x3C1F0"}, "getScrollDelta": {"offset": "0x3C320"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::Call": {"offset": "0x36210"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x35050"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<1,1,std::tuple<DevGuiPath const &> >": {"offset": "0x34E80"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x24B60"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x12E90"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::Call": {"offset": "0x24340"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x12990"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::Call": {"offset": "0x24750"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x12CE0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x10940"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x1E3C0"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0x115D0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0x11450"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0x11630"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x29BE0"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0x114D0"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0x11640"}, "internal::ConsoleVariableEntry<int>::ConsoleVariableEntry<int>": {"offset": "0x1DE70"}, "internal::ConsoleVariableEntry<int>::GetOfflineValue": {"offset": "0x279A0"}, "internal::ConsoleVariableEntry<int>::GetValue": {"offset": "0x27A90"}, "internal::ConsoleVariableEntry<int>::SaveOfflineValue": {"offset": "0x294B0"}, "internal::ConsoleVariableEntry<int>::SetRawValue": {"offset": "0x297B0"}, "internal::ConsoleVariableEntry<int>::SetValue": {"offset": "0x29DC0"}, "internal::ConsoleVariableEntry<int>::UpdateTrackingVariable": {"offset": "0x2A8E0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1E0F0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::GetOfflineValue": {"offset": "0x279C0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::GetValue": {"offset": "0x27AD0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SaveOfflineValue": {"offset": "0x294C0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SetRawValue": {"offset": "0x29980"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::SetValue": {"offset": "0x29EC0"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::UpdateTrackingVariable": {"offset": "0x2A900"}, "internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1FFC0"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x254A0"}, "internal::Constraints<int,void>::Compare": {"offset": "0x25090"}, "internal::MarkConsoleVarModified": {"offset": "0x28740"}, "internal::UnparseArgument<int>": {"offset": "0x13DE0"}, "isBoundary": {"offset": "0x3C990"}, "launch::IsSDKGuest": {"offset": "0x27F20"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xB4F0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xB590"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x2E00"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xC660"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xB5C0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xD7B0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xD870"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xDAE0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xDF80"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xB5D0"}, "rapidjson::internal::DigitGen": {"offset": "0xC910"}, "rapidjson::internal::Grisu2": {"offset": "0xD3C0"}, "rapidjson::internal::Prettify": {"offset": "0xD920"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x3890"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x3960"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xB590"}, "rapidjson::internal::WriteExponent": {"offset": "0xDEF0"}, "rapidjson::internal::u32toa": {"offset": "0xEA50"}, "rapidjson::internal::u64toa": {"offset": "0xECC0"}, "se::Object::~Object": {"offset": "0xB6E0"}, "se::Principal::~Principal": {"offset": "0xB6E0"}, "se::ScopedPrincipal::~ScopedPrincipal": {"offset": "0x20E10"}, "se::ScopedPrincipalReset::~ScopedPrincipalReset": {"offset": "0x20E90"}, "seCheckPrivilege": {"offset": "0x30DD0"}, "seGetCurrentContext": {"offset": "0x30EC0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >,void *> > >": {"offset": "0x356B0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB600"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xB600"}, "std::_Bt_state_t<char const *>::_Bt_state_t<char const *>": {"offset": "0x1E970"}, "std::_Bt_state_t<char const *>::~_Bt_state_t<char const *>": {"offset": "0x20240"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_char": {"offset": "0x2A960"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_char_to_array": {"offset": "0x2AA80"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_char_to_class": {"offset": "0x2AB40"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_class": {"offset": "0x2ABD0"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_equiv": {"offset": "0x2AC70"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_named_class": {"offset": "0x2AE10"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_range": {"offset": "0x2AEF0"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Add_rep": {"offset": "0x2B090"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Beg_expr": {"offset": "0x2BBC0"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Begin_capture_group": {"offset": "0x2BC00"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_Char_to_elts": {"offset": "0x2BD70"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_End_group": {"offset": "0x2E090"}, "std::_Builder<char const *,char,std::regex_traits<char> >::_New_node": {"offset": "0x2F000"}, "std::_Calculate_loop_simplicity": {"offset": "0x2BC90"}, "std::_Compare<char const *,char const *,std::regex_traits<char> >": {"offset": "0x14170"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x14370"}, "std::_Destroy_range<std::allocator<std::tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> > > >": {"offset": "0x38410"}, "std::_Facet_Register": {"offset": "0x4A3FC"}, "std::_Format_default<char const *,std::allocator<std::sub_match<char const *> >,char const *,std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x14720"}, "std::_Format_sed<char const *,std::allocator<std::sub_match<char const *> >,char const *,std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x14BC0"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x10580"}, "std::_Func_class<bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::~_Func_class<bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>": {"offset": "0x10580"}, "std::_Func_class<bool,bool *>::~_Func_class<bool,bool *>": {"offset": "0x10580"}, "std::_Func_class<bool,int &>::~_Func_class<bool,int &>": {"offset": "0x10580"}, "std::_Func_class<bool,int,int>::~_Func_class<bool,int,int>": {"offset": "0x10580"}, "std::_Func_class<bool,std::basic_string_view<char,std::char_traits<char> > >::~_Func_class<bool,std::basic_string_view<char,std::char_traits<char> > >": {"offset": "0x10580"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x10580"}, "std::_Func_class<void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x10580"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x10580"}, "std::_Func_class<void,int const &>::~_Func_class<void,int const &>": {"offset": "0x10580"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x10580"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x10580"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Copy": {"offset": "0xF860"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Do_call": {"offset": "0xF880"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Move": {"offset": "0xF860"}, "std::_Func_impl_no_alloc<<lambda_01d7adb225ea4ca3c20b20e3050c3dc5>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Target_type": {"offset": "0xF910"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x344B0"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFA70"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x34450"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_02837e64370d6fef96f08c65622c99b1>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x34530"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Copy": {"offset": "0x31A20"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Do_call": {"offset": "0x31A40"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Move": {"offset": "0x31A20"}, "std::_Func_impl_no_alloc<<lambda_058f5e79eaf16871b65263a27ab21600>,bool>::_Target_type": {"offset": "0x31A80"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Copy": {"offset": "0x319D0"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Do_call": {"offset": "0x319F0"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Move": {"offset": "0x319D0"}, "std::_Func_impl_no_alloc<<lambda_146cac2b710c5400711578427decc0a9>,bool,int &>::_Target_type": {"offset": "0x31A10"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38250"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x38260"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38250"}, "std::_Func_impl_no_alloc<<lambda_1beb9bdfa04be13fad17e0d46ac4731f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x38280"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0x11840"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0x11860"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0x11840"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0x11940"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x2C8A0"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x112B0"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_2c71f2734007fd255e94b39244217c06>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x2F6D0"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Copy": {"offset": "0x2C920"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Do_call": {"offset": "0x2D210"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Move": {"offset": "0x2C920"}, "std::_Func_impl_no_alloc<<lambda_3dc21e907c73372eaaba542348d0abbb>,float,unsigned __int64>::_Target_type": {"offset": "0x2F6E0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Copy": {"offset": "0x2C940"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Delete_this": {"offset": "0x117C0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Do_call": {"offset": "0x2D220"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Target_type": {"offset": "0x2F6F0"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Copy": {"offset": "0x37840"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Do_call": {"offset": "0x37860"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Move": {"offset": "0x37840"}, "std::_Func_impl_no_alloc<<lambda_45b58023f1436a1d28964bbd50777a6b>,bool,bool *>::_Target_type": {"offset": "0x37880"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x34540"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFA70"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xFA10"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_47435eb07b572c7d5654305a814d3474>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x345C0"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Copy": {"offset": "0x38170"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Do_call": {"offset": "0x38190"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Move": {"offset": "0x38170"}, "std::_Func_impl_no_alloc<<lambda_474b46e08daeb9eb84f42970798b5211>,bool,int,int>::_Target_type": {"offset": "0x381B0"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Copy": {"offset": "0x31800"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Do_call": {"offset": "0x31820"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Move": {"offset": "0x31800"}, "std::_Func_impl_no_alloc<<lambda_49a25c0d93e6a80cc42d97a34fd26fa6>,bool>::_Target_type": {"offset": "0x318D0"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Copy": {"offset": "0x343A0"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Do_call": {"offset": "0x343B0"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Move": {"offset": "0x343A0"}, "std::_Func_impl_no_alloc<<lambda_4f01932d3df21bafe14b0b705d1c2d2b>,void>::_Target_type": {"offset": "0x343C0"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x2C9A0"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x2D230"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_55cf421c54d17ec848e1c39d1a1f440e>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x2F700"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Copy": {"offset": "0x110D0"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Do_call": {"offset": "0x110F0"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Move": {"offset": "0x110D0"}, "std::_Func_impl_no_alloc<<lambda_5678a8d9272cb491f07103ca9f893192>,bool,bool *>::_Target_type": {"offset": "0x11110"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x2CA20"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x2D280"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x2CA20"}, "std::_Func_impl_no_alloc<<lambda_5b1c43db5646bdcc3820d1877854a8bd>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x2F710"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38310"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x38320"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38310"}, "std::_Func_impl_no_alloc<<lambda_69b0a3eca3295ecffd6fec90f979d38a>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x38340"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x2CA40"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x2CDF0"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x2D380"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x2CA40"}, "std::_Func_impl_no_alloc<<lambda_6ae025072f81c1346542a1fe58c624df>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x2F720"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x382D0"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x382E0"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x382D0"}, "std::_Func_impl_no_alloc<<lambda_7059d70280685a0cf9501fca0b7ae37f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x38300"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11660"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x116E0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11730"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Copy": {"offset": "0x2CA60"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Do_call": {"offset": "0x2D3F0"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Move": {"offset": "0x2CA60"}, "std::_Func_impl_no_alloc<<lambda_752471e60b3794527e2f2a89ab183199>,unsigned __int64>::_Target_type": {"offset": "0x2F730"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x2CA80"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x2D400"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x2F740"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x11230"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x112B0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x11300"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x34330"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x34340"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x34330"}, "std::_Func_impl_no_alloc<<lambda_8255e8b86698f5bb3d273a330684cd3f>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x34390"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Copy": {"offset": "0x2CB00"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Delete_this": {"offset": "0x117C0"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Do_call": {"offset": "0x2D450"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_86be70dd2992469b5252a9192288f807>,void>::_Target_type": {"offset": "0x2F750"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x343D0"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFA70"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x34450"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_86d4d591f8ee155bf9fb023697a78601>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x344A0"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Copy": {"offset": "0x2CB60"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Do_call": {"offset": "0x2D460"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Move": {"offset": "0x2CB60"}, "std::_Func_impl_no_alloc<<lambda_8a78866092edfd2701c6857c9314e0b3>,std::basic_string_view<char,std::char_traits<char> >,unsigned __int64>::_Target_type": {"offset": "0x2F760"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38350"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x38360"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38350"}, "std::_Func_impl_no_alloc<<lambda_8e0b500849b90ae58d7ce19d5bffc855>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x38380"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Copy": {"offset": "0x31960"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Do_call": {"offset": "0x31980"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Move": {"offset": "0x31960"}, "std::_Func_impl_no_alloc<<lambda_9611f8f72f4a264f9c162f6f34928f74>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Target_type": {"offset": "0x319C0"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Copy": {"offset": "0xF920"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Do_call": {"offset": "0xF940"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Move": {"offset": "0xF920"}, "std::_Func_impl_no_alloc<<lambda_9631c3f2f17e141d6d1654c1e8590520>,bool,HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &>::_Target_type": {"offset": "0xF980"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xF990"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xFA70"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xFA10"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_980c07fc34548288b7608f6341665abc>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xFA60"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Copy": {"offset": "0x11120"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Do_call": {"offset": "0x11140"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Move": {"offset": "0x11120"}, "std::_Func_impl_no_alloc<<lambda_a451aeb690c4e8966b13ce7e3d1eb1ac>,bool>::_Target_type": {"offset": "0x11180"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38390"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x383A0"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38390"}, "std::_Func_impl_no_alloc<<lambda_aa6c4726f09f85ea7971c224aaa8d65f>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x383C0"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x342C0"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x342D0"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x342C0"}, "std::_Func_impl_no_alloc<<lambda_bc85ef5bc811bb4046f609d5aec41b07>,void,DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x34320"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0x11740"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0x117C0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0x117A0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0x117B0"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x2CB80"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x2CDF0"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x2D380"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x2CB80"}, "std::_Func_impl_no_alloc<<lambda_c01895ae56fff4979366bae888bae92b>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x2F770"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Copy": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Do_call": {"offset": "0xFAE0"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Move": {"offset": "0xFAD0"}, "std::_Func_impl_no_alloc<<lambda_c30a8a1d6decbd9789e47aecee8410f4>,void>::_Target_type": {"offset": "0xFAF0"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38290"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x382A0"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38290"}, "std::_Func_impl_no_alloc<<lambda_c329d627e06173c8b49b00d005ab3c81>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x382C0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Copy": {"offset": "0x2CBA0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Do_call": {"offset": "0x2D480"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Move": {"offset": "0x2CBA0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Target_type": {"offset": "0x2F780"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Copy": {"offset": "0x37890"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Do_call": {"offset": "0x378B0"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Move": {"offset": "0x37890"}, "std::_Func_impl_no_alloc<<lambda_d873094bd7d5a12ed6e9c8d40dccbc03>,bool>::_Target_type": {"offset": "0x378D0"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Copy": {"offset": "0x38120"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Do_call": {"offset": "0x38140"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Move": {"offset": "0x38120"}, "std::_Func_impl_no_alloc<<lambda_d8f9aab4b1c5b7ce3a88f6304bb4ec2a>,bool,bool *>::_Target_type": {"offset": "0x38160"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Copy": {"offset": "0x31770"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Do_call": {"offset": "0x31790"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Move": {"offset": "0x31770"}, "std::_Func_impl_no_alloc<<lambda_d913eb8f00f3e708c28dd2cb67f4952b>,bool>::_Target_type": {"offset": "0x317F0"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Copy": {"offset": "0x381C0"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Do_call": {"offset": "0x381E0"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Move": {"offset": "0x381C0"}, "std::_Func_impl_no_alloc<<lambda_e1f861b58e95e717f9b6cd8e71718e4d>,bool>::_Target_type": {"offset": "0x38200"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Copy": {"offset": "0x11190"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Do_call": {"offset": "0x111A0"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Move": {"offset": "0x11190"}, "std::_Func_impl_no_alloc<<lambda_e27563d4ffc46b89091625888285883d>,bool,std::basic_string_view<char,std::char_traits<char> > >::_Target_type": {"offset": "0x111C0"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Copy": {"offset": "0x38210"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Delete_this": {"offset": "0xF850"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Do_call": {"offset": "0x38220"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Move": {"offset": "0x38210"}, "std::_Func_impl_no_alloc<<lambda_ea356c5db1e7869625346797e1f4d1f2>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Target_type": {"offset": "0x38240"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x2CBC0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x11310"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x112B0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF840"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xF6A0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x2F790"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x10290"}, "std::_Invoker_ret<void>::_Call<<lambda_c30a8a1d6decbd9789e47aecee8410f4> &>": {"offset": "0x100C0"}, "std::_Maklocstr<char>": {"offset": "0xF5E0"}, "std::_Maklocstr<wchar_t>": {"offset": "0x41760"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Do_class": {"offset": "0x2D560"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Do_if": {"offset": "0x2D930"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Do_rep": {"offset": "0x2DE70"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Do_rep0": {"offset": "0x2DBD0"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Match<std::allocator<std::sub_match<char const *> > >": {"offset": "0x150C0"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Match_pat": {"offset": "0x2E970"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Matcher<char const *,char,std::regex_traits<char>,char const *>": {"offset": "0x1EA40"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::_Skip": {"offset": "0x2F3E0"}, "std::_Matcher<char const *,char,std::regex_traits<char>,char const *>::~_Matcher<char const *,char,std::regex_traits<char>,char const *>": {"offset": "0x202A0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Alternative": {"offset": "0x2B460"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_AtomEscape": {"offset": "0x2BA40"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_CharacterClass": {"offset": "0x2BE60"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_CharacterClassEscape": {"offset": "0x2BF60"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_CharacterEscape": {"offset": "0x2C010"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_ClassAtom": {"offset": "0x2C530"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_ClassRanges": {"offset": "0x2C740"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_DecimalDigits": {"offset": "0x2CCB0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Disjunction": {"offset": "0x2CEC0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Do_assert_group": {"offset": "0x2D120"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Do_ex_class": {"offset": "0x2D6F0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Do_ffn": {"offset": "0x2D8E0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Do_noncapture_group": {"offset": "0x2DB80"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Error": {"offset": "0x2E150"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Expect": {"offset": "0x2E160"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_HexDigits": {"offset": "0x2E610"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Is_esc": {"offset": "0x2E930"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Next": {"offset": "0x2F090"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Quantifier": {"offset": "0x2F0E0"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Trans": {"offset": "0x2F960"}, "std::_Parser<char const *,char,std::regex_traits<char> >::_Wrapped_disjunction": {"offset": "0x2FC20"}, "std::_Parser<char const *,char,std::regex_traits<char> >::~_Parser<char const *,char,std::regex_traits<char> >": {"offset": "0x20320"}, "std::_Ref_count_base::_Decref": {"offset": "0x2CDA0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xF6A0"}, "std::_Ref_count_obj2<ConVar<bool> >::_Delete_this": {"offset": "0x111E0"}, "std::_Ref_count_obj2<ConVar<bool> >::_Destroy": {"offset": "0x111D0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0x111E0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0x11370"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Delete_this": {"offset": "0x111E0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Destroy": {"offset": "0x2CE00"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Delete_this": {"offset": "0x111E0"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Destroy": {"offset": "0x2CEB0"}, "std::_Regex_replace1<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char const *,std::regex_traits<char>,char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x15A60"}, "std::_Regex_search2<char const *,std::allocator<std::sub_match<char const *> >,char,std::regex_traits<char>,char const *>": {"offset": "0x15D40"}, "std::_Regex_traits<char>::lookup_classname<char const *>": {"offset": "0x17370"}, "std::_Regex_traits<char>::transform_primary<char *>": {"offset": "0x17DD0"}, "std::_Regex_traits<char>::transform_primary<char const *>": {"offset": "0x17DD0"}, "std::_Regex_traits<char>::translate": {"offset": "0x311C0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x2FDC0"}, "std::_Tgt_state_t<char const *>::_Tgt_state_t<char const *>": {"offset": "0x1EB90"}, "std::_Tgt_state_t<char const *>::~_Tgt_state_t<char const *>": {"offset": "0x203F0"}, "std::_Throw_bad_array_new_length": {"offset": "0xE320"}, "std::_Throw_bad_cast": {"offset": "0x2F7A0"}, "std::_Throw_tree_length_error": {"offset": "0xE340"}, "std::_Tidy_guard<std::_Builder<char const *,char,std::regex_traits<char> > >::~_Tidy_guard<std::_Builder<char const *,char,std::regex_traits<char> > >": {"offset": "0x204A0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x44F90"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x44F90"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3B30"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x3DB0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xB620"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x14400"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xB600"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x14660"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xE0C0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x3AD0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xE0C0"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x20510"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x35630"}, "std::_Uninitialized_move<std::tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> > *,std::allocator<std::tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> > > >": {"offset": "0x386F0"}, "std::_Uninitialized_value_construct_n<std::allocator<std::_Tgt_state_t<char const *>::_Grp_t> >": {"offset": "0x167F0"}, "std::_Vb_val<std::allocator<bool> >::~_Vb_val<std::allocator<bool> >": {"offset": "0x20520"}, "std::_Xlen_string": {"offset": "0xE360"}, "std::allocator<InputTarget *>::deallocate": {"offset": "0x10F20"}, "std::allocator<char>::allocate": {"offset": "0xE380"}, "std::allocator<char>::deallocate": {"offset": "0x30090"}, "std::allocator<std::_Loop_vals_t>::deallocate": {"offset": "0x30120"}, "std::allocator<std::_Tgt_state_t<char const *>::_Grp_t>::allocate": {"offset": "0x2FE50"}, "std::allocator<std::_Tgt_state_t<char const *>::_Grp_t>::deallocate": {"offset": "0x30120"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x2FEC0"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x377F0"}, "std::allocator<std::sub_match<char const *> >::deallocate": {"offset": "0x30170"}, "std::allocator<std::tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> > >::deallocate": {"offset": "0x3BAA0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x30090"}, "std::allocator<unsigned int>::allocate": {"offset": "0x2FDE0"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x300D0"}, "std::allocator<wchar_t>::allocate": {"offset": "0xE3E0"}, "std::bad_alloc::bad_alloc": {"offset": "0x4B834"}, "std::bad_alloc::~bad_alloc": {"offset": "0xB810"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xB400"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xB810"}, "std::bad_cast::bad_cast": {"offset": "0x1FD10"}, "std::bad_cast::~bad_cast": {"offset": "0xB810"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x105D0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x10610"}, "std::basic_regex<char,std::regex_traits<char> >::_Reset<char const *>": {"offset": "0x15F50"}, "std::basic_regex<char,std::regex_traits<char> >::_Tidy": {"offset": "0x2F7C0"}, "std::basic_regex<char,std::regex_traits<char> >::~basic_regex<char,std::regex_traits<char> >": {"offset": "0x205E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x3A10"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_for<<lambda_66f57f934f28d61049862f64df852ff0>,char const *>": {"offset": "0x152E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x15420"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x15590"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x476E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>": {"offset": "0x15720"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x158B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xE5D0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1EC50"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0x11970"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<char> > >,0>": {"offset": "0x170D0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::resize": {"offset": "0x30A80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xB6E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x142A0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xB740"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xE450"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x1FDB0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x49570"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x496D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xB740"}, "std::basic_string_view<char,std::char_traits<char> >::_Xran": {"offset": "0x3BBD0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::_Tidy": {"offset": "0x2F840"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x30750"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x308C0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x30F30"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x310B0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x31270"}, "std::collate<char>::_Getcat": {"offset": "0x2E1D0"}, "std::collate<char>::do_compare": {"offset": "0x30270"}, "std::collate<char>::do_hash": {"offset": "0x302C0"}, "std::collate<char>::do_transform": {"offset": "0x30300"}, "std::deque<char,std::allocator<char> >::_Emplace_back_internal<char const &>": {"offset": "0x145A0"}, "std::deque<char,std::allocator<char> >::_Growmap": {"offset": "0x2E430"}, "std::deque<char,std::allocator<char> >::_Insert_range<1,char const *,char const *>": {"offset": "0x14DF0"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<0>::~_Restore_old_size_guard<0>": {"offset": "0x203B0"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<1>::~_Restore_old_size_guard<1>": {"offset": "0x20380"}, "std::deque<char,std::allocator<char> >::_Xlen": {"offset": "0x2FD80"}, "std::deque<char,std::allocator<char> >::~deque<char,std::allocator<char> >": {"offset": "0x20780"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x10500"}, "std::exception::exception": {"offset": "0xB430"}, "std::exception::what": {"offset": "0xF4B0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x10580"}, "std::function<bool __cdecl(HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &)>::~function<bool __cdecl(HWND__ *,unsigned int,unsigned __int64,__int64,bool &,__int64 &)>": {"offset": "0x10580"}, "std::function<bool __cdecl(bool *)>::~function<bool __cdecl(bool *)>": {"offset": "0x10580"}, "std::function<bool __cdecl(int &)>::~function<bool __cdecl(int &)>": {"offset": "0x10580"}, "std::function<bool __cdecl(int,int)>::~function<bool __cdecl(int,int)>": {"offset": "0x10580"}, "std::function<bool __cdecl(std::basic_string_view<char,std::char_traits<char> >)>::~function<bool __cdecl(std::basic_string_view<char,std::char_traits<char> >)>": {"offset": "0x10580"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x10580"}, "std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)>::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)>": {"offset": "0x1ED80"}, "std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)>::~function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)>": {"offset": "0x10580"}, "std::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1ED80"}, "std::function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(DevGuiPath const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x10580"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x10580"}, "std::function<void __cdecl(int const &)>::~function<void __cdecl(int const &)>": {"offset": "0x10580"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x10580"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)>": {"offset": "0x1ED80"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x10580"}, "std::invoke<<lambda_c30a8a1d6decbd9789e47aecee8410f4> &>": {"offset": "0x10540"}, "std::length_error::length_error": {"offset": "0x1FEC0"}, "std::length_error::~length_error": {"offset": "0xB810"}, "std::list<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >,std::allocator<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> > > >::~list<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >,std::allocator<std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> > > >": {"offset": "0x356D0"}, "std::locale::~locale": {"offset": "0x20F90"}, "std::logic_error::logic_error": {"offset": "0x1FF10"}, "std::make_shared<ConVar<bool>,char const (&)[15],int,bool>": {"offset": "0x174C0"}, "std::match_results<char const *,std::allocator<std::sub_match<char const *> > >::_Resize": {"offset": "0x2F320"}, "std::match_results<char const *,std::allocator<std::sub_match<char const *> > >::~match_results<char const *,std::allocator<std::sub_match<char const *> > >": {"offset": "0x20850"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x45910"}, "std::numpunct<char>::do_falsename": {"offset": "0x45930"}, "std::numpunct<char>::do_grouping": {"offset": "0x459B0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x459F0"}, "std::numpunct<char>::do_truename": {"offset": "0x45A10"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x44DE0"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x451B0"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x45920"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x45970"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x459B0"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x45A00"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x45A50"}, "std::regex_match<std::char_traits<char>,std::allocator<char>,char,std::regex_traits<char> >": {"offset": "0x179C0"}, "std::regex_traits<char>::regex_traits<char>": {"offset": "0x1EE00"}, "std::regex_traits<char>::~regex_traits<char>": {"offset": "0x208A0"}, "std::rotate<std::_Deque_unchecked_iterator<std::_Deque_val<std::_Deque_simple_types<char> > > >": {"offset": "0x17B70"}, "std::runtime_error::runtime_error": {"offset": "0xB4B0"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x208D0"}, "std::shared_ptr<ConVar<bool> >::~shared_ptr<ConVar<bool> >": {"offset": "0x20900"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x20900"}, "std::shared_ptr<internal::ConsoleVariableEntry<int> >::~shared_ptr<internal::ConsoleVariableEntry<int> >": {"offset": "0x20900"}, "std::shared_ptr<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~shared_ptr<internal::ConsoleVariableEntry<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x20900"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x20900"}, "std::to_string": {"offset": "0x10F70"}, "std::tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> >::~tuple<std::shared_ptr<ConVar<bool> >,std::function<std::basic_string<char,std::char_traits<char>,std::allocator<char> > __cdecl(void)> >": {"offset": "0x387B0"}, "std::unique_lock<std::recursive_mutex>::~unique_lock<std::recursive_mutex>": {"offset": "0x20950"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x209A0"}, "std::unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >::~unique_ptr<DevGuiNode,std::default_delete<DevGuiNode> >": {"offset": "0x35740"}, "std::unique_ptr<FiveMConsoleBase,std::default_delete<FiveMConsoleBase> >::unique_ptr<FiveMConsoleBase,std::default_delete<FiveMConsoleBase> ><std::default_delete<FiveMConsoleBase>,0>": {"offset": "0x11A60"}, "std::unique_ptr<FiveMConsoleBase,std::default_delete<FiveMConsoleBase> >::~unique_ptr<FiveMConsoleBase,std::default_delete<FiveMConsoleBase> >": {"offset": "0x20960"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0x20990"}, "std::unique_ptr<fwEvent<bool *>::callback,std::default_delete<fwEvent<bool *>::callback> >::~unique_ptr<fwEvent<bool *>::callback,std::default_delete<fwEvent<bool *>::callback> >": {"offset": "0x20990"}, "std::unique_ptr<fwEvent<char const *,char const *>::callback,std::default_delete<fwEvent<char const *,char const *>::callback> >::~unique_ptr<fwEvent<char const *,char const *>::callback,std::default_delete<fwEvent<char const *,char const *>::callback> >": {"offset": "0x20990"}, "std::unique_ptr<fwEvent<int,int>::callback,std::default_delete<fwEvent<int,int>::callback> >::~unique_ptr<fwEvent<int,int>::callback,std::default_delete<fwEvent<int,int>::callback> >": {"offset": "0x20990"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x209D0"}, "std::unique_ptr<std::_Node_assert,std::default_delete<std::_Node_assert> >::~unique_ptr<std::_Node_assert,std::default_delete<std::_Node_assert> >": {"offset": "0x209D0"}, "std::unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >::~unique_ptr<unsigned char [0],std::default_delete<unsigned char [0]> >": {"offset": "0x32000"}, "std::use_facet<std::ctype<char> >": {"offset": "0x18020"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x449C0"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x44B30"}, "substringSizeX": {"offset": "0x3CAD0"}, "utf8::exception::exception": {"offset": "0x48900"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x47980"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x482B0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x48990"}, "utf8::invalid_code_point::what": {"offset": "0x49880"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xB810"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x48A00"}, "utf8::invalid_utf8::what": {"offset": "0x49890"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xB810"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x48A60"}, "utf8::not_enough_room::what": {"offset": "0x498A0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xB810"}, "utf8::unchecked::next<char const *>": {"offset": "0x3BAF0"}, "utf8::unchecked::next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x3BAF0"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x48090"}, "vva": {"offset": "0x49860"}, "xbr::GetGameBuild": {"offset": "0x33060"}, "xbr::GetReplaceExecutableInit": {"offset": "0x49980"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x49BA0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x2B00"}}}