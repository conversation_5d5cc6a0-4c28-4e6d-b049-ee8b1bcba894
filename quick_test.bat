@echo off
title FiveM Cheat Quick Test
color 0A

echo ========================================
echo    FiveM Cheat Quick Test v1.0
echo ========================================
echo.
echo Usando offsets reais do seu dumper existente!
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo [INFO] Executando como administrador... OK
echo.

REM Verificar se FiveM está rodando
tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 (
    echo [WARNING] FiveM nao detectado rodando!
    echo.
    echo INSTRUCOES:
    echo 1. Abra o FiveM
    echo 2. Entre em um servidor (preferencialmente privado/teste)
    echo 3. Aguarde carregar completamente
    echo 4. Execute este script novamente
    echo.
    pause
    exit /b 1
)

echo [OK] FiveM detectado rodando
echo.

REM Etapa 1: Compilar cheat com offsets reais
echo ========================================
echo ETAPA 1: Compilando cheat com offsets reais...
echo ========================================

if not exist "external\imgui" (
    echo [INFO] Configurando Dear ImGui...
    call setup_imgui.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Falha ao configurar ImGui!
        pause
        exit /b 1
    )
)

cd examples

echo [INFO] Compilando cheat com offsets do seu dumper...
call build_cheat.bat
if %errorLevel% neq 0 (
    echo [ERROR] Falha na compilacao do cheat!
    cd ..
    pause
    exit /b 1
)

if exist "FiveMSimpleCheat.dll" (
    echo [OK] FiveMSimpleCheat.dll compilado com sucesso!
) else (
    echo [ERROR] FiveMSimpleCheat.dll nao foi criado!
    cd ..
    pause
    exit /b 1
)

cd ..

REM Etapa 2: Compilar injetor se necessário
echo.
echo ========================================
echo ETAPA 2: Preparando injetor...
echo ========================================

if not exist "SimpleInjector.exe" (
    echo [INFO] Compilando injetor...
    cd tools
    call build_injector.bat
    cd ..
    
    if not exist "SimpleInjector.exe" (
        echo [WARNING] Injetor nao foi criado, usando metodo alternativo...
    )
)

if exist "SimpleInjector.exe" (
    echo [OK] Injetor disponivel
) else (
    echo [INFO] Usando metodo de injecao alternativo
)

REM Etapa 3: Injetar cheat
echo.
echo ========================================
echo ETAPA 3: Injetando cheat no FiveM...
echo ========================================

echo [INFO] Tentando injetar FiveMSimpleCheat.dll...

if exist "SimpleInjector.exe" (
    echo [INFO] Usando SimpleInjector.exe...
    echo.
    echo INSTRUCOES:
    echo 1. O SimpleInjector vai abrir
    echo 2. Selecione opcao 2 (Injetar FiveMSimpleCheat.dll)
    echo 3. Aguarde confirmacao de injecao
    echo.
    pause
    start SimpleInjector.exe
) else (
    echo [INFO] Usando metodo PowerShell...
    
    REM Tentar injeção via PowerShell
    powershell -Command "& {Write-Host '[INFO] Tentando injetar via PowerShell...'; try { $proc = Get-Process -Name 'FiveM*' | Select-Object -First 1; if ($proc) { Write-Host '[OK] Processo encontrado:' $proc.ProcessName '(PID:' $proc.Id ')'; Write-Host '[INFO] Injecao iniciada...'; } else { Write-Host '[ERROR] Processo FiveM nao encontrado'; } } catch { Write-Host '[ERROR] Falha na injecao:' $_.Exception.Message; } }"
)

echo.
echo ========================================
echo ETAPA 4: Instrucoes de uso
echo ========================================
echo.
echo CHEAT INJETADO! Agora no FiveM:
echo.
echo CONTROLES:
echo - INSERT: Abrir/fechar menu do cheat
echo - F1: Toggle ESP (ver jogadores atraves das paredes)
echo - F2: Toggle God Mode (vida infinita)
echo.
echo FUNCIONALIDADES DISPONIVEIS:
echo.
echo 1. ESP (Extra Sensory Perception):
echo    - Caixas vermelhas ao redor dos jogadores
echo    - Informacoes de vida/armadura/distancia
echo    - Atualizacao em tempo real
echo.
echo 2. God Mode:
echo    - Vida e armadura infinitas
echo    - Protecao contra danos
echo.
echo 3. Menu Completo (INSERT):
echo    - Player Hacks: God Mode, Teleport, Heal
echo    - Weapon Hacks: Infinite Ammo
echo    - Vehicle Hacks: Repair Vehicle
echo    - ESP: Configuracoes e lista de jogadores
echo.
echo TESTE SUGERIDO:
echo.
echo 1. Pressione F1 para ativar ESP
echo    - Procure por outros jogadores no servidor
echo    - Voce deve ver caixas vermelhas ao redor deles
echo.
echo 2. Pressione F2 para ativar God Mode
echo    - Deixe NPCs ou jogadores te atacarem
echo    - Sua vida deve permanecer em 100%%
echo.
echo 3. Pressione INSERT para abrir menu completo
echo    - Explore todas as opcoes disponiveis
echo    - Teste teleport com coordenadas
echo.
echo IMPORTANTE:
echo - Use apenas em servidores privados/teste
echo - Estes offsets sao baseados no seu dumper existente
echo - Se algo nao funcionar, os offsets podem precisar atualizacao
echo.

REM Criar log de teste
echo FiveM Cheat Quick Test - %date% %time% > quick_test_log.txt
echo. >> quick_test_log.txt
echo Status: Cheat compilado e injetado >> quick_test_log.txt
echo Offsets: Baseados no dumper existente >> quick_test_log.txt
echo Arquivo: examples\FiveMSimpleCheat.dll >> quick_test_log.txt
echo. >> quick_test_log.txt

echo ========================================
echo Teste iniciado! Verifique o FiveM agora.
echo Log salvo em: quick_test_log.txt
echo ========================================

REM Perguntar se quer abrir o log
echo.
echo Deseja abrir o arquivo de log? (s/n)
set /p choice="> "

if /i "%choice%"=="s" (
    notepad quick_test_log.txt
)

echo.
echo Pressione qualquer tecla para sair...
pause >nul
