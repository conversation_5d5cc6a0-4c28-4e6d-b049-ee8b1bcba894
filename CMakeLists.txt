cmake_minimum_required(VERSION 3.16)
project(FiveMOffsetDumper)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include directories
include_directories(src)
include_directories(external/imgui)
include_directories(external/imgui/backends)

# ImGui source files
set(IMGUI_SOURCES
    external/imgui/imgui.cpp
    external/imgui/imgui_demo.cpp
    external/imgui/imgui_draw.cpp
    external/imgui/imgui_tables.cpp
    external/imgui/imgui_widgets.cpp
    external/imgui/backends/imgui_impl_win32.cpp
    external/imgui/backends/imgui_impl_dx11.cpp
)

# Main source files
set(SOURCES
    src/dllmain.cpp
    src/OffsetDumper.cpp
    src/PatternScanner.cpp
    src/GUI.cpp
    src/MemoryUtils.cpp
    src/OffsetDatabase.cpp
    ${IMGUI_SOURCES}
)

# Create the DLL
add_library(FiveMOffsetDumper SHARED ${SOURCES})

# Link libraries
target_link_libraries(FiveMOffsetDumper
    d3d11
    dxgi
    user32
    kernel32
    gdi32
    winspool
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
)

# Set DLL properties
set_target_properties(FiveMOffsetDumper PROPERTIES
    OUTPUT_NAME "FiveMOffsetDumper"
    SUFFIX ".dll"
)

# Compiler flags
if(MSVC)
    target_compile_options(FiveMOffsetDumper PRIVATE /W3)
else()
    target_compile_options(FiveMOffsetDumper PRIVATE -Wall -Wextra)
endif()
