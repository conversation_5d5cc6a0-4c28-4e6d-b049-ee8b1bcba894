{"handling-loader-five.dll": {"CHandlingData::CHandlingData": {"offset": "0x18730"}, "CfxState::CfxState": {"offset": "0x2F350"}, "Component::As": {"offset": "0xE790"}, "Component::IsA": {"offset": "0xE850"}, "Component::SetCommandLine": {"offset": "0xA6B0"}, "Component::SetUserData": {"offset": "0xE860"}, "ComponentInstance::DoGameLoad": {"offset": "0xE830"}, "ComponentInstance::Initialize": {"offset": "0xE840"}, "ComponentInstance::Shutdown": {"offset": "0xE860"}, "CoreGetComponentRegistry": {"offset": "0xB8E0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0xB970"}, "CreateComponent": {"offset": "0xE870"}, "DllMain": {"offset": "0x34D08"}, "DoNtRaiseException": {"offset": "0x319C0"}, "FatalErrorNoExceptRealV": {"offset": "0xBD80"}, "FatalErrorRealV": {"offset": "0xBDB0"}, "FindFreeHandlingData": {"offset": "0x10650"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x24E0"}, "GetAbsoluteCitPath": {"offset": "0x2FD10"}, "GetHandlingIndexByHash": {"offset": "0x10760"}, "GetVehicleHandling<float>": {"offset": "0x14310"}, "GetVehicleHandling<int>": {"offset": "0x13CB0"}, "GlobalErrorHandler": {"offset": "0xBFF0"}, "HookFunction::Run": {"offset": "0xEB90"}, "HookFunctionBase::Register": {"offset": "0x32820"}, "HookFunctionBase::RunAll": {"offset": "0x32840"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x2EEB0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x2F410"}, "InitFunction::Run": {"offset": "0xE8A0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x31760"}, "InitFunctionBase::Register": {"offset": "0x31B30"}, "InitFunctionBase::RunAll": {"offset": "0x31B80"}, "LoadHandlingFile": {"offset": "0x108D0"}, "LoadHandlingFileWrap": {"offset": "0x10EE0"}, "LoadHandlingFileWrap2": {"offset": "0x10E20"}, "MakeRelativeCitPath": {"offset": "0xC690"}, "ModifyHandlingForVehicles<std::unordered_set<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<unsigned int> > >": {"offset": "0xED50"}, "RaiseDebugException": {"offset": "0x31AA0"}, "ScopedError::~ScopedError": {"offset": "0xA8A0"}, "SetHandlingDataInternal": {"offset": "0x258D0"}, "SysError": {"offset": "0xCC10"}, "ToNarrow": {"offset": "0x31BB0"}, "ToWide": {"offset": "0x31CA0"}, "TraceRealV": {"offset": "0x31FB0"}, "UnloadHandlingFile": {"offset": "0x10FA0"}, "UnloadHandlingFileEntry": {"offset": "0x11880"}, "Win32TrapAndJump64": {"offset": "0x336B0"}, "_DllMainCRTStartup": {"offset": "0x346BC"}, "_Init_thread_abort": {"offset": "0x339E0"}, "_Init_thread_footer": {"offset": "0x33A10"}, "_Init_thread_header": {"offset": "0x33A70"}, "_Init_thread_notify": {"offset": "0x33AD8"}, "_Init_thread_wait": {"offset": "0x33B1C"}, "_RTC_Initialize": {"offset": "0x34D74"}, "_RTC_Terminate": {"offset": "0x34DB0"}, "__ArrayUnwind": {"offset": "0x34338"}, "__GSHandlerCheck": {"offset": "0x340FC"}, "__GSHandlerCheckCommon": {"offset": "0x3411C"}, "__GSHandlerCheck_EH": {"offset": "0x34178"}, "__GSHandlerCheck_SEH": {"offset": "0x34878"}, "__crt_debugger_hook": {"offset": "0x34AAC"}, "__dyn_tls_init": {"offset": "0x33F44"}, "__dyn_tls_on_demand_init": {"offset": "0x33FAC"}, "__empty_global_delete": {"offset": "0xA6B0"}, "__isa_available_init": {"offset": "0x34900"}, "__local_stdio_printf_options": {"offset": "0xE670"}, "__local_stdio_scanf_options": {"offset": "0x34D48"}, "__raise_securityfailure": {"offset": "0x346FC"}, "__report_gsfailure": {"offset": "0x34730"}, "__scrt_acquire_startup_lock": {"offset": "0x33BC4"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x33C00"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x33C34"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x33C4C"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x33C74"}, "__scrt_dllmain_exception_filter": {"offset": "0x33C8C"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x33CEC"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x33D1C"}, "__scrt_fastfail": {"offset": "0x34AB4"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x34D6C"}, "__scrt_initialize_crt": {"offset": "0x33D30"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x34D50"}, "__scrt_initialize_onexit_tables": {"offset": "0x33D7C"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x338E8"}, "__scrt_initialize_type_info": {"offset": "0x34D2C"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x33E08"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x35FCC"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x34C50"}, "__scrt_release_startup_lock": {"offset": "0x33EA0"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE860"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE860"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE860"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE860"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE860"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE790"}, "__scrt_throw_std_bad_alloc": {"offset": "0x34C20"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD410"}, "__scrt_uninitialize_crt": {"offset": "0x33EC4"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x339B8"}, "__scrt_uninitialize_type_info": {"offset": "0x34D3C"}, "__security_check_cookie": {"offset": "0x34210"}, "__security_init_cookie": {"offset": "0x34C5C"}, "__std_find_trivial_1": {"offset": "0x336D0"}, "__std_find_trivial_8": {"offset": "0x337A0"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x33880"}, "_get_startup_argv_mode": {"offset": "0x34C48"}, "_guard_check_icall_nop": {"offset": "0xA6B0"}, "_guard_dispatch_icall_nop": {"offset": "0x34EF0"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x34F10"}, "_onexit": {"offset": "0x33EF0"}, "_wwassert": {"offset": "0x30090"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x3603C"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x3609B"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x360B2"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x360CB"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x360DF"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_33''": {"offset": "0x1390"}, "`dynamic initializer for '_init_instance_34''": {"offset": "0x13C0"}, "`dynamic initializer for '_init_instance_35''": {"offset": "0x13F0"}, "`dynamic initializer for '_processHandlingDataEntry''": {"offset": "0x1420"}, "`dynamic initializer for 'g_handlingByFile''": {"offset": "0x1460"}, "`dynamic initializer for 'g_handlingStack''": {"offset": "0x14E0"}, "`dynamic initializer for 'g_parseFileIntoStructure''": {"offset": "0x1560"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1760"}, "`dynamic initializer for 'initFunction''": {"offset": "0x17A0"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x1A20"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x19D0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x1210"}, "`dynamic initializer for 'xbr::virt::Base<15,2802,0,6>::ms_initFunction''": {"offset": "0x1350"}, "`dynamic initializer for 'xbr::virt::Base<18,2802,0,6>::ms_initFunction''": {"offset": "0x1250"}, "`dynamic initializer for 'xbr::virt::Base<23,2802,0,6>::ms_initFunction''": {"offset": "0x1290"}, "`dynamic initializer for 'xbr::virt::Base<26,2802,0,6>::ms_initFunction''": {"offset": "0x12D0"}, "`dynamic initializer for 'xbr::virt::Base<29,2802,0,6>::ms_initFunction''": {"offset": "0x1310"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::RegIDMap": {"offset": "0x19400"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::~RegIDMap": {"offset": "0x19D10"}, "atArray<CBaseSubHandlingData *>::~atArray<CBaseSubHandlingData *>": {"offset": "0xFF20"}, "atArray<CHandlingData *>::~atArray<CHandlingData *>": {"offset": "0xFF20"}, "atexit": {"offset": "0x33F2C"}, "capture_previous_context": {"offset": "0x34804"}, "console::Printf<unsigned short,char const *>": {"offset": "0xEE80"}, "dllmain_crt_dispatch": {"offset": "0x3439C"}, "dllmain_crt_process_attach": {"offset": "0x343EC"}, "dllmain_crt_process_detach": {"offset": "0x34504"}, "dllmain_dispatch": {"offset": "0x34588"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xDA50"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA770"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x2E6D0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x2DD10"}, "fmt::v8::detail::add_compare": {"offset": "0x2DEE0"}, "fmt::v8::detail::assert_fail": {"offset": "0x2E020"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x2E070"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x2E240"}, "fmt::v8::detail::bigint::square": {"offset": "0x2EAA0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x2DD10"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2F80"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x2ED60"}, "fmt::v8::detail::compare": {"offset": "0x2E1A0"}, "fmt::v8::detail::count_digits": {"offset": "0xD830"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x2A860"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x3030"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x2E5A0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x2C730"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x2E880"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x2C760"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x2D420"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x2CFF0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x2E800"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x2A970"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x2A970"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x2E530"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x3060"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x2BE20"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3330"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x2BD00"}, "fmt::v8::detail::format_float<double>": {"offset": "0x2A3B0"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x2BF60"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x3410"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x2C2C0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3530"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x3690"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x38F0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE5C0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x2C970"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x2CBF0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x2CE80"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA7D0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x39C0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE400"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4FC0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5FC0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5B70"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x6400"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x2DA80"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x2D960"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5AA0"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6840"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6880"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x6A10"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x73D0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6DE0"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA360"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x7B00"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7F20"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x80F0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x8290"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x8290"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8420"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x8640"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x87C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9F50"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8950"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8B70"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8E10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x9030"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x91B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x93D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x95F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9770"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9990"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x9B10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9D30"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xA0E0"}, "fmt::v8::format_error::format_error": {"offset": "0xA560"}, "fmt::v8::format_error::~format_error": {"offset": "0xA900"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x30D90"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3E30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3AE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x39F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3E30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3D00"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3F60"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4AC0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x44F0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x31630"}, "fprintf": {"offset": "0xE680"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA920"}, "fwRefCountable::AddRef": {"offset": "0x327E0"}, "fwRefCountable::Release": {"offset": "0x327F0"}, "fwRefCountable::~fwRefCountable": {"offset": "0x327D0"}, "getFloatField": {"offset": "0x27CB0"}, "getIntField": {"offset": "0x28280"}, "hook::AllocateFunctionStub": {"offset": "0x328A0"}, "hook::TransformPattern": {"offset": "0x33500"}, "hook::details::StubInitFunction::Run": {"offset": "0xE8B0"}, "hook::get_pattern<char,36>": {"offset": "0xFAB0"}, "hook::get_pattern<char,44>": {"offset": "0xFAB0"}, "hook::get_pattern<void,29>": {"offset": "0xFAB0"}, "hook::get_pattern<void,33>": {"offset": "0xFAB0"}, "hook::get_pattern<void,35>": {"offset": "0xFAB0"}, "hook::get_pattern<void,36>": {"offset": "0xFAB0"}, "hook::get_pattern<void,49>": {"offset": "0xFAB0"}, "hook::pattern::EnsureMatches": {"offset": "0x32EA0"}, "hook::pattern::Initialize": {"offset": "0x33200"}, "hook::pattern::~pattern": {"offset": "0x10090"}, "jitasm::Backend::Assemble": {"offset": "0x1C410"}, "jitasm::Backend::Encode": {"offset": "0x1F200"}, "jitasm::Backend::EncodeALU": {"offset": "0x1F670"}, "jitasm::Backend::EncodeImm": {"offset": "0x1F7E0"}, "jitasm::Backend::EncodeJMP": {"offset": "0x1F990"}, "jitasm::Backend::EncodeModRM": {"offset": "0x1FBE0"}, "jitasm::Backend::EncodeOpcode": {"offset": "0x20190"}, "jitasm::Backend::EncodePrefixes": {"offset": "0x20240"}, "jitasm::Backend::GetWRXB": {"offset": "0x20D90"}, "jitasm::Backend::db": {"offset": "0x279E0"}, "jitasm::Backend::dd": {"offset": "0x27A50"}, "jitasm::Frontend::AppendInstr": {"offset": "0x1C2E0"}, "jitasm::Frontend::Assemble": {"offset": "0x1C810"}, "jitasm::Frontend::Label::~Label": {"offset": "0xA7D0"}, "jitasm::Frontend::ResolveJump": {"offset": "0x23A80"}, "jitasm::Frontend::add": {"offset": "0x274D0"}, "jitasm::Frontend::mov": {"offset": "0x28880"}, "jitasm::Frontend::movaps": {"offset": "0x28A40"}, "jitasm::Frontend::pop": {"offset": "0x28B80"}, "jitasm::Frontend::push": {"offset": "0x28C80"}, "jitasm::Frontend::pxor": {"offset": "0x28F00"}, "jitasm::Frontend::ret": {"offset": "0x29240"}, "jitasm::Frontend::sub": {"offset": "0x29E10"}, "jitasm::Frontend::vxorps": {"offset": "0x29F40"}, "jitasm::Frontend::xorps": {"offset": "0x2A040"}, "jitasm::Frontend::~Frontend": {"offset": "0x199A0"}, "jitasm::Instr::Instr": {"offset": "0x18F30"}, "jitasm::compiler::BasicBlock::BasicBlock": {"offset": "0x186A0"}, "jitasm::compiler::BasicBlock::IsDominated": {"offset": "0x20F50"}, "jitasm::compiler::Compile": {"offset": "0x1EAE0"}, "jitasm::compiler::ControlFlowGraph::Build": {"offset": "0x1D130"}, "jitasm::compiler::ControlFlowGraph::DetectLoops": {"offset": "0x1EEC0"}, "jitasm::compiler::ControlFlowGraph::MakeDepthFirstBlocks": {"offset": "0x231E0"}, "jitasm::compiler::ControlFlowGraph::clear": {"offset": "0x278B0"}, "jitasm::compiler::ControlFlowGraph::get_block": {"offset": "0x28560"}, "jitasm::compiler::ControlFlowGraph::initialize": {"offset": "0x28660"}, "jitasm::compiler::ControlFlowGraph::~ControlFlowGraph": {"offset": "0x19830"}, "jitasm::compiler::DominatorFinder::Compress": {"offset": "0x1EE50"}, "jitasm::compiler::DominatorFinder::~DominatorFinder": {"offset": "0x198A0"}, "jitasm::compiler::GenerateEpilog": {"offset": "0x20550"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::GpRegOperator>": {"offset": "0x12CE0"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::MmxRegOperator>": {"offset": "0x132D0"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::XmmRegOperator>": {"offset": "0x13730"}, "jitasm::compiler::GenerateProlog": {"offset": "0x208A0"}, "jitasm::compiler::GetRegFamily": {"offset": "0x20D00"}, "jitasm::compiler::Lifetime::AddUsePoint": {"offset": "0x1BE00"}, "jitasm::compiler::Lifetime::AssignRegister": {"offset": "0x1CBC0"}, "jitasm::compiler::Lifetime::BuildIntervals": {"offset": "0x1DBA0"}, "jitasm::compiler::Lifetime::Interval::Interval": {"offset": "0x19070"}, "jitasm::compiler::Lifetime::Interval::~Interval": {"offset": "0x19A20"}, "jitasm::compiler::Lifetime::LessAssignOrder::num_of_assignable": {"offset": "0x28B20"}, "jitasm::compiler::Lifetime::Lifetime": {"offset": "0x19140"}, "jitasm::compiler::Lifetime::SpillIdentification": {"offset": "0x25E80"}, "jitasm::compiler::Lifetime::~Lifetime": {"offset": "0x19BB0"}, "jitasm::compiler::LinearScanRegisterAlloc": {"offset": "0x20F70"}, "jitasm::compiler::LiveVariableAnalysis": {"offset": "0x213D0"}, "jitasm::compiler::Operations::Operations": {"offset": "0x19310"}, "jitasm::compiler::PrepareCompile": {"offset": "0x232D0"}, "jitasm::compiler::RewriteInstructions": {"offset": "0x24520"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::GpRegOperator> >": {"offset": "0x12770"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::MmxRegOperator> >": {"offset": "0x12A60"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::XmmRegOperator> >": {"offset": "0x12BA0"}, "jitasm::compiler::VariableManager::AllocSpillSlots": {"offset": "0x1C010"}, "jitasm::compiler::VariableManager::UpdateVarSize": {"offset": "0x267E0"}, "jitasm::compiler::VariableManager::~VariableManager": {"offset": "0x19D70"}, "jitasm::detail::CodeBuffer::Reset": {"offset": "0x239B0"}, "jitasm::detail::CodeBuffer::~CodeBuffer": {"offset": "0x197F0"}, "jitasm::detail::ImmXor8": {"offset": "0x20E60"}, "jitasm::detail::Opd::GetDisp": {"offset": "0x20C80"}, "jitasm::detail::Opd::GetImm": {"offset": "0x20CC0"}, "jitasm::detail::ScopedLock<jitasm::detail::SpinLock>::~ScopedLock<jitasm::detail::SpinLock>": {"offset": "0x19450"}, "launch::IsSDKGuest": {"offset": "0x30010"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA5E0"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA680"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1EF0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB750"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA6B0"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC8A0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC960"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xCBD0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xD070"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA6C0"}, "rapidjson::internal::DigitGen": {"offset": "0xBA00"}, "rapidjson::internal::Grisu2": {"offset": "0xC4B0"}, "rapidjson::internal::Prettify": {"offset": "0xCA10"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2980"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2A50"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA680"}, "rapidjson::internal::WriteExponent": {"offset": "0xCFE0"}, "rapidjson::internal::u32toa": {"offset": "0xDB40"}, "rapidjson::internal::u64toa": {"offset": "0xDDB0"}, "setFloatField": {"offset": "0x29310"}, "setIntField": {"offset": "0x29AF0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<int,void *> > >": {"offset": "0xFC90"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0xFCD0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *> > >": {"offset": "0xFCB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<unsigned int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<unsigned int,void *> > >": {"offset": "0xFC90"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0xFCB0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA6F0"}, "std::_Default_allocator_traits<std::allocator<jitasm::compiler::Lifetime::Interval> >::construct<jitasm::compiler::Lifetime::Interval,jitasm::compiler::Lifetime::Interval const &>": {"offset": "0x17DB0"}, "std::_Destroy_range<std::allocator<jitasm::compiler::Lifetime::Interval> >": {"offset": "0x14980"}, "std::_Facet_Register": {"offset": "0x33898"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Copy": {"offset": "0xEAF0"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Do_call": {"offset": "0xEB10"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Move": {"offset": "0xEAF0"}, "std::_Func_impl_no_alloc<<lambda_39ceaaec5b17e39b0a17f9e85aee4edc>,void,atArray<CVehicleModelInfo> *>::_Target_type": {"offset": "0xEB80"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Copy": {"offset": "0xE8D0"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Do_call": {"offset": "0xE8F0"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Move": {"offset": "0xE8D0"}, "std::_Func_impl_no_alloc<<lambda_41dd09a7777bf353403f1b189cb0ddf4>,void,CVehicleModelInfo *>::_Target_type": {"offset": "0xEAC0"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Copy": {"offset": "0x12400"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x12410"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Move": {"offset": "0x12400"}, "std::_Func_impl_no_alloc<<lambda_7591b86956eb86f480264e0754e9f998>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x12420"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Copy": {"offset": "0x12460"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x12470"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Move": {"offset": "0x12460"}, "std::_Func_impl_no_alloc<<lambda_9a543d9103eddbc4c3de63e2a5a63dcf>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x124A0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Copy": {"offset": "0x123D0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x123E0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Move": {"offset": "0x123D0"}, "std::_Func_impl_no_alloc<<lambda_9e47c704977b4f95aa2cf31e647e0757>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x123F0"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Copy": {"offset": "0x12370"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x12380"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Move": {"offset": "0x12370"}, "std::_Func_impl_no_alloc<<lambda_bed5030bd862ae1e09a42aac5023da12>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x12390"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Copy": {"offset": "0x123A0"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x123B0"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Move": {"offset": "0x123A0"}, "std::_Func_impl_no_alloc<<lambda_de4e4389c0b5dee3c1d97e9d1eec8e7e>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x123C0"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Copy": {"offset": "0x2A120"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x2A130"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Move": {"offset": "0x2A120"}, "std::_Func_impl_no_alloc<<lambda_df68edec91a4f3a4a5d9063f91a98e46>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x2A140"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Copy": {"offset": "0x12430"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xEAE0"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x12440"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Get": {"offset": "0xEAD0"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Move": {"offset": "0x12430"}, "std::_Func_impl_no_alloc<<lambda_e521c746ea659e0fa7ebd705e9721395>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x12450"}, "std::_Guess_median_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x154D0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,1> >::_Forced_rehash": {"offset": "0x11B90"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,1> >::_Unchecked_erase": {"offset": "0x12080"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,1> >::emplace<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >": {"offset": "0xF790"}, "std::_Hash<std::_Umap_traits<unsigned int,std::list<int,std::allocator<int> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > >,0> >::_Forced_rehash": {"offset": "0x119D0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::list<int,std::allocator<int> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > >,0> >::_Rehash_for_1": {"offset": "0x11FD0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::list<int,std::allocator<int> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > >,0> >::_Try_emplace<unsigned int const &>": {"offset": "0xF270"}, "std::_Hash<std::_Umap_traits<unsigned int,std::list<int,std::allocator<int> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > >,0> >::_Try_emplace<unsigned int>": {"offset": "0xF270"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::_Forced_rehash": {"offset": "0x11E10"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::emplace<unsigned int>": {"offset": "0xF4D0"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::~_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >": {"offset": "0xFCF0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x11890"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >": {"offset": "0xFDA0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >::_Assign_grow": {"offset": "0x11890"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >": {"offset": "0xFDA0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > > > > >::_Assign_grow": {"offset": "0x11890"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > > > > >": {"offset": "0xFDA0"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x10060"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0xF160"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *>::_Freenode<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0xF200"}, "std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *> > >": {"offset": "0xF0C0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0xFE90"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,void *> > >": {"offset": "0xFE00"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<unsigned int,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<unsigned int,void *> > >": {"offset": "0xFC90"}, "std::_Make_heap_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x15940"}, "std::_Maklocstr<char>": {"offset": "0xE6D0"}, "std::_Med3_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x15AC0"}, "std::_Med3_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x15BF0"}, "std::_Med3_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x15B60"}, "std::_Move_unchecked<jitasm::Instr *,jitasm::Instr *>": {"offset": "0x15CC0"}, "std::_Partition_by_median_guess_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x15CF0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x162B0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x16020"}, "std::_Pop_heap_hole_by_index<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64>,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x16610"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x16840"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x16740"}, "std::_Sort_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x17220"}, "std::_Sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x17000"}, "std::_Sort_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x179D0"}, "std::_Sort_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x176E0"}, "std::_Throw_bad_array_new_length": {"offset": "0xD410"}, "std::_Throw_tree_length_error": {"offset": "0xD430"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x2DCD0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2C20"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2EA0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA710"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x15470"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Insert_node": {"offset": "0xD1B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x2BC0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xD1B0"}, "std::_Uninitialized_move<jitasm::Instr *,std::allocator<jitasm::Instr> >": {"offset": "0x17D10"}, "std::_Xlen_string": {"offset": "0xD450"}, "std::allocator<char>::allocate": {"offset": "0xD470"}, "std::allocator<char>::deallocate": {"offset": "0x32450"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x27670"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x27B20"}, "std::allocator<int>::allocate": {"offset": "0x27600"}, "std::allocator<int>::deallocate": {"offset": "0x27AD0"}, "std::allocator<jitasm::Instr>::allocate": {"offset": "0x27750"}, "std::allocator<jitasm::Instr>::deallocate": {"offset": "0x27BC0"}, "std::allocator<jitasm::compiler::BasicBlock *>::allocate": {"offset": "0x27670"}, "std::allocator<jitasm::compiler::BasicBlock *>::deallocate": {"offset": "0x27B20"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::allocate": {"offset": "0x277C0"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::deallocate": {"offset": "0x27C10"}, "std::allocator<jitasm::compiler::OrderedLabel>::allocate": {"offset": "0x276E0"}, "std::allocator<jitasm::compiler::OrderedLabel>::deallocate": {"offset": "0x27B70"}, "std::allocator<jitasm::compiler::RegUsePoint>::deallocate": {"offset": "0x27B70"}, "std::allocator<jitasm::compiler::VarAttribute>::deallocate": {"offset": "0x27C60"}, "std::allocator<std::pair<unsigned __int64,unsigned __int64> >::deallocate": {"offset": "0x27B70"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x27670"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x27B20"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x32450"}, "std::allocator<unsigned int>::allocate": {"offset": "0x27600"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x27AD0"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD4D0"}, "std::bad_alloc::bad_alloc": {"offset": "0x34C00"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA900"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA4F0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA900"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x2B00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x303F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x30560"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD6C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA290"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA7D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x2A790"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0xA830"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD540"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x2F280"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x32490"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x325F0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA830"}, "std::deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >::~deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >": {"offset": "0x19490"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Growmap": {"offset": "0x26D90"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Tidy": {"offset": "0x27210"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Xlen": {"offset": "0x27470"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::~deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x19560"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Growmap": {"offset": "0x26BB0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Tidy": {"offset": "0x27150"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Xlen": {"offset": "0x27470"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_back": {"offset": "0x28D80"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_front": {"offset": "0x28E40"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::~deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >": {"offset": "0x19460"}, "std::exception::exception": {"offset": "0xA520"}, "std::exception::what": {"offset": "0xE5A0"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> >": {"offset": "0xFA20"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > >": {"offset": "0xFA20"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > > > >": {"offset": "0xFA20"}, "std::function<void __cdecl(CVehicleModelInfo *)>::~function<void __cdecl(CVehicleModelInfo *)>": {"offset": "0xFF60"}, "std::function<void __cdecl(atArray<CVehicleModelInfo> *)>::~function<void __cdecl(atArray<CVehicleModelInfo> *)>": {"offset": "0xFF60"}, "std::function<void __cdecl(fx::ScriptContext &)>::~function<void __cdecl(fx::ScriptContext &)>": {"offset": "0xFF60"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > >": {"offset": "0x10020"}, "std::list<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > >::~list<std::pair<unsigned int const ,std::list<int,std::allocator<int> > >,std::allocator<std::pair<unsigned int const ,std::list<int,std::allocator<int> > > > >": {"offset": "0xFFF0"}, "std::list<unsigned int,std::allocator<unsigned int> >::~list<unsigned int,std::allocator<unsigned int> >": {"offset": "0xFF90"}, "std::locale::~locale": {"offset": "0x2DD90"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x2E450"}, "std::numpunct<char>::do_falsename": {"offset": "0x2E460"}, "std::numpunct<char>::do_grouping": {"offset": "0x2E4A0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x2E4E0"}, "std::numpunct<char>::do_truename": {"offset": "0x2E4F0"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x2DB20"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>": {"offset": "0xA7D0"}, "std::rotate<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<jitasm::compiler::BasicBlock *> > > >": {"offset": "0x18280"}, "std::runtime_error::runtime_error": {"offset": "0xA5A0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x2DD70"}, "std::unordered_set<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<unsigned int> >::~unordered_set<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<unsigned int> >": {"offset": "0x10050"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x2D7F0"}, "utf8::exception::exception": {"offset": "0x31780"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x30800"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x31130"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x31810"}, "utf8::invalid_code_point::what": {"offset": "0x327A0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA900"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x31880"}, "utf8::invalid_utf8::what": {"offset": "0x327B0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA900"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x318E0"}, "utf8::not_enough_room::what": {"offset": "0x327C0"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA900"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x30F10"}, "vva": {"offset": "0x32780"}, "xbr::GetGameBuild": {"offset": "0x10680"}, "xbr::GetReplaceExecutableInit": {"offset": "0x32900"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x32B20"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1A70"}}}