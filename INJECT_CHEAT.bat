@echo off
title FiveM Cheat - INJETAR CHEAT
color 0A

echo ========================================
echo      FIVEM CHEAT - INJETAR CHEAT
echo ========================================
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Execute como ADMINISTRADOR!
    echo.
    echo 1. Clique com botao direito neste arquivo
    echo 2. Selecione "Executar como administrador"
    echo.
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo VERIFICANDO ARQUIVOS
echo ========================================

REM Verificar se DLL existe
if not exist "FiveMCheat_FINAL.dll" (
    echo [ERROR] FiveMCheat_FINAL.dll nao encontrada!
    echo.
    echo SOLUCAO:
    echo 1. Execute: COMPILE_CHEAT_SIMPLE.bat
    echo 2. Aguarde a compilacao
    echo 3. Execute este script novamente
    echo.
    pause
    exit /b 1
)

echo [OK] FiveMCheat_FINAL.dll encontrada
dir FiveMCheat_FINAL.dll | find "FiveMCheat_FINAL.dll"

echo.

echo ========================================
echo VERIFICANDO FIVEM
echo ========================================

REM Verificar se FiveM está rodando
echo [INFO] Procurando processos do FiveM...

tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GameProcess.exe" 2>NUL | find /I /N "FiveM_GameProcess.exe">NUL
set "fivem_game_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 if %fivem_game_running% neq 0 (
    echo [WARNING] FiveM NAO ESTA RODANDO!
    echo.
    echo INSTRUCOES:
    echo 1. Abra o FiveM
    echo 2. Entre em um servidor (preferencialmente privado)
    echo 3. Aguarde carregar COMPLETAMENTE no jogo
    echo 4. Execute este script novamente
    echo.
    echo Deseja continuar mesmo assim? (s/n)
    set /p choice="> "
    if /i not "%choice%"=="s" exit /b 1
) else (
    echo [OK] FiveM detectado rodando!
    
    REM Mostrar processos encontrados
    if %fivem_running% equ 0 echo     ✅ FiveM.exe encontrado
    if %fivem_gta_running% equ 0 echo     ✅ FiveM_GTAProcess.exe encontrado  
    if %fivem_game_running% equ 0 echo     ✅ FiveM_GameProcess.exe encontrado
)

echo.

echo ========================================
echo METODOS DE INJECAO
echo ========================================

echo Escolha o metodo de injecao:
echo.
echo [1] PowerShell (Automatico)
echo [2] Process Hacker (Manual - Recomendado)
echo [3] Extreme Injector (Manual)
echo [4] Cheat Engine (Manual)
echo [5] Sair
echo.
set /p method="Digite sua escolha (1-5): "

if "%method%"=="1" goto powershell_inject
if "%method%"=="2" goto process_hacker
if "%method%"=="3" goto extreme_injector
if "%method%"=="4" goto cheat_engine
if "%method%"=="5" exit /b 0

echo [ERROR] Opcao invalida!
pause
exit /b 1

:powershell_inject
echo.
echo ========================================
echo INJECAO VIA POWERSHELL
echo ========================================
echo.
echo [INFO] Tentando injecao automatica via PowerShell...

REM Criar script PowerShell para injecao
echo # Script de injecao DLL > inject_dll.ps1
echo Add-Type -TypeDefinition @" >> inject_dll.ps1
echo using System; >> inject_dll.ps1
echo using System.Diagnostics; >> inject_dll.ps1
echo using System.Runtime.InteropServices; >> inject_dll.ps1
echo using System.Text; >> inject_dll.ps1
echo. >> inject_dll.ps1
echo public class DllInjector { >> inject_dll.ps1
echo     [DllImport("kernel32.dll")] >> inject_dll.ps1
echo     public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     [DllImport("kernel32.dll", CharSet = CharSet.Auto)] >> inject_dll.ps1
echo     public static extern IntPtr GetModuleHandle(string lpModuleName); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     [DllImport("kernel32", CharSet = CharSet.Ansi, ExactSpelling = true, SetLastError = true)] >> inject_dll.ps1
echo     public static extern IntPtr GetProcAddress(IntPtr hModule, string procName); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)] >> inject_dll.ps1
echo     public static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     [DllImport("kernel32.dll", SetLastError = true)] >> inject_dll.ps1
echo     public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     [DllImport("kernel32.dll")] >> inject_dll.ps1
echo     public static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId); >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     public static bool InjectDLL(int processId, string dllPath) { >> inject_dll.ps1
echo         try { >> inject_dll.ps1
echo             IntPtr hProcess = OpenProcess(0x1F0FFF, false, processId); >> inject_dll.ps1
echo             if (hProcess == IntPtr.Zero) return false; >> inject_dll.ps1
echo. >> inject_dll.ps1
echo             IntPtr loadLibraryAddr = GetProcAddress(GetModuleHandle("kernel32.dll"), "LoadLibraryA"); >> inject_dll.ps1
echo             if (loadLibraryAddr == IntPtr.Zero) return false; >> inject_dll.ps1
echo. >> inject_dll.ps1
echo             IntPtr allocMemAddress = VirtualAllocEx(hProcess, IntPtr.Zero, (uint)((dllPath.Length + 1) * Marshal.SizeOf(typeof(char))), 0x3000, 0x40); >> inject_dll.ps1
echo             if (allocMemAddress == IntPtr.Zero) return false; >> inject_dll.ps1
echo. >> inject_dll.ps1
echo             UIntPtr bytesWritten; >> inject_dll.ps1
echo             bool result = WriteProcessMemory(hProcess, allocMemAddress, Encoding.Default.GetBytes(dllPath), (uint)((dllPath.Length + 1) * Marshal.SizeOf(typeof(char))), out bytesWritten); >> inject_dll.ps1
echo             if (!result) return false; >> inject_dll.ps1
echo. >> inject_dll.ps1
echo             CreateRemoteThread(hProcess, IntPtr.Zero, 0, loadLibraryAddr, allocMemAddress, 0, IntPtr.Zero); >> inject_dll.ps1
echo             return true; >> inject_dll.ps1
echo         } catch { >> inject_dll.ps1
echo             return false; >> inject_dll.ps1
echo         } >> inject_dll.ps1
echo     } >> inject_dll.ps1
echo } >> inject_dll.ps1
echo "@ >> inject_dll.ps1
echo. >> inject_dll.ps1
echo # Procurar processo FiveM >> inject_dll.ps1
echo $fivemProcess = Get-Process ^| Where-Object { $_.ProcessName -like "*FiveM*GameProcess*" } ^| Select-Object -First 1 >> inject_dll.ps1
echo. >> inject_dll.ps1
echo if ($fivemProcess) { >> inject_dll.ps1
echo     Write-Host "[INFO] Processo encontrado: $($fivemProcess.ProcessName) (PID: $($fivemProcess.Id))" >> inject_dll.ps1
echo     $dllPath = (Get-Location).Path + "\FiveMCheat_FINAL.dll" >> inject_dll.ps1
echo     Write-Host "[INFO] Injetando DLL: $dllPath" >> inject_dll.ps1
echo. >> inject_dll.ps1
echo     if ([DllInjector]::InjectDLL($fivemProcess.Id, $dllPath)) { >> inject_dll.ps1
echo         Write-Host "[OK] DLL injetada com sucesso!" -ForegroundColor Green >> inject_dll.ps1
echo     } else { >> inject_dll.ps1
echo         Write-Host "[ERROR] Falha na injecao!" -ForegroundColor Red >> inject_dll.ps1
echo     } >> inject_dll.ps1
echo } else { >> inject_dll.ps1
echo     Write-Host "[ERROR] Processo FiveM_GameProcess nao encontrado!" -ForegroundColor Red >> inject_dll.ps1
echo } >> inject_dll.ps1

echo [INFO] Executando script de injecao...
powershell -ExecutionPolicy Bypass -File inject_dll.ps1

REM Limpar arquivo temporario
del inject_dll.ps1 >nul 2>&1

goto success

:process_hacker
echo.
echo ========================================
echo PROCESS HACKER (RECOMENDADO)
echo ========================================
echo.
echo INSTRUCOES PARA PROCESS HACKER:
echo.
echo 1. Baixe Process Hacker: https://processhacker.sourceforge.io/
echo 2. Execute Process Hacker como ADMINISTRADOR
echo 3. Encontre o processo "FiveM_GameProcess.exe"
echo 4. Clique com botao direito no processo
echo 5. Selecione "Miscellaneous" ^> "Inject DLL"
echo 6. Navegue e selecione: FiveMCheat_FINAL.dll
echo 7. Clique "Inject"
echo 8. Aguarde confirmacao de sucesso
echo.
echo Caminho da DLL: %CD%\FiveMCheat_FINAL.dll
echo.
goto success

:extreme_injector
echo.
echo ========================================
echo EXTREME INJECTOR
echo ========================================
echo.
echo INSTRUCOES PARA EXTREME INJECTOR:
echo.
echo 1. Baixe Extreme Injector
echo 2. Execute como ADMINISTRADOR
echo 3. Em "Process", selecione: FiveM_GameProcess.exe
echo 4. Em "DLL", clique "..." e selecione: FiveMCheat_FINAL.dll
echo 5. Clique "Inject"
echo 6. Aguarde confirmacao de sucesso
echo.
echo Caminho da DLL: %CD%\FiveMCheat_FINAL.dll
echo.
goto success

:cheat_engine
echo.
echo ========================================
echo CHEAT ENGINE
echo ========================================
echo.
echo INSTRUCOES PARA CHEAT ENGINE:
echo.
echo 1. Abra Cheat Engine
echo 2. Clique no icone do computador (Select Process)
echo 3. Encontre e selecione "FiveM_GameProcess.exe"
echo 4. Clique "Open"
echo 5. Va em "Memory View" (Ctrl+M)
echo 6. Menu "Tools" ^> "Inject DLL"
echo 7. Selecione: FiveMCheat_FINAL.dll
echo 8. Clique "Inject"
echo.
echo Caminho da DLL: %CD%\FiveMCheat_FINAL.dll
echo.
goto success

:success
echo.
echo ========================================
echo CHEAT INJETADO! - INSTRUCOES DE USO
echo ========================================
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [F1] - Toggle GOD MODE (vida/armadura infinita)
echo [F2] - Toggle INFINITE AMMO (municao infinita)
echo.
echo 🎯 TESTE SUGERIDO:
echo.
echo 1. No FiveM, pressione F1 para ativar God Mode
echo    - Sua vida deve ficar em 100%% sempre
echo    - Deixe NPCs te atacarem para testar
echo.
echo 2. Pressione F2 para ativar Infinite Ammo
echo    - Pegue uma arma e atire
echo    - Municao nao deve diminuir
echo.
echo 📋 CONSOLE DO CHEAT:
echo.
echo ✅ Uma janela de console deve ter aparecido
echo ✅ Mostra status das funcionalidades
echo ✅ Confirma quando F1/F2 sao pressionados
echo.
echo ⚠️  IMPORTANTE:
echo.
echo ✅ Cheat compilado com offsets HARDCODED
echo ✅ Enderecos absolutos (nao busca em runtime)
echo ✅ Performance otimizada
echo ✅ Use apenas em servidores PRIVADOS/TESTE
echo.
echo 🔧 TROUBLESHOOTING:
echo.
echo ❌ Console nao apareceu:
echo    - DLL pode nao ter sido injetada
echo    - Tente reinjetar
echo.
echo ❌ F1/F2 nao funcionam:
echo    - Verifique se esta no jogo (nao no menu)
echo    - Offsets podem precisar atualizacao
echo.

REM Criar log de injecao
echo FiveM Cheat Injection - %date% %time% > INJECTION_LOG.txt
echo. >> INJECTION_LOG.txt
echo Status: Cheat injetado >> INJECTION_LOG.txt
echo DLL: FiveMCheat_FINAL.dll >> INJECTION_LOG.txt
echo Controles: F1 = God Mode, F2 = Infinite Ammo >> INJECTION_LOG.txt
echo. >> INJECTION_LOG.txt

echo ========================================
echo ✅ CHEAT ATIVO E PRONTO PARA USO!
echo Log salvo em: INJECTION_LOG.txt
echo ========================================

echo.
echo Pressione qualquer tecla para sair...
pause >nul
