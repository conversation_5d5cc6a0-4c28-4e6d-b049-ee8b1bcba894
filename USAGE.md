# Como Usar o FiveM Offset Dumper

## Passo a Passo

### 1. Preparação
1. Certifique-se de que o FiveM está fechado
2. Tenha um injetor de DLL (recomendado: Process Hacker, Extreme Injector, ou similar)
3. Localize o arquivo `FiveMOffsetDumper.dll` na pasta do projeto

### 2. Injeção
1. Abra o FiveM e entre em um servidor
2. Abra seu injetor de DLL
3. Selecione o processo do FiveM (geralmente `FiveM.exe` ou `FiveM_GTAProcess.exe`)
4. Injete a DLL `FiveMOffsetDumper.dll`

### 3. Uso da Interface

#### Controles Básicos
- **INSERT** - Mostrar/ocultar a interface
- **END** - Sair da DLL

#### Interface Principal

##### Área de Controle de Scan
- **Scan All Offsets** - Inicia o scan de todos os offsets
- **Custom Pattern** - Campo para testar padrões personalizados
- **Filtros** - Filtrar por nome ou status dos offsets

##### Lista de Offsets
- Visualização em tabela com:
  - Nome do offset
  - Categoria (Player, Vehicle, World, etc.)
  - Endereço encontrado
  - Status (Found/Missing)
- Clique em um offset para ver detalhes

##### Detalhes do Offset
- Informações completas do offset selecionado
- Botões para copiar endereço/offset para clipboard
- Opção de re-escanear offsets específicos

### 4. Exportação de Dados

#### Menu File
- **Export to JSON** - Exporta todos os offsets para formato JSON
- **Export to C++ Header** - Gera arquivo header C++ com constantes
- **Export to Cheat Engine** - Cria tabela para Cheat Engine

#### Formatos de Saída

##### JSON (offsets.json)
```json
{
  "offsets": {
    "LocalPlayer": {
      "description": "Pointer to local player entity",
      "category": "Player",
      "found": true,
      "address": "0x7FF123456789",
      "offset": "0x123456"
    }
  }
}
```

##### C++ Header (offsets.h)
```cpp
namespace FiveMOffsets {
    namespace Player {
        constexpr uintptr_t LocalPlayer = 0x123456;
    }
}
```

##### Cheat Engine Table (offsets.CT)
- Arquivo XML compatível com Cheat Engine
- Cada offset como entrada separada
- Endereços absolutos prontos para uso

### 5. Usando os Offsets em Seu Cheat

#### Exemplo C++
```cpp
#include "offsets.h"

// Obter base do módulo
uintptr_t moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));

// Usar offset
uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);

// Ler coordenadas do jogador
float* coords = reinterpret_cast<float*>(localPlayer + FiveMOffsets::Player::PlayerCoords);
float x = coords[0];
float y = coords[1];
float z = coords[2];
```

#### Exemplo com JSON (Python)
```python
import json

# Carregar offsets
with open('offsets.json', 'r') as f:
    data = json.load(f)

# Obter offset
local_player_offset = int(data['offsets']['LocalPlayer']['offset'], 16)
module_base = int(data['metadata']['module_base'], 16)

# Calcular endereço
local_player_addr = module_base + local_player_offset
```

### 6. Dicas e Melhores Práticas

#### Segurança
- Sempre teste em ambiente offline primeiro
- Use apenas para fins educacionais
- Respeite os termos de serviço do FiveM

#### Performance
- Execute o scan apenas quando necessário
- Use filtros para encontrar offsets específicos rapidamente
- Salve os resultados para reutilização

#### Troubleshooting
- Se offsets não forem encontrados, tente:
  - Aguardar o jogo carregar completamente
  - Re-escanear após entrar em um servidor
  - Verificar se a versão do FiveM é compatível

#### Atualizações do FiveM
- Offsets podem mudar com atualizações
- Re-escaneie após cada atualização
- Mantenha backups dos offsets funcionais

### 7. Console de Debug

A DLL abre automaticamente um console que mostra:
- Status de inicialização
- Progresso do scan
- Erros e avisos
- Estatísticas de offsets encontrados

### 8. Exemplo de Workflow Completo

1. **Preparação**
   ```
   FiveM fechado → Compilar DLL → Preparar injetor
   ```

2. **Injeção**
   ```
   Abrir FiveM → Entrar no servidor → Injetar DLL
   ```

3. **Scanning**
   ```
   Pressionar INSERT → Scan All Offsets → Aguardar conclusão
   ```

4. **Exportação**
   ```
   File → Export to C++ Header → Salvar como offsets.h
   ```

5. **Desenvolvimento**
   ```
   Incluir offsets.h → Usar constantes → Compilar cheat
   ```

### 9. Estrutura de Arquivos Gerados

```
projeto/
├── FiveMOffsetDumper.dll    # DLL principal
├── offsets.json             # Offsets em JSON
├── offsets.h                # Header C++
├── offsets.CT               # Tabela Cheat Engine
└── logs/                    # Logs de debug (se habilitado)
```

### 10. Suporte e Desenvolvimento

Para adicionar novos offsets:
1. Edite `src/OffsetDatabase.cpp`
2. Adicione o padrão na categoria apropriada
3. Recompile o projeto

Para reportar bugs ou sugestões:
- Verifique o console de debug
- Anote a versão do FiveM
- Descreva os passos para reproduzir o problema
