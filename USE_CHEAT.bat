@echo off
title FiveM Cheat - USAR CHEAT
color 0A

echo ========================================
echo        FIVEM CHEAT - USAR CHEAT
echo ========================================
echo.
echo Usando DLL com offsets HARDCODED!
echo.

REM Verificar se está rodando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] EXECUTE COMO ADMINISTRADOR!
    echo.
    echo 1. Clique com botao direito neste arquivo
    echo 2. Selecione "Executar como administrador"
    echo.
    pause
    exit /b 1
)

echo [OK] Executando como administrador
echo.

echo ========================================
echo VERIFICANDO ARQUIVOS
echo ========================================

REM Verificar se DLL final existe
if not exist "FiveMCheat_FINAL.dll" (
    echo [ERROR] FiveMCheat_FINAL.dll nao encontrada!
    echo.
    echo SOLUCAO:
    echo 1. Execute: BUILD_FINAL_CHEAT.bat
    echo 2. Aguarde a compilacao
    echo 3. Execute este script novamente
    echo.
    pause
    exit /b 1
)

echo [OK] FiveMCheat_FINAL.dll encontrada
dir FiveMCheat_FINAL.dll | find "FiveMCheat_FINAL.dll"

echo.

echo ========================================
echo VERIFICANDO FIVEM
echo ========================================

REM Verificar se FiveM está rodando
tasklist /FI "IMAGENAME eq FiveM.exe" 2>NUL | find /I /N "FiveM.exe">NUL
set "fivem_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GTAProcess.exe" 2>NUL | find /I /N "FiveM_GTAProcess.exe">NUL
set "fivem_gta_running=%ERRORLEVEL%"

tasklist /FI "IMAGENAME eq FiveM_GameProcess.exe" 2>NUL | find /I /N "FiveM_GameProcess.exe">NUL
set "fivem_game_running=%ERRORLEVEL%"

if %fivem_running% neq 0 if %fivem_gta_running% neq 0 if %fivem_game_running% neq 0 (
    echo [WARNING] FiveM NAO ESTA RODANDO!
    echo.
    echo INSTRUCOES:
    echo 1. Abra o FiveM
    echo 2. Entre em um servidor (preferencialmente privado)
    echo 3. Aguarde carregar COMPLETAMENTE no jogo
    echo 4. Execute este script novamente
    echo.
    echo Deseja continuar mesmo assim? (s/n)
    set /p choice="> "
    if /i not "%choice%"=="s" exit /b 1
) else (
    echo [OK] FiveM detectado rodando!
    
    REM Mostrar processos encontrados
    if %fivem_running% equ 0 echo     - FiveM.exe encontrado
    if %fivem_gta_running% equ 0 echo     - FiveM_GTAProcess.exe encontrado  
    if %fivem_game_running% equ 0 echo     - FiveM_GameProcess.exe encontrado
)

echo.

echo ========================================
echo INJETANDO CHEAT
echo ========================================

echo [INFO] Iniciando injecao da DLL final...
echo [INFO] Arquivo: FiveMCheat_FINAL.dll
echo [INFO] Processo alvo: FiveM_GameProcess.exe
echo.

if exist "SimpleInjector.exe" (
    echo METODO 1: Usando SimpleInjector
    echo.
    echo INSTRUCOES:
    echo 1. O SimpleInjector vai abrir
    echo 2. Selecione opcao 2 (Injetar DLL)
    echo 3. Navegue e selecione: FiveMCheat_FINAL.dll
    echo 4. Aguarde mensagem de sucesso
    echo.
    echo Pressione ENTER para abrir o injetor...
    pause >nul
    
    start SimpleInjector.exe
    
    echo [INFO] SimpleInjector aberto!
    echo Aguarde a injecao ser concluida...
    
) else (
    echo METODO 2: Injecao Manual
    echo.
    echo OPCOES PARA INJETAR:
    echo.
    echo A) Process Hacker (RECOMENDADO):
    echo    1. Baixe Process Hacker
    echo    2. Abra como administrador
    echo    3. Encontre FiveM_GameProcess.exe
    echo    4. Clique direito ^> Miscellaneous ^> Inject DLL
    echo    5. Selecione: FiveMCheat_FINAL.dll
    echo.
    echo B) Extreme Injector:
    echo    1. Baixe Extreme Injector
    echo    2. Process: FiveM_GameProcess.exe
    echo    3. DLL: FiveMCheat_FINAL.dll
    echo    4. Clique Inject
    echo.
    echo C) Cheat Engine:
    echo    1. Abra Cheat Engine
    echo    2. Attach to FiveM_GameProcess.exe
    echo    3. Memory View ^> Tools ^> Inject DLL
    echo    4. Selecione: FiveMCheat_FINAL.dll
    echo.
    
    echo Pressione qualquer tecla apos injetar a DLL...
    pause >nul
)

echo.

echo ========================================
echo CHEAT ATIVO! - INSTRUCOES DE USO
echo ========================================
echo.
echo 🎮 CONTROLES NO FIVEM:
echo.
echo [INSERT] - Abrir/fechar MENU PRINCIPAL
echo [F1]     - Toggle ESP (ver jogadores)
echo [F2]     - Toggle GOD MODE (vida infinita)
echo [F3]     - Teleport para waypoint
echo [F4]     - Reparar veiculo atual
echo [F5]     - Munição infinita
echo [F6]     - Sem recarga
echo.
echo 📋 MENU PRINCIPAL (INSERT):
echo.
echo ┌─ Player Hacks
echo │  ├─ God Mode On/Off
echo │  ├─ Heal (vida completa)
echo │  ├─ Teleport para coordenadas
echo │  ├─ Stamina infinita
echo │  └─ Super velocidade
echo │
echo ├─ Weapon Hacks  
echo │  ├─ Infinite Ammo
echo │  ├─ No Reload
echo │  ├─ Damage Multiplier
echo │  └─ No Recoil
echo │
echo ├─ Vehicle Hacks
echo │  ├─ Repair Vehicle
echo │  ├─ Speed Boost
echo │  ├─ Invincible Vehicle
echo │  └─ Disable Handbrake
echo │
echo └─ ESP Settings
echo    ├─ Toggle ESP
echo    ├─ Show Health/Armor
echo    ├─ Show Distance
echo    └─ Show Player Names
echo.
echo 🎯 TESTE SUGERIDO:
echo.
echo 1. Pressione INSERT para abrir o menu
echo    - Verifique se o menu aparece
echo    - Explore todas as opcoes
echo.
echo 2. Teste ESP (F1):
echo    - Ative o ESP
echo    - Procure por outros jogadores no servidor
echo    - Voce deve ver caixas vermelhas ao redor deles
echo    - Informacoes de vida/armadura devem aparecer
echo.
echo 3. Teste God Mode (F2):
echo    - Ative o God Mode
echo    - Deixe NPCs ou jogadores te atacarem
echo    - Sua vida deve permanecer em 100%%
echo    - Armadura tambem deve ficar infinita
echo.
echo 4. Teste Teleport (F3):
echo    - Marque um waypoint no mapa
echo    - Pressione F3
echo    - Voce deve ser teleportado instantaneamente
echo.
echo 5. Teste Vehicle Hacks (F4):
echo    - Entre em um veiculo
echo    - Danifique o veiculo
echo    - Pressione F4 para reparar
echo.
echo 6. Teste Weapon Hacks (F5):
echo    - Pegue uma arma
echo    - Pressione F5 para munição infinita
echo    - Atire sem parar - munição nao deve acabar
echo.
echo ⚠️  IMPORTANTE:
echo.
echo ✅ DLL compilada com 155 offsets REAIS
echo ✅ Enderecos absolutos hardcoded
echo ✅ Nao precisa buscar offsets em runtime
echo ✅ Performance otimizada
echo ✅ Use apenas em servidores PRIVADOS/TESTE
echo ✅ Compativel com versao atual do FiveM
echo.
echo 🔧 TROUBLESHOOTING:
echo.
echo ❌ Menu nao abre (INSERT):
echo    - Verifique se DLL foi injetada corretamente
echo    - Tente reinjetar a DLL
echo.
echo ❌ ESP nao funciona (F1):
echo    - Certifique-se que ha outros jogadores no servidor
echo    - Offsets podem precisar atualizacao
echo.
echo ❌ God Mode nao funciona (F2):
echo    - Verifique se esta sendo atacado
echo    - Alguns servidores podem ter protecao
echo.
echo ❌ Funcoes nao respondem:
echo    - Offsets podem estar desatualizados
echo    - FiveM pode ter sido atualizado
echo    - Re-extraia offsets e recompile
echo.

REM Criar log de uso
echo FiveM Cheat Usage - %date% %time% > CHEAT_USAGE_LOG.txt
echo. >> CHEAT_USAGE_LOG.txt
echo Status: Cheat injetado e ativo >> CHEAT_USAGE_LOG.txt
echo DLL: FiveMCheat_FINAL.dll >> CHEAT_USAGE_LOG.txt
echo Offsets: 155 hardcoded (absolutos) >> CHEAT_USAGE_LOG.txt
echo Base: 0x140000000 >> CHEAT_USAGE_LOG.txt
echo. >> CHEAT_USAGE_LOG.txt
echo Controles: >> CHEAT_USAGE_LOG.txt
echo - INSERT: Menu principal >> CHEAT_USAGE_LOG.txt
echo - F1: ESP >> CHEAT_USAGE_LOG.txt
echo - F2: God Mode >> CHEAT_USAGE_LOG.txt
echo - F3: Teleport >> CHEAT_USAGE_LOG.txt
echo - F4: Repair Vehicle >> CHEAT_USAGE_LOG.txt
echo - F5: Infinite Ammo >> CHEAT_USAGE_LOG.txt
echo. >> CHEAT_USAGE_LOG.txt

echo ========================================
echo ✅ CHEAT ATIVO E PRONTO PARA USO!
echo Log salvo em: CHEAT_USAGE_LOG.txt
echo ========================================

echo.
echo O cheat esta rodando no FiveM!
echo Use os controles acima para testar.
echo.
echo Pressione qualquer tecla para sair...
pause >nul
