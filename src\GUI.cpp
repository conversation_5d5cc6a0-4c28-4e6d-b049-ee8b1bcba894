#include "GUI.h"
#include "OffsetDumper.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cctype>

extern OffsetDumper* g_dumper;
GUI* GUI::s_instance = nullptr;

GUI::GUI() : 
    m_hwnd(nullptr),
    m_visible(false),
    m_initialized(false),
    m_pd3dDevice(nullptr),
    m_pd3dDeviceContext(nullptr),
    m_pSwap<PERSON>hain(nullptr),
    m_mainRenderTargetView(nullptr),
    m_showAbout(false),
    m_selectedOffset(-1),
    m_showFoundOnly(false),
    m_showNotFoundOnly(false) {
    
    s_instance = this;
    strcpy_s(m_exportPath, "offsets");
    strcpy_s(m_customPattern, "");
    strcpy_s(m_filterText, "");
}

GUI::~GUI() {
    Shutdown();
    s_instance = nullptr;
}

bool GUI::Initialize() {
    // Register window class
    m_wc.cbSize = sizeof(WNDCLASSEX);
    m_wc.style = CS_CLASSDC;
    m_wc.lpfnWndProc = WndProc;
    m_wc.cbClsExtra = 0L;
    m_wc.cbWndExtra = 0L;
    m_wc.hInstance = GetModuleHandle(nullptr);
    m_wc.hIcon = nullptr;
    m_wc.hCursor = nullptr;
    m_wc.hbrBackground = nullptr;
    m_wc.lpszMenuName = nullptr;
    m_wc.lpszClassName = "FiveMOffsetDumper";
    m_wc.hIconSm = nullptr;
    RegisterClassExA(&m_wc);

    // Create window
    m_hwnd = CreateWindowA("FiveMOffsetDumper", "FiveM Offset Dumper", WS_OVERLAPPEDWINDOW, 100, 100, 1280, 800, nullptr, nullptr, m_wc.hInstance, nullptr);
    
    if (!m_hwnd) {
        std::cerr << "[GUI] Failed to create window" << std::endl;
        return false;
    }
    
    // Initialize Direct3D
    if (!CreateDeviceD3D(m_hwnd)) {
        CleanupDeviceD3D();
        UnregisterClass(m_wc.lpszClassName, m_wc.hInstance);
        return false;
    }
    
    // Show window (start hidden)
    ShowWindow(m_hwnd, SW_HIDE);
    UpdateWindow(m_hwnd);
    
    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;
    
    // Setup Dear ImGui style
    ImGui::StyleColorsDark();
    
    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(m_hwnd);
    ImGui_ImplDX11_Init(m_pd3dDevice, m_pd3dDeviceContext);
    
    m_initialized = true;
    std::cout << "[GUI] Successfully initialized" << std::endl;
    
    return true;
}

void GUI::Shutdown() {
    if (!m_initialized) return;
    
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
    
    CleanupDeviceD3D();
    DestroyWindow(m_hwnd);
    UnregisterClassA(m_wc.lpszClassName, m_wc.hInstance);
    
    m_initialized = false;
}

void GUI::Render() {
    if (!m_initialized) return;

    // Show/hide window based on visibility state
    if (m_visible) {
        ShowWindow(m_hwnd, SW_SHOW);
    } else {
        ShowWindow(m_hwnd, SW_HIDE);
        return;
    }
    
    // Handle window messages
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
        if (msg.message == WM_QUIT) {
            m_visible = false;
            return;
        }
    }
    
    // Start the Dear ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();
    
    // Render GUI
    RenderMainWindow();
    
    if (m_showAbout) {
        RenderAboutWindow();
    }
    
    // Rendering
    ImGui::Render();
    const float clear_color[4] = { 0.0f, 0.0f, 0.0f, 1.0f };
    m_pd3dDeviceContext->OMSetRenderTargets(1, &m_mainRenderTargetView, nullptr);
    m_pd3dDeviceContext->ClearRenderTargetView(m_mainRenderTargetView, clear_color);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
    
    m_pSwapChain->Present(1, 0); // Present with vsync
}

LRESULT WINAPI GUI::WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;
    
    switch (msg) {
    case WM_SIZE:
        if (s_instance && s_instance->m_pd3dDevice != nullptr && wParam != SIZE_MINIMIZED) {
            s_instance->CleanupRenderTarget();
            s_instance->m_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            s_instance->CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

bool GUI::CreateDeviceD3D(HWND hWnd) {
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

    if (D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &m_pSwapChain, &m_pd3dDevice, &featureLevel, &m_pd3dDeviceContext) != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void GUI::CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (m_pSwapChain) { m_pSwapChain->Release(); m_pSwapChain = nullptr; }
    if (m_pd3dDeviceContext) { m_pd3dDeviceContext->Release(); m_pd3dDeviceContext = nullptr; }
    if (m_pd3dDevice) { m_pd3dDevice->Release(); m_pd3dDevice = nullptr; }
}

void GUI::CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    m_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    m_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &m_mainRenderTargetView);
    pBackBuffer->Release();
}

void GUI::CleanupRenderTarget() {
    if (m_mainRenderTargetView) { m_mainRenderTargetView->Release(); m_mainRenderTargetView = nullptr; }
}

void GUI::RenderMainWindow() {
    if (!ImGui::Begin("FiveM Offset Dumper", &m_visible, ImGuiWindowFlags_MenuBar)) {
        ImGui::End();
        return;
    }

    // Menu bar
    if (ImGui::BeginMenuBar()) {
        if (ImGui::BeginMenu("File")) {
            if (ImGui::MenuItem("Export to JSON")) {
                if (g_dumper) {
                    std::string filename = std::string(m_exportPath) + ".json";
                    g_dumper->ExportToJSON(filename);
                }
            }
            if (ImGui::MenuItem("Export to C++ Header")) {
                if (g_dumper) {
                    std::string filename = std::string(m_exportPath) + ".h";
                    g_dumper->ExportToCppHeader(filename);
                }
            }
            ImGui::Separator();
            if (ImGui::MenuItem("Exit")) {
                m_visible = false;
            }
            ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Help")) {
            if (ImGui::MenuItem("About")) {
                m_showAbout = true;
            }
            ImGui::EndMenu();
        }
        ImGui::EndMenuBar();
    }

    // Main content
    RenderScanControls();
    ImGui::Separator();

    // Split view
    ImGui::BeginChild("OffsetList", ImVec2(ImGui::GetContentRegionAvail().x * 0.6f, 0), true);
    RenderOffsetList();
    ImGui::EndChild();

    ImGui::SameLine();

    ImGui::BeginChild("OffsetDetails", ImVec2(0, 0), true);
    RenderOffsetDetails();
    ImGui::EndChild();

    RenderStatusBar();

    ImGui::End();
}

void GUI::RenderScanControls() {
    ImGui::Text("Scan Controls");

    if (g_dumper) {
        if (g_dumper->IsScanning()) {
            ImGui::ProgressBar(g_dumper->GetScanProgress(), ImVec2(-1.0f, 0.0f));
            ImGui::SameLine();
            ImGui::Text("Scanning...");
        } else {
            if (ImGui::Button("Scan All Offsets", ImVec2(150, 0))) {
                g_dumper->ScanAllOffsets();
            }
            ImGui::SameLine();

            ImGui::PushItemWidth(200);
            ImGui::InputText("Custom Pattern", m_customPattern, sizeof(m_customPattern));
            ImGui::PopItemWidth();
            ImGui::SameLine();

            if (ImGui::Button("Scan Pattern", ImVec2(100, 0))) {
                // TODO: Implement custom pattern scanning
            }
        }
    }

    // Filters
    ImGui::Spacing();
    ImGui::Text("Filters:");
    ImGui::SameLine();
    ImGui::PushItemWidth(200);
    ImGui::InputText("##Filter", m_filterText, sizeof(m_filterText));
    ImGui::PopItemWidth();
    ImGui::SameLine();
    ImGui::Checkbox("Found Only", &m_showFoundOnly);
    ImGui::SameLine();
    ImGui::Checkbox("Not Found Only", &m_showNotFoundOnly);
}

void GUI::RenderOffsetList() {
    ImGui::Text("Offsets");

    if (!g_dumper) {
        ImGui::Text("Dumper not initialized");
        return;
    }

    const auto& offsets = g_dumper->GetOffsets();

    if (ImGui::BeginTable("OffsetsTable", 4, ImGuiTableFlags_Resizable | ImGuiTableFlags_Sortable | ImGuiTableFlags_ScrollY)) {
        ImGui::TableSetupColumn("Name", ImGuiTableColumnFlags_WidthFixed, 200.0f);
        ImGui::TableSetupColumn("Category", ImGuiTableColumnFlags_WidthFixed, 100.0f);
        ImGui::TableSetupColumn("Address", ImGuiTableColumnFlags_WidthFixed, 120.0f);
        ImGui::TableSetupColumn("Status", ImGuiTableColumnFlags_WidthFixed, 80.0f);
        ImGui::TableHeadersRow();

        for (int i = 0; i < static_cast<int>(offsets.size()); ++i) {
            const auto& offset = offsets[i];

            // Apply filters
            if (strlen(m_filterText) > 0) {
                std::string name = offset.name;
                std::transform(name.begin(), name.end(), name.begin(), ::tolower);
                std::string filter = m_filterText;
                std::transform(filter.begin(), filter.end(), filter.begin(), ::tolower);
                if (name.find(filter) == std::string::npos) continue;
            }

            if (m_showFoundOnly && !offset.found) continue;
            if (m_showNotFoundOnly && offset.found) continue;

            ImGui::TableNextRow();

            // Name column
            ImGui::TableSetColumnIndex(0);
            if (ImGui::Selectable(offset.name.c_str(), m_selectedOffset == i, ImGuiSelectableFlags_SpanAllColumns)) {
                m_selectedOffset = i;
            }

            // Category column
            ImGui::TableSetColumnIndex(1);
            ImGui::Text("%s", offset.category.c_str());

            // Address column
            ImGui::TableSetColumnIndex(2);
            if (offset.found) {
                ImGui::Text("%s", FormatAddress(offset.address).c_str());
            } else {
                ImGui::TextColored(ImVec4(0.5f, 0.5f, 0.5f, 1.0f), "Not found");
            }

            // Status column
            ImGui::TableSetColumnIndex(3);
            ImVec4 color = GetStatusColor(offset.found);
            ImGui::TextColored(color, offset.found ? "Found" : "Missing");
        }

        ImGui::EndTable();
    }
}

void GUI::RenderOffsetDetails() {
    ImGui::Text("Offset Details");

    if (!g_dumper || m_selectedOffset < 0) {
        ImGui::Text("Select an offset to view details");
        return;
    }

    const auto& offsets = g_dumper->GetOffsets();
    if (m_selectedOffset >= static_cast<int>(offsets.size())) {
        ImGui::Text("Invalid offset selected");
        return;
    }

    const auto& offset = offsets[m_selectedOffset];

    ImGui::Separator();

    ImGui::Text("Name: %s", offset.name.c_str());
    ImGui::Text("Category: %s", offset.category.c_str());
    ImGui::Text("Description: %s", offset.description.c_str());

    ImGui::Spacing();

    if (offset.found) {
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: Found");
        ImGui::Text("Address: %s", FormatAddress(offset.address).c_str());
        ImGui::Text("Offset: 0x%llX", offset.offset);

        if (ImGui::Button("Copy Address")) {
            std::string addr = FormatAddress(offset.address);
            if (OpenClipboard(nullptr)) {
                EmptyClipboard();
                HGLOBAL hg = GlobalAlloc(GMEM_MOVEABLE, addr.size() + 1);
                if (hg) {
                    memcpy(GlobalLock(hg), addr.c_str(), addr.size() + 1);
                    GlobalUnlock(hg);
                    SetClipboardData(CF_TEXT, hg);
                }
                CloseClipboard();
            }
        }

        ImGui::SameLine();
        if (ImGui::Button("Copy Offset")) {
            std::stringstream ss;
            ss << "0x" << std::hex << std::uppercase << offset.offset;
            std::string offsetStr = ss.str();

            if (OpenClipboard(nullptr)) {
                EmptyClipboard();
                HGLOBAL hg = GlobalAlloc(GMEM_MOVEABLE, offsetStr.size() + 1);
                if (hg) {
                    memcpy(GlobalLock(hg), offsetStr.c_str(), offsetStr.size() + 1);
                    GlobalUnlock(hg);
                    SetClipboardData(CF_TEXT, hg);
                }
                CloseClipboard();
            }
        }
    } else {
        ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Status: Not Found");

        if (ImGui::Button("Rescan")) {
            g_dumper->ScanSpecificOffset(offset.name);
        }
    }

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Text("Pattern: %s", offset.pattern.c_str());

    if (ImGui::Button("Test Pattern")) {
        // TODO: Implement pattern testing
    }
}

void GUI::RenderStatusBar() {
    ImGui::Separator();

    if (g_dumper) {
        const auto& offsets = g_dumper->GetOffsets();
        int foundCount = 0;
        for (const auto& offset : offsets) {
            if (offset.found) foundCount++;
        }

        ImGui::Text("Offsets: %d/%d found", foundCount, static_cast<int>(offsets.size()));

        if (!g_dumper->GetLastError().empty()) {
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Error: %s", g_dumper->GetLastError().c_str());
        }
    }
}

void GUI::RenderAboutWindow() {
    if (!ImGui::Begin("About", &m_showAbout, ImGuiWindowFlags_AlwaysAutoResize)) {
        ImGui::End();
        return;
    }

    ImGui::Text("FiveM Offset Dumper");
    ImGui::Text("Version 1.0");
    ImGui::Separator();
    ImGui::Text("Professional offset dumping tool for FiveM");
    ImGui::Text("Created for educational purposes");
    ImGui::Spacing();
    ImGui::Text("Controls:");
    ImGui::BulletText("INSERT - Toggle GUI");
    ImGui::BulletText("END - Exit");

    if (ImGui::Button("Close")) {
        m_showAbout = false;
    }

    ImGui::End();
}

std::string GUI::FormatAddress(uintptr_t address) {
    std::stringstream ss;
    ss << "0x" << std::hex << std::uppercase << address;
    return ss.str();
}

ImVec4 GUI::GetStatusColor(bool found) {
    return found ? ImVec4(0.0f, 1.0f, 0.0f, 1.0f) : ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
}

void GUI::ShowHelpMarker(const char* desc) {
    ImGui::TextDisabled("(?)");
    if (ImGui::IsItemHovered()) {
        ImGui::BeginTooltip();
        ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
        ImGui::TextUnformatted(desc);
        ImGui::PopTextWrapPos();
        ImGui::EndTooltip();
    }
}
