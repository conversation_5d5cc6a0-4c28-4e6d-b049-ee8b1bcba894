@echo off
echo Building Simple Injector...

REM Criar diretório de build
if not exist "build" mkdir build
cd build

echo Configuring project...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

cd ..

echo Build completed successfully!
echo Output: SimpleInjector.exe

if exist "..\SimpleInjector.exe" (
    echo Injector found in root directory.
) else (
    echo Warning: Injector not found in expected location.
)

pause
