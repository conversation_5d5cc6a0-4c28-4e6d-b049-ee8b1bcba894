@echo off
echo Compilando FiveMCheat_FINAL.dll...

REM Configurar ambiente Visual Studio
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

REM Compilar DLL
cl.exe /LD /EHsc simple_cheat_final.cpp /Fe:FiveMCheat_FINAL.dll /link user32.lib kernel32.lib

if exist "FiveMCheat_FINAL.dll" (
    echo [OK] FiveMCheat_FINAL.dll criada com sucesso!
    copy "FiveMCheat_FINAL.dll" "..\FiveMCheat_FINAL.dll"
    echo [OK] DLL copiada para pasta raiz
    dir FiveMCheat_FINAL.dll
) else (
    echo [ERROR] Falha na compilacao
)

pause
