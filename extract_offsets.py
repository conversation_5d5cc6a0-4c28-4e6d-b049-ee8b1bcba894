#!/usr/bin/env python3
"""
Script para extrair offsets dos arquivos JSON do FiveM Offset Dumper
e gerar um header C++ atualizado
"""

import json
import os
import sys
from pathlib import Path

def extract_offset_from_json(json_file):
    """Extrai offset de um arquivo JSON"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Procurar por offsets no JSON
        for key, value in data.items():
            if isinstance(value, dict):
                for process, info in value.items():
                    if process == "FiveM_GameProcess.exe" and "offset" in info:
                        offset = info["offset"]
                        # Remover 0x se presente e converter para int
                        if offset.startswith("0x"):
                            offset = offset[2:]
                        return key, int(offset, 16)
        return None, None
    except Exception as e:
        print(f"Erro ao processar {json_file}: {e}")
        return None, None

def generate_cpp_header(offsets_dict):
    """Gera header C++ com os offsets"""
    header = '''/*
 * FiveM Offsets Header
 * Gerado automaticamente dos arquivos JSON do FiveM Offset Dumper
 * 
 * IMPORTANTE: Estes são offsets REAIS extraídos automaticamente!
 */

#pragma once
#include <cstdint>

namespace FiveMOffsets {
    // Base do módulo (será definida em runtime)
    extern uintptr_t MODULE_BASE;
    
    namespace Functions {
        // Funções importantes do FiveM/GTA V
'''
    
    # Adicionar funções
    functions = [
        "GetPlayerPed", "GetAllPhysicalPlayers", "GetNumPhysicalPlayers",
        "GetNetObjById", "GetNetObjType", "GetNetworkObject", "GetScriptEntity",
        "DeleteObject", "DeletePedReal", "DeleteVehicle", "RepairVehicle",
        "GiveAmmo", "HasModelLoaded", "MarkAsNoLongerNeeded"
    ]
    
    for func in functions:
        if func in offsets_dict:
            header += f'        constexpr uintptr_t {func} = 0x{offsets_dict[func]:X};\n'
    
    header += '''    }
    
    namespace Globals {
        // Variáveis globais importantes
'''
    
    # Adicionar globais
    globals_list = [
        "g_vehiclePool", "g_viewportGame", "g_pedDensity", "g_vehicleDensity",
        "g_parkedVehicleDensity", "g_pedScenarioDensity", "g_ambientPedRange",
        "g_ambientVehicleRangeMultiplier", "g_fireInstances", "g_trainConfigData"
    ]
    
    for global_var in globals_list:
        if global_var in offsets_dict:
            header += f'        constexpr uintptr_t {global_var} = 0x{offsets_dict[global_var]:X};\n'
    
    header += '''    }
    
    namespace Classes {
        // Classes e estruturas importantes
'''
    
    # Adicionar classes
    classes = [
        "CWorld", "CCamera", "CViewPort", "CPedFactory", "CReplayInterface"
    ]
    
    for cls in classes:
        if cls in offsets_dict:
            header += f'        constexpr uintptr_t {cls} = 0x{offsets_dict[cls]:X};\n'
    
    header += '''    }
    
    namespace Modifiers {
        // Modificadores de gameplay
'''
    
    # Adicionar modificadores
    modifiers = [
        "WeaponDamageModifier", "WeaponDamageModifier2", "WeaponDefenseModifier",
        "WeaponDefenseModifier2", "MeleeWeaponDamageModifier", "MeleeWeaponDefenseModifier",
        "VehicleDamageModifier", "VehicleDefenseModifier", "VehicleTopSpeedModifier",
        "VehicleCheatPowerIncrease", "CurrentWeaponDamageModifier"
    ]
    
    for mod in modifiers:
        if mod in offsets_dict:
            header += f'        constexpr uintptr_t {mod} = 0x{offsets_dict[mod]:X};\n'
    
    header += '''    }
    
    namespace Flags {
        // Flags e estados
'''
    
    # Adicionar flags
    flags = [
        "InfiniteAmmo", "NoReload", "IsClone", "PedFlags", "Handbrake",
        "IsNetworkGame", "ExpandedRadar", "RevealFullMap"
    ]
    
    for flag in flags:
        if flag in offsets_dict:
            header += f'        constexpr uintptr_t {flag} = 0x{offsets_dict[flag]:X};\n'
    
    header += '''    }
    
    namespace UI {
        // Interface e HUD
'''
    
    # Adicionar UI
    ui_elements = [
        "HudComponentsArray", "HudComponentsCount", "MaxHudColours",
        "MinimapArray", "MinimapIsRect", "WaypointPoint"
    ]
    
    for ui in ui_elements:
        if ui in offsets_dict:
            header += f'        constexpr uintptr_t {ui} = 0x{offsets_dict[ui]:X};\n'
    
    header += '''    }
    
    namespace Player {
        // Offsets dentro da estrutura do jogador (baseados no GTA V)
        constexpr uintptr_t PlayerCoords = 0x90;            // Coordenadas do ped
        constexpr uintptr_t PlayerHealth = 0x280;           // Vida do ped
        constexpr uintptr_t PlayerArmor = 0x14C;            // Armadura do ped
        constexpr uintptr_t PlayerStamina = 0x10B4;         // Stamina
        constexpr uintptr_t PlayerMaxStamina = 0x10C4;      // Stamina máxima
        constexpr uintptr_t PlayerFlags = 0x188;            // Flags do jogador
        constexpr uintptr_t CurrentWeapon = 0x20;           // Arma atual
    }
    
    namespace Vehicle {
        // Offsets dentro da estrutura do veículo (baseados no GTA V)
        constexpr uintptr_t VehicleCoords = 0x90;           // Coordenadas do veículo
        constexpr uintptr_t VehicleSpeed = 0x7C;            // Velocidade
        constexpr uintptr_t VehicleHealth = 0x280;          // Vida do veículo
        constexpr uintptr_t VehicleEngineHealth = 0x908;    // Saúde do motor
        constexpr uintptr_t VehicleGravity = 0xBC;          // Gravidade
        constexpr uintptr_t VehicleHandbrake = 0x9C4;       // Freio de mão
        constexpr uintptr_t VehicleSteeringScale = 0x9CC;   // Escala de direção
    }
    
    namespace Weapon {
        // Offsets dentro da estrutura da arma (baseados no GTA V)
        constexpr uintptr_t WeaponAmmo = 0x18;              // Munição da arma
        constexpr uintptr_t WeaponClipAmmo = 0x1C;          // Munição no pente
        constexpr uintptr_t WeaponRecoil = 0x2C;            // Recuo da arma
        constexpr uintptr_t WeaponSpread = 0x74;            // Dispersão
        constexpr uintptr_t WeaponRange = 0x78;             // Alcance
    }
}

/*
 * COMO USAR:
 * 
 * 1. Compile o cheat: cd examples && build_cheat.bat
 * 2. Injete: SimpleInjector.exe
 * 3. Controles:
 *    - INSERT: Menu
 *    - F1: ESP
 *    - F2: God Mode
 * 
 * Offsets extraídos automaticamente dos arquivos JSON!
 */
'''
    
    return header

def main():
    """Função principal"""
    print("FiveM Offset Extractor v1.0")
    print("=" * 40)
    
    # Caminho para os arquivos JSON
    json_dir = Path("FiveM-Offsets-Dumper-main/FiveM-Offsets-Dumper-main")
    
    if not json_dir.exists():
        print(f"Erro: Diretório {json_dir} não encontrado!")
        print("Certifique-se de que os arquivos JSON estão no local correto.")
        return
    
    print(f"Procurando arquivos JSON em: {json_dir}")
    
    offsets_dict = {}
    processed_files = 0
    
    # Processar todos os arquivos JSON
    for json_file in json_dir.rglob("*.json"):
        name, offset = extract_offset_from_json(json_file)
        if name and offset:
            offsets_dict[name] = offset
            processed_files += 1
            print(f"✓ {name}: 0x{offset:X}")
    
    print(f"\nProcessados {processed_files} arquivos JSON")
    print(f"Encontrados {len(offsets_dict)} offsets válidos")
    
    if not offsets_dict:
        print("Nenhum offset encontrado!")
        return
    
    # Gerar header
    print("\nGerando header C++...")
    header_content = generate_cpp_header(offsets_dict)
    
    # Salvar header
    output_file = "examples/offsets_auto.h"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print(f"✓ Header salvo em: {output_file}")
    
    # Também salvar uma cópia de backup
    backup_file = "offsets_backup.h"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print(f"✓ Backup salvo em: {backup_file}")
    
    print("\nConcluído! Agora você pode:")
    print("1. Usar offsets_auto.h no lugar de offsets.h")
    print("2. Recompilar o cheat com os novos offsets")
    print("3. Testar as funcionalidades")

if __name__ == "__main__":
    main()
