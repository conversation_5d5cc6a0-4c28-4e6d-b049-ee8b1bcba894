{"gta-streaming-five.dll": {"<lambda_2730b6c68f930e168344f035418efbb7>::~<lambda_2730b6c68f930e168344f035418efbb7>": {"offset": "0x22BF0"}, "<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>::~<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>": {"offset": "0x22BF0"}, "<lambda_41af1ba482b752c39807db2ef15caf48>::~<lambda_41af1ba482b752c39807db2ef15caf48>": {"offset": "0x16850"}, "<lambda_4bd345ce870bcb6a9dba57dde6595eaf>::<lambda_4bd345ce870bcb6a9dba57dde6595eaf>": {"offset": "0x85E90"}, "<lambda_55b0750980631035f31e5def29397b5e>::~<lambda_55b0750980631035f31e5def29397b5e>": {"offset": "0x5F520"}, "<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x1D9C0"}, "<lambda_7604da78e8f33a312c72ae5a43f9b93b>::~<lambda_7604da78e8f33a312c72ae5a43f9b93b>": {"offset": "0x1D9C0"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x1D9C0"}, "<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>::~<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>": {"offset": "0x22BF0"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0x16850"}, "<lambda_cece5b4357793226365050ea62567114>::~<lambda_cece5b4357793226365050ea62567114>": {"offset": "0x5F5D0"}, "<lambda_e4ba7588b360fe652a3faff73b7b991b>::~<lambda_e4ba7588b360fe652a3faff73b7b991b>": {"offset": "0x5F5D0"}, "<lambda_f17ebabe33bc32be107ce6fff046b802>::~<lambda_f17ebabe33bc32be107ce6fff046b802>": {"offset": "0x1D9C0"}, "AddCollisionWrap": {"offset": "0x279C0"}, "AddCrashometry": {"offset": "0x808B0"}, "AddRawStreamer": {"offset": "0x71D00"}, "AddStreamingFileWrap": {"offset": "0x74CA0"}, "AddVehicleArchetype": {"offset": "0x3F4B0"}, "AllocateBuffer": {"offset": "0x84BD0"}, "AppendToCacheWrap": {"offset": "0x247D0"}, "AssignFontLibWrap": {"offset": "0x799D0"}, "BvhSet": {"offset": "0x279D0"}, "CDistantLODLight::CDistantLODLight": {"offset": "0x1D020"}, "CDistantLODLight::~CDistantLODLight": {"offset": "0x1DDA0"}, "CEntityDef::CEntityDef": {"offset": "0x1D080"}, "CEntityDef::~CEntityDef": {"offset": "0x1DE00"}, "CEntity_SetModelIdWrap": {"offset": "0x60970"}, "CMapData::CMapData": {"offset": "0x1D0E0"}, "CMapData::CreateMapDataContents": {"offset": "0x203F0"}, "CMapData::~CMapData": {"offset": "0x1DE50"}, "CMapTypes::CMapTypes": {"offset": "0x1D280"}, "CMapTypes::~CMapTypes": {"offset": "0x1DDA0"}, "CMapTypes_Load": {"offset": "0x1FE70"}, "CMapTypes__ConstructLocalExtensions": {"offset": "0x58A30"}, "CVehicle::CVehicle": {"offset": "0x1D370"}, "CVehicle::GetHandlingData": {"offset": "0x20550"}, "CVehicle::GetSeatManager": {"offset": "0x6A050"}, "CVehicle::SetHandlingData": {"offset": "0x20930"}, "CVehicle::~CVehicle": {"offset": "0x16730"}, "CVehicleStreamRenderGfx_GetFragment": {"offset": "0x29AA0"}, "CalculateMipLevelHook": {"offset": "0x76690"}, "CancelRequestWrap": {"offset": "0x60B90"}, "CfxCacheMounter::LoadDataFile": {"offset": "0x2EB30"}, "CfxCacheMounter::UnloadDataFile": {"offset": "0x16730"}, "CfxCollection_AddStreamingFileByTag": {"offset": "0x42400"}, "CfxCollection_BackoutStreamingTag": {"offset": "0x42970"}, "CfxCollection_RemoveStreamingTag": {"offset": "0x42CC0"}, "CfxCollection_SetStreamingLoadLocked": {"offset": "0x43740"}, "CfxPackfileMounter::LoadDataFile": {"offset": "0x2F730"}, "CfxPackfileMounter::UnloadDataFile": {"offset": "0x2F780"}, "CfxProxyInteriorOrderMounter::LoadDataFile": {"offset": "0x2F3B0"}, "CfxProxyInteriorOrderMounter::UnloadDataFile": {"offset": "0x2F3D0"}, "CfxProxyItypMounter::LoadDataFile": {"offset": "0x2EB60"}, "CfxProxyItypMounter::ParseBaseName": {"offset": "0x4E290"}, "CfxProxyItypMounter::UnloadDataFile": {"offset": "0x2F000"}, "CfxPseudoMounter::LoadDataFile": {"offset": "0x2EA70"}, "CfxPseudoMounter::UnloadDataFile": {"offset": "0x2EAA0"}, "CfxState::CfxState": {"offset": "0x7FEF0"}, "CheckForDuplicateArchetypes": {"offset": "0x2B170"}, "CleanupStreaming": {"offset": "0x43760"}, "CompTrace": {"offset": "0x77630"}, "Component::As": {"offset": "0x21030"}, "Component::IsA": {"offset": "0x28780"}, "Component::SetCommandLine": {"offset": "0x16730"}, "Component::SetUserData": {"offset": "0x28790"}, "ComponentInstance::DoGameLoad": {"offset": "0x28760"}, "ComponentInstance::Initialize": {"offset": "0x28770"}, "ComponentInstance::Shutdown": {"offset": "0x28790"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x64ED0"}, "ConsoleArgumentType<int,void>::Parse": {"offset": "0x5BA50"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0x63340"}, "ConsoleCommand::ConsoleCommand<<lambda_41af1ba482b752c39807db2ef15caf48> >": {"offset": "0x591B0"}, "ConsoleCommand::ConsoleCommand<<lambda_98135fb5c535408e12d17fbabc55ae8c> >": {"offset": "0x30440"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0x63520"}, "ConsoleCommand::ConsoleCommand<<lambda_cd07e93ce4e084374db77c62c4f7d117> >": {"offset": "0x59410"}, "ConsoleCommand::ConsoleCommand<<lambda_eae4a17f64911edbb9552e7485b4bbb0> >": {"offset": "0x222C0"}, "ConsoleCommand::ConsoleCommand<<lambda_fe88ff6dbc907fb0feeae768dbc364ca> >": {"offset": "0x72960"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x5A1F0"}, "ConsoleFlagsToString": {"offset": "0x5B330"}, "CoreGetComponentRegistry": {"offset": "0x279F0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x27A80"}, "CreateComponent": {"offset": "0x287A0"}, "CreatePopulationPedWrap": {"offset": "0x6C570"}, "CreateTrampolineFunction": {"offset": "0x84F20"}, "CreateVariableEntry<bool>": {"offset": "0x639F0"}, "CreateVariableEntry<int>": {"offset": "0x59940"}, "DirectX::BitsPerPixel": {"offset": "0x88C20"}, "DirectX::ComputePitch": {"offset": "0x88D80"}, "DirectX::D3DXDecodeBC1": {"offset": "0x8D9D0"}, "DirectX::D3DXDecodeBC2": {"offset": "0x8DAC0"}, "DirectX::D3DXDecodeBC3": {"offset": "0x8DE60"}, "DirectX::D3DXDecodeBC4S": {"offset": "0x8E380"}, "DirectX::D3DXDecodeBC4U": {"offset": "0x8E3F0"}, "DirectX::D3DXDecodeBC5S": {"offset": "0x8E440"}, "DirectX::D3DXDecodeBC5U": {"offset": "0x8E4D0"}, "DirectX::D3DXDecodeBC6HS": {"offset": "0x8E820"}, "DirectX::D3DXDecodeBC6HU": {"offset": "0x8E830"}, "DirectX::D3DXDecodeBC7": {"offset": "0x8E840"}, "DirectX::Decompress": {"offset": "0x88570"}, "DirectX::Internal::round_to_nearest": {"offset": "0x8D8D0"}, "DirectX::IsPlanar": {"offset": "0x89220"}, "DirectX::IsTypeless": {"offset": "0x89280"}, "DirectX::LDRColorA::InterpolateRGB": {"offset": "0x8FC80"}, "DirectX::ScratchImage::GetImage": {"offset": "0x87DE0"}, "DirectX::ScratchImage::Initialize2D": {"offset": "0x87E90"}, "DirectX::ScratchImage::Release": {"offset": "0x88070"}, "DirectX::ScratchImage::~ScratchImage": {"offset": "0x28A90"}, "DirectX::_CalculateMipLevels": {"offset": "0x8D940"}, "DirectX::_ConvertScanline": {"offset": "0x898A0"}, "DirectX::_DetermineImageArray": {"offset": "0x880D0"}, "DirectX::_SetupImageArray": {"offset": "0x882A0"}, "DirectX::_StoreScanline": {"offset": "0x8A8A0"}, "DisplayRawStreamerError": {"offset": "0x448E0"}, "DllMain": {"offset": "0x9173C"}, "DoBVHThing": {"offset": "0x6D510"}, "DoBeforeGetEntries": {"offset": "0x69C50"}, "DoGeomThing": {"offset": "0x27B10"}, "DoGeometryThing": {"offset": "0x6D530"}, "DoNtRaiseException": {"offset": "0x82340"}, "EnableAllHooksLL": {"offset": "0x84130"}, "EnableHook": {"offset": "0x84280"}, "EnableHookLL": {"offset": "0x843A0"}, "EntityLogDamage": {"offset": "0x2DE00"}, "ErrorInflateFailure": {"offset": "0x778B0"}, "ExecuteGroupForWeaponInfo": {"offset": "0x46670"}, "FatalErrorNoExceptRealV": {"offset": "0x17E00"}, "FatalErrorRealV": {"offset": "0x17E30"}, "FilterUnmountOperation": {"offset": "0x46890"}, "FindExportedResource": {"offset": "0x79C60"}, "FlushCustomAssets": {"offset": "0x46970"}, "ForceMountDataFile": {"offset": "0x46AE0"}, "ForcedDevice::Close": {"offset": "0x44050"}, "ForcedDevice::CloseBulk": {"offset": "0x44060"}, "ForcedDevice::Create": {"offset": "0x44570"}, "ForcedDevice::CreateLocal": {"offset": "0x44580"}, "ForcedDevice::GetCollectionId": {"offset": "0x47990"}, "ForcedDevice::GetFileAttributesW": {"offset": "0x479E0"}, "ForcedDevice::GetFileLength": {"offset": "0x47A00"}, "ForcedDevice::GetFileLengthLong": {"offset": "0x47A10"}, "ForcedDevice::GetFileLengthUInt64": {"offset": "0x47A30"}, "ForcedDevice::GetFileTime": {"offset": "0x47A40"}, "ForcedDevice::GetName": {"offset": "0x47AA0"}, "ForcedDevice::GetResourceVersion": {"offset": "0x47B60"}, "ForcedDevice::IsCollection": {"offset": "0x48470"}, "ForcedDevice::Open": {"offset": "0x4E240"}, "ForcedDevice::OpenBulk": {"offset": "0x4E260"}, "ForcedDevice::OpenBulkWrap": {"offset": "0x4E280"}, "ForcedDevice::Read": {"offset": "0x4EB40"}, "ForcedDevice::ReadBulk": {"offset": "0x4EB50"}, "ForcedDevice::RemoveFile": {"offset": "0x28780"}, "ForcedDevice::RenameFile": {"offset": "0x21030"}, "ForcedDevice::Seek": {"offset": "0x51E10"}, "ForcedDevice::SeekLong": {"offset": "0x51E20"}, "ForcedDevice::SetFileTime": {"offset": "0x28780"}, "ForcedDevice::Write": {"offset": "0x52E90"}, "ForcedDevice::WriteBulk": {"offset": "0x52E90"}, "ForcedDevice::m_xy": {"offset": "0x56780"}, "ForcedDevice::m_yx": {"offset": "0x567A0"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0xE560"}, "FreeArchetypesHook": {"offset": "0x46CA0"}, "FreeBuffer": {"offset": "0x84C00"}, "Freeze": {"offset": "0x844B0"}, "GFxValue::GetString": {"offset": "0x71160"}, "GFxValue::~GFxValue": {"offset": "0x70420"}, "GetAbsoluteCitPath": {"offset": "0x80A30"}, "GetBaseName": {"offset": "0x47540"}, "GetBlockMapWrap": {"offset": "0x75450"}, "GetBoneIndexFromId": {"offset": "0x5E2F0"}, "GetBudgetVar": {"offset": "0x5B770"}, "GetCacheIndex": {"offset": "0x24B60"}, "GetCurrentMapGroup": {"offset": "0x29E00"}, "GetCurrentStreamingIndex": {"offset": "0x6D550"}, "GetCurrentStreamingName": {"offset": "0x6D570"}, "GetDummyCollectionIndexByTag": {"offset": "0x24C40"}, "GetDummyStreamingPackfileByTag": {"offset": "0x24C60"}, "GetEventName": {"offset": "0x2DE90"}, "GetExportedResource": {"offset": "0x79D50"}, "GetFontById": {"offset": "0x79E40"}, "GetGlobalMatrix": {"offset": "0x5E310"}, "GetGlobalMatrixFuncPatch": {"offset": "0x5E380"}, "GetGlobalTransform": {"offset": "0x5E3C0"}, "GetMapTypeDefName": {"offset": "0x2B620"}, "GetMaxSpeed<&origBase>": {"offset": "0x5C290"}, "GetMaxSpeed<&origDamp>": {"offset": "0x5C2C0"}, "GetMaxSpeed<&origPhys>": {"offset": "0x5C2F0"}, "GetMemoryBlock": {"offset": "0x84C80"}, "GetPackIndex": {"offset": "0x25060"}, "GetRcdDebugInfoExtension::~GetRcdDebugInfoExtension": {"offset": "0x3ACE0"}, "GetStreamingModuleWithValidate<0>": {"offset": "0x72EB0"}, "GetStreamingModuleWithValidate<1>": {"offset": "0x72B30"}, "GetTxdRelationships": {"offset": "0x47B80"}, "GlobalErrorHandler": {"offset": "0x18070"}, "HandleDataFile": {"offset": "0x47E30"}, "HandleEventWrap<&g_eventCall3>": {"offset": "0x2C4B0"}, "HandleEventWrapExt<&g_eventCall1,0>": {"offset": "0x2C5E0"}, "HandleEventWrapExt<&g_eventCall2,1>": {"offset": "0x2C8D0"}, "HandleSprite": {"offset": "0x79F70"}, "HookFunction::Run": {"offset": "0x1A900"}, "HookFunctionBase::Register": {"offset": "0x831F0"}, "HookFunctionBase::RunAll": {"offset": "0x83210"}, "Hook_StreamingSema": {"offset": "0x60CF0"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x7FB20"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x7FFB0"}, "InflateFailureBaseGame": {"offset": "0x78390"}, "InitFunction::Run": {"offset": "0x1A7F0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x82140"}, "InitFunctionBase::Register": {"offset": "0x824B0"}, "InitFunctionBase::RunAll": {"offset": "0x82500"}, "InitializeBuffer": {"offset": "0x16730"}, "InitializeIPLBuffer": {"offset": "0x5D1D0"}, "InsertStreamingModuleWrap": {"offset": "0x6D5C0"}, "Instance<ICoreGameInit>::Get": {"offset": "0x474E0"}, "IsCodePadding": {"offset": "0x852D0"}, "IsErrorException": {"offset": "0x18710"}, "IsEvaluationServer": {"offset": "0x484A0"}, "IsExecutableAddress": {"offset": "0x84EE0"}, "IsHandleCache": {"offset": "0x60F90"}, "IsPedSkeletonApplicable": {"offset": "0x65A40"}, "IterateDataFiles": {"offset": "0x6A0C0"}, "LoadAllMapTypesFiles": {"offset": "0x206F0"}, "LoadArchetypeFiles": {"offset": "0x6A200"}, "LoadCache": {"offset": "0x25140"}, "LoadCacheHook": {"offset": "0x253A0"}, "LoadDataFiles": {"offset": "0x4AA20"}, "LoadDats": {"offset": "0x4AE70"}, "LoadDefDats": {"offset": "0x4AF40"}, "LoadManifest": {"offset": "0x4B010"}, "LoadReplayDlc": {"offset": "0x4B9E0"}, "LoadStreamingFiles": {"offset": "0x4BA30"}, "LoadVehicleMetaForDlc": {"offset": "0x4D750"}, "LookupDataFileMounter": {"offset": "0x4DBC0"}, "LookupDataFileType": {"offset": "0x4DCC0"}, "MH_CreateHook": {"offset": "0x84660"}, "MH_EnableHook": {"offset": "0x848F0"}, "MH_Initialize": {"offset": "0x84900"}, "MakeDefragmentableHook": {"offset": "0x759F0"}, "MakeRelativeCitPath": {"offset": "0x18730"}, "MapTypesFile::MapTypesFile": {"offset": "0x1D4F0"}, "MapTypesFile::~MapTypesFile": {"offset": "0x1DF10"}, "ModifyHierarchyStatusHook": {"offset": "0x4DFA0"}, "NoLSN": {"offset": "0x61300"}, "NormalizePath": {"offset": "0x4E1E0"}, "ObfuscatedDevice::Close": {"offset": "0x44050"}, "ObfuscatedDevice::CloseBulk": {"offset": "0x44060"}, "ObfuscatedDevice::Create": {"offset": "0x44570"}, "ObfuscatedDevice::CreateLocal": {"offset": "0x44580"}, "ObfuscatedDevice::GetCollectionId": {"offset": "0x47990"}, "ObfuscatedDevice::GetFileAttributesW": {"offset": "0x479E0"}, "ObfuscatedDevice::GetFileLength": {"offset": "0x47A00"}, "ObfuscatedDevice::GetFileLengthLong": {"offset": "0x47A10"}, "ObfuscatedDevice::GetFileLengthUInt64": {"offset": "0x47A30"}, "ObfuscatedDevice::GetFileTime": {"offset": "0x47A40"}, "ObfuscatedDevice::GetName": {"offset": "0x47AA0"}, "ObfuscatedDevice::GetResourceVersion": {"offset": "0x47B60"}, "ObfuscatedDevice::GetUnkDevice": {"offset": "0x6ECF0"}, "ObfuscatedDevice::IsCollection": {"offset": "0x48470"}, "ObfuscatedDevice::Open": {"offset": "0x4E240"}, "ObfuscatedDevice::OpenBulk": {"offset": "0x4E260"}, "ObfuscatedDevice::OpenBulkWrap": {"offset": "0x4E280"}, "ObfuscatedDevice::Read": {"offset": "0x4EB40"}, "ObfuscatedDevice::ReadBulk": {"offset": "0x4EB50"}, "ObfuscatedDevice::RemoveFile": {"offset": "0x28780"}, "ObfuscatedDevice::RenameFile": {"offset": "0x21030"}, "ObfuscatedDevice::Seek": {"offset": "0x51E10"}, "ObfuscatedDevice::SeekLong": {"offset": "0x51E20"}, "ObfuscatedDevice::SetFileTime": {"offset": "0x28780"}, "ObfuscatedDevice::Write": {"offset": "0x52E90"}, "ObfuscatedDevice::WriteBulk": {"offset": "0x52E90"}, "ObfuscatedDevice::m_40": {"offset": "0x6ECE0"}, "ObfuscatedDevice::m_ax": {"offset": "0x6ED10"}, "ObfuscatedDevice::m_xx": {"offset": "0x6ED00"}, "ObfuscatedDevice::m_xy": {"offset": "0x6ED20"}, "ObfuscatedDevice::m_yx": {"offset": "0x567A0"}, "OnEntityTakeDmg": {"offset": "0x2DFB0"}, "OverlayMethodFunctionHandler::Call": {"offset": "0x6FAA0"}, "ParseArchetypeFile": {"offset": "0x6A610"}, "ParserCreateAndLoadAnyType": {"offset": "0x4E360"}, "PedDecorationManager::Comparator": {"offset": "0x648C0"}, "PolyErrorv": {"offset": "0x6D730"}, "ProcessHandler<ByteAwaiter>": {"offset": "0x5EFF0"}, "ProcessHandler<SemaAwaiter>": {"offset": "0x5F070"}, "ProcessRemoval": {"offset": "0x61310"}, "ProcessThreadIPs": {"offset": "0x849A0"}, "PtrError": {"offset": "0x27B90"}, "RaiseDebugException": {"offset": "0x82420"}, "RegisterPeds": {"offset": "0x4EB60"}, "RegisterStreamingFileStrchrWrap": {"offset": "0x27D00"}, "ReloadMapStore": {"offset": "0x4EDF0"}, "RelocateRelative": {"offset": "0x5D210"}, "RequestHandleExtension::~RequestHandleExtension": {"offset": "0x5F890"}, "ReturnTrue": {"offset": "0x78440"}, "RunCompatibilityBehavior": {"offset": "0x6C000"}, "SMPACreateStub": {"offset": "0x78450"}, "SafeCall<<lambda_5cd72400bc154858438f9b9dbbf12639> >": {"offset": "0x31C00"}, "SafelyDrainStreamer": {"offset": "0x51BB0"}, "SafetyFilter": {"offset": "0x7A400"}, "SceneLoaderScan": {"offset": "0x784F0"}, "ScopedError::~ScopedError": {"offset": "0x16920"}, "SehRoutine": {"offset": "0x51E30"}, "SetArchetypeModelId": {"offset": "0x2BD40"}, "SetCurrentStreamingModuleCallback": {"offset": "0x6D970"}, "SetGamePhysicalBudget": {"offset": "0x5BCA0"}, "SetHandleDownloadWeight": {"offset": "0x61750"}, "SetPedProp": {"offset": "0x5C6E0"}, "SetStreamedPedComponentVariation": {"offset": "0x5C790"}, "SetToLoadingWrap": {"offset": "0x61850"}, "SettingsVramTex": {"offset": "0x5C150"}, "SetupTerritories": {"offset": "0x713C0"}, "StreamingDataEntry::ComputePhysicalSize": {"offset": "0x71D80"}, "StreamingDataEntry::ComputeVirtualSize": {"offset": "0x71D90"}, "SysError": {"offset": "0x18CB0"}, "TattooSort": {"offset": "0x65350"}, "ToNarrow": {"offset": "0x82530"}, "ToWide": {"offset": "0x82620"}, "TraceReal<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x31C40"}, "TraceReal<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int>": {"offset": "0x31CF0"}, "TraceRealV": {"offset": "0x82930"}, "Unfreeze": {"offset": "0x84B50"}, "UnloadVehicleMetaForDlc": {"offset": "0x52890"}, "UnloadWeaponInfosStub": {"offset": "0x52B60"}, "UpdateFontLoading": {"offset": "0x7A4F0"}, "ValidateGeometry": {"offset": "0x6D990"}, "Win32TrapAndJump64": {"offset": "0x84110"}, "WrapAddMapBoolEntry": {"offset": "0x52DD0"}, "WrapAssetRelease": {"offset": "0x75AD0"}, "WrapStreamingLoad": {"offset": "0x6E840"}, "WriteArchetypesFile": {"offset": "0x20BC0"}, "_DllMainCRTStartup": {"offset": "0x911A0"}, "_Init_thread_abort": {"offset": "0x90394"}, "_Init_thread_footer": {"offset": "0x903C4"}, "_Init_thread_header": {"offset": "0x90424"}, "_Init_thread_notify": {"offset": "0x9048C"}, "_Init_thread_wait": {"offset": "0x904D0"}, "_RTC_Initialize": {"offset": "0x9178C"}, "_RTC_Terminate": {"offset": "0x917C8"}, "_Smtx_lock_exclusive": {"offset": "0x9025C"}, "_Smtx_lock_shared": {"offset": "0x90264"}, "_Smtx_unlock_exclusive": {"offset": "0x9026C"}, "_Smtx_unlock_shared": {"offset": "0x90274"}, "__ArrayUnwind": {"offset": "0x90C78"}, "__GSHandlerCheck": {"offset": "0x90AB0"}, "__GSHandlerCheckCommon": {"offset": "0x90AD0"}, "__GSHandlerCheck_EH": {"offset": "0x90B2C"}, "__GSHandlerCheck_SEH": {"offset": "0x90DF8"}, "__chkstk": {"offset": "0x90DA0"}, "__crt_debugger_hook": {"offset": "0x91510"}, "__dyn_tls_init": {"offset": "0x908F8"}, "__dyn_tls_on_demand_init": {"offset": "0x90960"}, "__global_delete": {"offset": "0x21740"}, "__isa_available_init": {"offset": "0x9135C"}, "__local_stdio_printf_options": {"offset": "0x1A710"}, "__local_stdio_scanf_options": {"offset": "0x91760"}, "__raise_securityfailure": {"offset": "0x911E0"}, "__report_gsfailure": {"offset": "0x91214"}, "__scrt_acquire_startup_lock": {"offset": "0x90578"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x905B4"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x905E8"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x90600"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x90628"}, "__scrt_dllmain_exception_filter": {"offset": "0x90640"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x906A0"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x906D0"}, "__scrt_fastfail": {"offset": "0x91518"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x91784"}, "__scrt_initialize_crt": {"offset": "0x906E4"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x91768"}, "__scrt_initialize_onexit_tables": {"offset": "0x90730"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x9029C"}, "__scrt_initialize_type_info": {"offset": "0x90CE4"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x907BC"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x969BD"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x91684"}, "__scrt_release_startup_lock": {"offset": "0x90854"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x28790"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x28790"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x28790"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x28790"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x28790"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0x21030"}, "__scrt_throw_std_bad_alloc": {"offset": "0x91664"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0x194B0"}, "__scrt_uninitialize_crt": {"offset": "0x90878"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x9036C"}, "__scrt_uninitialize_type_info": {"offset": "0x90CF4"}, "__security_check_cookie": {"offset": "0x90BC0"}, "__security_init_cookie": {"offset": "0x91690"}, "__std_find_trivial_1": {"offset": "0x90000"}, "__std_find_trivial_8": {"offset": "0x900D0"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x90250"}, "_enableGroupHook": {"offset": "0x2A000"}, "_getAvailableMemoryForStreamer": {"offset": "0x5C1A0"}, "_get_startup_argv_mode": {"offset": "0x78440"}, "_guard_check_icall_nop": {"offset": "0x16730"}, "_guard_dispatch_icall_nop": {"offset": "0x91960"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x91980"}, "_onexit": {"offset": "0x908A4"}, "_wwassert": {"offset": "0x80D30"}, "`CompTrace'::`2'::<unnamed-type-errorBit>::InternalMain": {"offset": "0x76AB0"}, "`Hook_StreamingSema'::`6'::<unnamed-type-weirdStub>::InternalMain": {"offset": "0x5E8D0"}, "`Hook_StreamingSema'::`7'::<unnamed-type-weirdStub>::InternalMain": {"offset": "0x5E930"}, "`Hook_StreamingSema2699'::`3'::<unnamed-type-weirdStub>::InternalMain": {"offset": "0x5E810"}, "`Hook_StreamingSema2699'::`4'::<unnamed-type-weirdStub>::InternalMain": {"offset": "0x5E870"}, "`InsertStreamingModuleWrap'::`2'::StreamingOnLoadStub::InternalMain": {"offset": "0x6C7F0"}, "`SafeCall<<lambda_5cd72400bc154858438f9b9dbbf12639> >'::`1'::filt$0": {"offset": "0x92660"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x96A01"}, "`anonymous namespace'::BC4_SNORM::DecodeFromIndex": {"offset": "0x8E540"}, "`anonymous namespace'::BC4_UNORM::DecodeFromIndex": {"offset": "0x8E6C0"}, "`anonymous namespace'::CBits<16>::GetBits": {"offset": "0x8FBD0"}, "`anonymous namespace'::ConvertCompare": {"offset": "0x89340"}, "`anonymous namespace'::D3DX_BC6H::Decode": {"offset": "0x8E850"}, "`anonymous namespace'::D3DX_BC6H::Unquantize": {"offset": "0x8FF00"}, "`anonymous namespace'::D3DX_BC7::Decode": {"offset": "0x8F040"}, "`anonymous namespace'::DecompressBC": {"offset": "0x88740"}, "`anonymous namespace'::DefaultDecompress": {"offset": "0x88B80"}, "`anonymous namespace'::FloatTo6e4": {"offset": "0x89360"}, "`anonymous namespace'::FloatTo7e3": {"offset": "0x893D0"}, "`anonymous namespace'::INTColor::SignExtend": {"offset": "0x8FD60"}, "`anonymous namespace'::TransformInverse": {"offset": "0x8FDF0"}, "`anonymous namespace'::`dynamic initializer for 'g_WICFormats''": {"offset": "0xDD10"}, "`anonymous namespace'::msgpack_array<float [3]>": {"offset": "0x2CBE0"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x96AAA"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x96AC1"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x96ADA"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x96AEE"}, "`dynamic initializer for 'CVehicleStreamRenderGfx_GetShaderEffect''": {"offset": "0x2350"}, "`dynamic initializer for 'OnCreatePopulationPed''": {"offset": "0xB3F0"}, "`dynamic initializer for 'OnEntityDamaged''": {"offset": "0x2A70"}, "`dynamic initializer for 'OnRefreshArchetypesCollection''": {"offset": "0x13A0"}, "`dynamic initializer for 'OnRefreshArchetypesCollectionDone''": {"offset": "0x13B0"}, "`dynamic initializer for 'OnReloadMapStore''": {"offset": "0x3060"}, "`dynamic initializer for 'OnTriggerGameEvent''": {"offset": "0x2A80"}, "`dynamic initializer for 'OnTriggerGameEventExt''": {"offset": "0x2A90"}, "`dynamic initializer for 'SeenDuplicates''": {"offset": "0x25C0"}, "`dynamic initializer for '_IsObjectInImage''": {"offset": "0x2C60"}, "`dynamic initializer for '__GFxObjectInterface_CreateEmptyMovieClip''": {"offset": "0xBA90"}, "`dynamic initializer for '__GFxObjectInterface_GetDisplayInfo''": {"offset": "0xBAD0"}, "`dynamic initializer for '__GFxObjectInterface_GetMember''": {"offset": "0xBB10"}, "`dynamic initializer for '__GFxObjectInterface_Invoke''": {"offset": "0xBB50"}, "`dynamic initializer for '__GFxObjectInterface_ObjectRelease''": {"offset": "0xBB90"}, "`dynamic initializer for '__GFxObjectInterface_SetDisplayInfo''": {"offset": "0xBBD0"}, "`dynamic initializer for '_addPackfile''": {"offset": "0x3070"}, "`dynamic initializer for '_clearManifestChunk''": {"offset": "0x30B0"}, "`dynamic initializer for '_computePhysicalSize''": {"offset": "0xC300"}, "`dynamic initializer for '_computeVirtualSize''": {"offset": "0xC340"}, "`dynamic initializer for '_doesPedComponentDrawableExist''": {"offset": "0x9D90"}, "`dynamic initializer for '_doesPedPropExist''": {"offset": "0x9DD0"}, "`dynamic initializer for '_getAllPedArchetypes''": {"offset": "0x30F0"}, "`dynamic initializer for '_getDependents''": {"offset": "0xC380"}, "`dynamic initializer for '_getDependentsInner''": {"offset": "0xC3C0"}, "`dynamic initializer for '_getIndexByKey''": {"offset": "0x3130"}, "`dynamic initializer for '_getScaleformASRoot''": {"offset": "0xBC10"}, "`dynamic initializer for '_getScaleformMovie''": {"offset": "0xBC50"}, "`dynamic initializer for '_getStrIndicesForArchive''": {"offset": "0x2CA0"}, "`dynamic initializer for '_gfxMovieRoot_getLevelMovie''": {"offset": "0xD100"}, "`dynamic initializer for '_gfxPushInt''": {"offset": "0xBC90"}, "`dynamic initializer for '_gfxPushString''": {"offset": "0xBCD0"}, "`dynamic initializer for '_hashMapAdd''": {"offset": "0x2CE0"}, "`dynamic initializer for '_initManifestChunk''": {"offset": "0x3170"}, "`dynamic initializer for '_initVehiclePaintRamps''": {"offset": "0x31B0"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1000"}, "`dynamic initializer for '_init_instance_1''": {"offset": "0x16A0"}, "`dynamic initializer for '_init_instance_14''": {"offset": "0x1AF0"}, "`dynamic initializer for '_init_instance_2''": {"offset": "0x16D0"}, "`dynamic initializer for '_init_instance_22''": {"offset": "0x31F0"}, "`dynamic initializer for '_init_instance_23''": {"offset": "0x3220"}, "`dynamic initializer for '_init_instance_24''": {"offset": "0x3250"}, "`dynamic initializer for '_init_instance_25''": {"offset": "0x3280"}, "`dynamic initializer for '_init_instance_3''": {"offset": "0xAAE0"}, "`dynamic initializer for '_init_instance_33''": {"offset": "0xAF90"}, "`dynamic initializer for '_init_instance_34''": {"offset": "0xCCF0"}, "`dynamic initializer for '_init_instance_35''": {"offset": "0xCD20"}, "`dynamic initializer for '_init_instance_40''": {"offset": "0xA410"}, "`dynamic initializer for '_init_instance_41''": {"offset": "0xA440"}, "`dynamic initializer for '_init_instance_42''": {"offset": "0xC8A0"}, "`dynamic initializer for '_init_instance_43''": {"offset": "0x1700"}, "`dynamic initializer for '_init_instance_59''": {"offset": "0x13C0"}, "`dynamic initializer for '_init_instance_71''": {"offset": "0x32B0"}, "`dynamic initializer for '_isReadyToDelete''": {"offset": "0xC400"}, "`dynamic initializer for '_isResourceNotCached''": {"offset": "0x32E0"}, "`dynamic initializer for '_loadCacheFile''": {"offset": "0x1730"}, "`dynamic initializer for '_loadManifestChunk''": {"offset": "0x3320"}, "`dynamic initializer for '_mapD3DFMT''": {"offset": "0x1F90"}, "`dynamic initializer for '_parseCache''": {"offset": "0x1770"}, "`dynamic initializer for '_reloadMapIfNeeded''": {"offset": "0x3360"}, "`dynamic initializer for '_removePackfile''": {"offset": "0x33A0"}, "`dynamic initializer for '_resyncStreamers''": {"offset": "0x33E0"}, "`dynamic initializer for '_setupGfxCall''": {"offset": "0xBDA0"}, "`dynamic initializer for '_unloadTextureLODs''": {"offset": "0x3420"}, "`dynamic initializer for '_updateVideoMemoryBar''": {"offset": "0x99D0"}, "`dynamic initializer for '_waitUntilStreamerClear''": {"offset": "0x3460"}, "`dynamic initializer for 'addExtension''": {"offset": "0x2670"}, "`dynamic initializer for 'addToScene''": {"offset": "0xB040"}, "`dynamic initializer for 'atHashMap_bool_insert''": {"offset": "0x17B0"}, "`dynamic initializer for 'calculateBVH''": {"offset": "0x1B20"}, "`dynamic initializer for 'dataFileMgr__getNextEntry''": {"offset": "0xB080"}, "`dynamic initializer for 'fwAssetStoreBase__isResourceValid''": {"offset": "0xC900"}, "`dynamic initializer for 'fwEntityDef__instantiate''": {"offset": "0xB0C0"}, "`dynamic initializer for 'fwEntity__ProtectStreamedArchetype''": {"offset": "0x26B0"}, "`dynamic initializer for 'g_afterLevelMetas''": {"offset": "0x34A0"}, "`dynamic initializer for 'g_appendToCache''": {"offset": "0x17F0"}, "`dynamic initializer for 'g_beforeLevelMetas''": {"offset": "0x34B0"}, "`dynamic initializer for 'g_blockedNames2189''": {"offset": "0x34C0"}, "`dynamic initializer for 'g_blockedNames2372''": {"offset": "0x6E60"}, "`dynamic initializer for 'g_currentStreamingName''": {"offset": "0xB6D0"}, "`dynamic initializer for 'g_customStreamingFileRefs''": {"offset": "0x8930"}, "`dynamic initializer for 'g_customStreamingFiles''": {"offset": "0x1B60"}, "`dynamic initializer for 'g_customStreamingFilesByTag''": {"offset": "0x1B70"}, "`dynamic initializer for 'g_dataFiles''": {"offset": "0x89F0"}, "`dynamic initializer for 'g_defaultMetas''": {"offset": "0x8A00"}, "`dynamic initializer for 'g_dummyPackfileMutex''": {"offset": "0x1800"}, "`dynamic initializer for 'g_dummyPackfiles''": {"offset": "0x1830"}, "`dynamic initializer for 'g_failures''": {"offset": "0xA470"}, "`dynamic initializer for 'g_fontIds''": {"offset": "0xD170"}, "`dynamic initializer for 'g_fontLoadQueue''": {"offset": "0xD560"}, "`dynamic initializer for 'g_forceSemaQueue''": {"offset": "0xA580"}, "`dynamic initializer for 'g_getStreamingModule''": {"offset": "0xC440"}, "`dynamic initializer for 'g_getStreamingModuleFromExt''": {"offset": "0xC480"}, "`dynamic initializer for 'g_gtxdFiles''": {"offset": "0x8A10"}, "`dynamic initializer for 'g_handleMap''": {"offset": "0xA5F0"}, "`dynamic initializer for 'g_handleSprite''": {"offset": "0xD5A0"}, "`dynamic initializer for 'g_handleStack''": {"offset": "0x8A20"}, "`dynamic initializer for 'g_handlesToTag''": {"offset": "0x8AA0"}, "`dynamic initializer for 'g_hashes''": {"offset": "0x1BB0"}, "`dynamic initializer for 'g_itypHashList''": {"offset": "0x8BA0"}, "`dynamic initializer for 'g_itypRequests''": {"offset": "0xB100"}, "`dynamic initializer for 'g_itypToMapDataDeps''": {"offset": "0x8BE0"}, "`dynamic initializer for 'g_jsonRequests''": {"offset": "0xB110"}, "`dynamic initializer for 'g_lastStreamingName''": {"offset": "0x1BF0"}, "`dynamic initializer for 'g_loadObjectsNow''": {"offset": "0xC4C0"}, "`dynamic initializer for 'g_loadedDataFiles''": {"offset": "0x8C60"}, "`dynamic initializer for 'g_loadedFonts''": {"offset": "0xD5D0"}, "`dynamic initializer for 'g_manifestNames''": {"offset": "0x8C70"}, "`dynamic initializer for 'g_mapTypesFiles''": {"offset": "0x13F0"}, "`dynamic initializer for 'g_minimapOverlayLoadQueue''": {"offset": "0xBDE0"}, "`dynamic initializer for 'g_minimapOverlayRemoveQueue''": {"offset": "0xBE20"}, "`dynamic initializer for 'g_oldEntryList''": {"offset": "0x8CB0"}, "`dynamic initializer for 'g_onCriticalFrameQueue''": {"offset": "0xA700"}, "`dynamic initializer for 'g_ourIndexes''": {"offset": "0x8CC0"}, "`dynamic initializer for 'g_overlayClips''": {"offset": "0xBE60"}, "`dynamic initializer for 'g_pedsToRegister''": {"offset": "0x8D40"}, "`dynamic initializer for 'g_pendingRemovals''": {"offset": "0x8D80"}, "`dynamic initializer for 'g_permanentItyps''": {"offset": "0x8DC0"}, "`dynamic initializer for 'g_registerObject''": {"offset": "0xC500"}, "`dynamic initializer for 'g_registerRawStreamingFile''": {"offset": "0xC540"}, "`dynamic initializer for 'g_registeredFileSet''": {"offset": "0x1C00"}, "`dynamic initializer for 'g_releaseObject''": {"offset": "0xC580"}, "`dynamic initializer for 'g_releaseSystemObject''": {"offset": "0xC5C0"}, "`dynamic initializer for 'g_removalQueue''": {"offset": "0xA770"}, "`dynamic initializer for 'g_requestObject''": {"offset": "0xC600"}, "`dynamic initializer for 'g_resourceStats''": {"offset": "0x8E00"}, "`dynamic initializer for 'g_sceneContentsList''": {"offset": "0xB120"}, "`dynamic initializer for 'g_streamingHashStoresToIndices''": {"offset": "0xC940"}, "`dynamic initializer for 'g_streamingHashesToNames''": {"offset": "0xC980"}, "`dynamic initializer for 'g_streamingIndexesToNames''": {"offset": "0xCA00"}, "`dynamic initializer for 'g_streamingNamesToIndices''": {"offset": "0xCA80"}, "`dynamic initializer for 'g_streamingSuffixSet''": {"offset": "0x8E80"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'g_undoTxdRelationships''": {"offset": "0x8F90"}, "`dynamic initializer for 'g_unregisterObject''": {"offset": "0xC640"}, "`dynamic initializer for 'getArchetype''": {"offset": "0x26F0"}, "`dynamic initializer for 'getArchetypeFromModelIndex''": {"offset": "0xB400"}, "`dynamic initializer for 'getBoneIndexFromTag''": {"offset": "0xAB50"}, "`dynamic initializer for 'getExtension''": {"offset": "0x2730"}, "`dynamic initializer for 'getRawStreamer''": {"offset": "0x8FD0"}, "`dynamic initializer for 'getScriptEntity''": {"offset": "0x2770"}, "`dynamic initializer for 'getScriptGuidForEntity''": {"offset": "0x27B0"}, "`dynamic initializer for 'hasModelLoaded''": {"offset": "0xB440"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x1D10"}, "`dynamic initializer for 'hookFunctionMetadataDep''": {"offset": "0x9E10"}, "`dynamic initializer for 'hookFunctionSeatManager''": {"offset": "0xB170"}, "`dynamic initializer for 'initFunction''": {"offset": "0x18F0"}, "`dynamic initializer for 'initGfxTexture''": {"offset": "0x9050"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0xD7D0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0xDB20"}, "`dynamic initializer for 'loadManifest''": {"offset": "0x9210"}, "`dynamic initializer for 'loadedCollisions''": {"offset": "0x9250"}, "`dynamic initializer for 'makeMapDataContents''": {"offset": "0xB330"}, "`dynamic initializer for 'overlayRootClip''": {"offset": "0xC2F0"}, "`dynamic initializer for 'pgRawStreamer_AssetsCountCmd''": {"offset": "0x92D0"}, "`dynamic initializer for 'rage__fwArchetypeManager__FreeArchetypes''": {"offset": "0x93F0"}, "`dynamic initializer for 'registerArchetype''": {"offset": "0xB370"}, "`dynamic initializer for 'removeFromScene''": {"offset": "0xB3B0"}, "`dynamic initializer for 's_newTypesIds''": {"offset": "0x29B0"}, "`dynamic initializer for 'strRefCounts''": {"offset": "0xA9A0"}, "`dynamic initializer for 'strRefCountsMutex''": {"offset": "0xAA20"}, "`dynamic initializer for 'tbb::detail::r1::concurrent_monitor_mutex::my_init_mutex''": {"offset": "0xDCB0"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x11E0"}, "`dynamic initializer for 'xbr::virt::Base<15,2802,0,6>::ms_initFunction''": {"offset": "0x1360"}, "`dynamic initializer for 'xbr::virt::Base<18,2802,0,6>::ms_initFunction''": {"offset": "0x1220"}, "`dynamic initializer for 'xbr::virt::Base<22,2802,0,6>::ms_initFunction''": {"offset": "0x1AB0"}, "`dynamic initializer for 'xbr::virt::Base<23,2802,0,6>::ms_initFunction''": {"offset": "0x1260"}, "`dynamic initializer for 'xbr::virt::Base<26,2802,0,6>::ms_initFunction''": {"offset": "0x12A0"}, "`dynamic initializer for 'xbr::virt::Base<27,2802,0,6>::ms_initFunction''": {"offset": "0x2EE0"}, "`dynamic initializer for 'xbr::virt::Base<29,2802,0,6>::ms_initFunction''": {"offset": "0x12E0"}, "`dynamic initializer for 'xbr::virt::Base<32,2802,2,6>::ms_initFunction''": {"offset": "0x98C0"}, "`dynamic initializer for 'xbr::virt::Base<34,2802,0,6>::ms_initFunction''": {"offset": "0x1320"}, "`dynamic initializer for 'xbr::virt::Base<35,2802,0,6>::ms_initFunction''": {"offset": "0xAF10"}, "`dynamic initializer for 'xbr::virt::Base<4,2802,0,6>::ms_initFunction''": {"offset": "0x9880"}, "`dynamic initializer for 'xbr::virt::Base<4,2802,2,6>::ms_initFunction''": {"offset": "0x1630"}, "`dynamic initializer for 'xbr::virt::Base<43,2802,0,6>::ms_initFunction''": {"offset": "0xA350"}, "`dynamic initializer for 'xbr::virt::Base<46,2802,0,6>::ms_initFunction''": {"offset": "0x9900"}, "`dynamic initializer for 'xbr::virt::Base<51,2802,0,6>::ms_initFunction''": {"offset": "0xA390"}, "`dynamic initializer for 'xbr::virt::Base<53,2802,0,6>::ms_initFunction''": {"offset": "0x2F20"}, "`dynamic initializer for 'xbr::virt::Base<54,2802,0,6>::ms_initFunction''": {"offset": "0xA3D0"}, "`dynamic initializer for 'xbr::virt::Base<56,2802,0,6>::ms_initFunction''": {"offset": "0x2F60"}, "`dynamic initializer for 'xbr::virt::Base<57,2802,0,6>::ms_initFunction''": {"offset": "0x2A30"}, "`dynamic initializer for 'xbr::virt::Base<61,2802,0,6>::ms_initFunction''": {"offset": "0x2FA0"}, "`dynamic initializer for 'xbr::virt::Base<64,2802,0,6>::ms_initFunction''": {"offset": "0x2FE0"}, "`dynamic initializer for 'xbr::virt::Base<67,2802,0,6>::ms_initFunction''": {"offset": "0x3020"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::RegIDMap": {"offset": "0x3A6E0"}, "`jitasm::compiler::PrepareCompile'::`2'::RegIDMap::~RegIDMap": {"offset": "0x3B550"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>,<lambda_2730b6c68f930e168344f035418efbb7> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>,<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5A280"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>,<lambda_41af1ba482b752c39807db2ef15caf48> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5A280"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>,<lambda_55b0750980631035f31e5def29397b5e> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5F8C0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>,<lambda_55b0750980631035f31e5def29397b5e> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5F8C0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>,<lambda_7604da78e8f33a312c72ae5a43f9b93b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>,<lambda_9eb0329f5ef69cc2910674ebb0cb78aa> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5A280"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5A280"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>,<lambda_e4ba7588b360fe652a3faff73b7b991b> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x5F8E0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>,<lambda_f17ebabe33bc32be107ce6fff046b802> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x22F90"}, "atArray<CPedModelInfo *>::~atArray<CPedModelInfo *>": {"offset": "0x1DAB0"}, "atArray<fwEntityDef *>::~atArray<fwEntityDef *>": {"offset": "0x1DAB0"}, "atArray<fwExtensionDef *>::atArray<fwExtensionDef *>": {"offset": "0x1CCA0"}, "atArray<fwExtensionDef *>::~atArray<fwExtensionDef *>": {"offset": "0x1DAB0"}, "atArray<std::array<float,3> >::~atArray<std::array<float,3> >": {"offset": "0x1DAB0"}, "atArray<streaming::strStreamingModule *>::atArray<streaming::strStreamingModule *>": {"offset": "0x1CCA0"}, "atArray<unsigned int>::~atArray<unsigned int>": {"offset": "0x1DAB0"}, "atHashMap<bool>::~atHashMap<bool>": {"offset": "0x22C60"}, "atPoolBase::GetCount": {"offset": "0x20430"}, "atexit": {"offset": "0x908E0"}, "bigUpdate": {"offset": "0x5C1E0"}, "capture_previous_context": {"offset": "0x912E8"}, "chunkyArrayAppend": {"offset": "0x54CA0"}, "console::DPrintf<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,unsigned int>": {"offset": "0x30610"}, "console::PrintError<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x6CB80"}, "console::PrintWarning<char const *,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x28840"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x59C20"}, "console::Printfv": {"offset": "0x5BAE0"}, "dllmain_crt_dispatch": {"offset": "0x90E80"}, "dllmain_crt_process_attach": {"offset": "0x90ED0"}, "dllmain_crt_process_detach": {"offset": "0x90FE8"}, "dllmain_dispatch": {"offset": "0x9106C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0x19AF0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0x167F0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x7F340"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x7E9D0"}, "fmt::v8::detail::add_compare": {"offset": "0x7EB50"}, "fmt::v8::detail::assert_fail": {"offset": "0x7EC90"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x7ECE0"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x7EEB0"}, "fmt::v8::detail::bigint::square": {"offset": "0x7F710"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x7E9D0"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0xF000"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x7F9D0"}, "fmt::v8::detail::compare": {"offset": "0x7EE10"}, "fmt::v8::detail::count_digits": {"offset": "0x198D0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x7B520"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0xF0B0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x7F210"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x7D3F0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x7F4F0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x7D420"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x7E0E0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x7DCB0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x7F470"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x7B630"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x7B630"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x7F1A0"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0xF0E0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x7CAE0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0xF3B0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x7C9C0"}, "fmt::v8::detail::format_float<double>": {"offset": "0x7B070"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x7CC20"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0xF490"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x7CF80"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0xF5B0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0xF710"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0xF970"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0x1A660"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x7D630"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x7D8B0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x7DB40"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0x16850"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0xFA40"}, "fmt::v8::detail::utf8_decode": {"offset": "0x1A4A0"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x11040"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x12040"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x11BF0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x12480"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x7E740"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x7E620"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x11B20"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x128C0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x12900"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x12A90"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x13450"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x12E60"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x163E0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x13B80"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x13FA0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x14170"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x14310"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x14310"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x144A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x146C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x14840"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x15FD0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x149D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x14BF0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x14E90"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x150B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x15230"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x15450"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x15670"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x157F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x15A10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x15B90"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x15DB0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x16160"}, "fmt::v8::format_error::format_error": {"offset": "0x165E0"}, "fmt::v8::format_error::~format_error": {"offset": "0x16980"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x81770"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFEB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFC90"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFB60"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFA70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFEB0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFD80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xFFE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x10B40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x10570"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x22AD0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x82010"}, "fprintf": {"offset": "0x1A720"}, "fwAltSkeletonExtension__Add": {"offset": "0x296B0"}, "fwArchetypeDef::fwArchetypeDef": {"offset": "0x1D6B0"}, "fwArchetypeDef::~fwArchetypeDef": {"offset": "0x1E0E0"}, "fwEntity::AddExtension": {"offset": "0x1FDA0"}, "fwEntity::AddToScene": {"offset": "0x1FDB0"}, "fwEntity::AddToSceneWrap": {"offset": "0x1FE10"}, "fwEntity::GetArchetype": {"offset": "0x20420"}, "fwEntity::GetExtension": {"offset": "0x20460"}, "fwEntity::GetNetObject": {"offset": "0x205B0"}, "fwEntity::GetOwnedBy": {"offset": "0x205D0"}, "fwEntity::GetPosition": {"offset": "0x205E0"}, "fwEntity::GetProtectedFlags": {"offset": "0x20600"}, "fwEntity::GetRadius": {"offset": "0x20610"}, "fwEntity::GetTransform": {"offset": "0x20670"}, "fwEntity::GetType": {"offset": "0x20680"}, "fwEntity::IsDynamicEntity": {"offset": "0x206D0"}, "fwEntity::IsOfType": {"offset": "0x206E0"}, "fwEntity::IsOfTypeH": {"offset": "0x2BA40"}, "fwEntity::ProtectStreamedArchetype": {"offset": "0x2BC90"}, "fwEntity::RemoveFromScene": {"offset": "0x208D0"}, "fwEntity::SetModelIndex": {"offset": "0x209A0"}, "fwEntity::SetTransform": {"offset": "0x20A10"}, "fwEntity::SetupFromEntityDef": {"offset": "0x20AA0"}, "fwEntity::UpdateTransform": {"offset": "0x20B30"}, "fwEntity::fwEntity": {"offset": "0x1D7C0"}, "fwEntity::~fwEntity": {"offset": "0x16730"}, "fwEntityDef::fwEntityDef": {"offset": "0x1D860"}, "fwEntityDef::~fwEntityDef": {"offset": "0x1E110"}, "fwEntity_DtorWrap": {"offset": "0x61FA0"}, "fwEvent<>::ConnectInternal": {"offset": "0x20190"}, "fwEvent<>::callback::~callback": {"offset": "0x1E010"}, "fwEvent<>::~fwEvent<>": {"offset": "0x1DBD0"}, "fwEvent<char const *>::ConnectInternal": {"offset": "0x20190"}, "fwEvent<enum rage::InitFunctionType>::ConnectInternal": {"offset": "0x20190"}, "fwExtensionDef::~fwExtensionDef": {"offset": "0x1E160"}, "fwExtensionDefImpl2802::_2802_1": {"offset": "0x21030"}, "fwExtensionDefImpl2802::_2802_2": {"offset": "0x21030"}, "fwExtensionDefImpl2802::_2802_3": {"offset": "0x21030"}, "fwExtensionDefImpl2802::_2802_4": {"offset": "0x21030"}, "fwExtensionDefImpl2802::_2802_5": {"offset": "0x21030"}, "fwExtensionDefImpl2802::_2802_6": {"offset": "0x21030"}, "fwExtensionDefImpl2802::fwExtensionDefImpl2802": {"offset": "0x1D8E0"}, "fwExtensionDefImpl2802::~fwExtensionDefImpl2802": {"offset": "0x16730"}, "fwExtensionDefImplOld::fwExtensionDefImplOld": {"offset": "0x1D910"}, "fwExtensionDefImplOld::~fwExtensionDefImplOld": {"offset": "0x16730"}, "fwExtensionList::Add": {"offset": "0x2B160"}, "fwExtensionList::Get": {"offset": "0x2B5D0"}, "fwMapDataStore__FinishLoadingHook": {"offset": "0x55DC0"}, "fwMapDataStore__ModifyHierarchyStatusRecursive": {"offset": "0x560B0"}, "fwMapTypesStore__Unload": {"offset": "0x560F0"}, "fwMapTypes__ConstructArchetypesStub": {"offset": "0x563B0"}, "fwPlatformString::fwPlatformString": {"offset": "0x6F760"}, "fwPlatformString::~fwPlatformString": {"offset": "0x169A0"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0x22D70"}, "fwRefCountable::AddRef": {"offset": "0x84000"}, "fwRefCountable::Release": {"offset": "0x84010"}, "fwRefCountable::~fwRefCountable": {"offset": "0x83FF0"}, "fwSceneUpdateExtension::GetClassId": {"offset": "0x2B600"}, "fwSceneUpdateExtension::GetUpdateFlags": {"offset": "0x20690"}, "fwSceneUpdateExtension::fwSceneUpdateExtension": {"offset": "0x1D950"}, "fwSceneUpdateExtension::~fwSceneUpdateExtension": {"offset": "0x16730"}, "fwStaticBoundsStore__ModifyHierarchyStatus": {"offset": "0x563E0"}, "grcTextureDX11_CtorWrap": {"offset": "0x28D00"}, "grcTextureDX11_resolvePtr": {"offset": "0x28DC0"}, "grcTexturePC_CtorWrap": {"offset": "0x28DF0"}, "hde64_disasm": {"offset": "0x85310"}, "hook::AllocateFunctionStub": {"offset": "0x83140"}, "hook::AllocateStubMemory": {"offset": "0x831A0"}, "hook::TransformPattern": {"offset": "0x838A0"}, "hook::call<void *,char *>": {"offset": "0x5F100"}, "hook::call<void *,void *>": {"offset": "0x5F100"}, "hook::details::StubInitFunction::Run": {"offset": "0x22070"}, "hook::get_adjusted<__int64>": {"offset": "0x57890"}, "hook::get_pattern<EnumEntry,24>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,18>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,21>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,26>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,27>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,29>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,30>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,32>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,33>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,35>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,36>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,37>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,38>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,39>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,40>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,42>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,44>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,46>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,48>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,51>": {"offset": "0x1C9D0"}, "hook::get_pattern<char,59>": {"offset": "0x1C9D0"}, "hook::get_pattern<char>": {"offset": "0x578C0"}, "hook::get_pattern<int,40>": {"offset": "0x1C9D0"}, "hook::get_pattern<int>": {"offset": "0x578C0"}, "hook::get_pattern<signed char,27>": {"offset": "0x1C9D0"}, "hook::get_pattern<unsigned char,38>": {"offset": "0x1C9D0"}, "hook::get_pattern<unsigned char,48>": {"offset": "0x1C9D0"}, "hook::get_pattern<unsigned char,51>": {"offset": "0x1C9D0"}, "hook::get_pattern<unsigned int,41>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,12>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,15>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,18>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,20>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,21>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,23>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,24>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,26>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,27>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,29>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,30>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,31>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,32>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,33>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,35>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,36>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,37>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,38>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,39>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,41>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,42>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,43>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,44>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,45>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,46>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,47>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,48>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,51>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,53>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,54>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,55>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,56>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,57>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,58>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,68>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,71>": {"offset": "0x1C9D0"}, "hook::get_pattern<void,89>": {"offset": "0x1C9D0"}, "hook::get_pattern<void>": {"offset": "0x578C0"}, "hook::nop<char *>": {"offset": "0x39460"}, "hook::nop<void *>": {"offset": "0x39460"}, "hook::pattern::EnsureMatches": {"offset": "0x83240"}, "hook::pattern::Initialize": {"offset": "0x835A0"}, "hook::pattern::count": {"offset": "0x283F0"}, "hook::pattern::~pattern": {"offset": "0x1E1C0"}, "hook::trampoline_raw": {"offset": "0x84040"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x64690"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x63780"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::Call": {"offset": "0x5AB10"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(int const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x595F0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::Call": {"offset": "0x24810"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x22490"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x41FC0"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x63CD0"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0x62FA0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0x62E20"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0x63000"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x64FC0"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0x62EA0"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0x63010"}, "internal::ConsoleVariableEntry<int>::ConsoleVariableEntry<int>": {"offset": "0x59F40"}, "internal::ConsoleVariableEntry<int>::GetOfflineValue": {"offset": "0x58B90"}, "internal::ConsoleVariableEntry<int>::GetValue": {"offset": "0x58A50"}, "internal::ConsoleVariableEntry<int>::SaveOfflineValue": {"offset": "0x58BB0"}, "internal::ConsoleVariableEntry<int>::SetRawValue": {"offset": "0x5BDC0"}, "internal::ConsoleVariableEntry<int>::SetValue": {"offset": "0x58A90"}, "internal::ConsoleVariableEntry<int>::UpdateTrackingVariable": {"offset": "0x58BC0"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x64910"}, "internal::Constraints<int,void>::Compare": {"offset": "0x5AF20"}, "internal::UnparseArgument<int>": {"offset": "0x59E70"}, "jitasm::Backend::Assemble": {"offset": "0x3F8F0"}, "jitasm::Backend::Encode": {"offset": "0x45320"}, "jitasm::Backend::EncodeALU": {"offset": "0x45790"}, "jitasm::Backend::EncodeImm": {"offset": "0x45900"}, "jitasm::Backend::EncodeJMP": {"offset": "0x45AB0"}, "jitasm::Backend::EncodeModRM": {"offset": "0x45D00"}, "jitasm::Backend::EncodeOpcode": {"offset": "0x462B0"}, "jitasm::Backend::EncodePrefixes": {"offset": "0x46360"}, "jitasm::Backend::GetWRXB": {"offset": "0x47D60"}, "jitasm::Backend::db": {"offset": "0x55910"}, "jitasm::Backend::dd": {"offset": "0x55980"}, "jitasm::Frontend::AppendInstr": {"offset": "0x3F7C0"}, "jitasm::Frontend::AppendJcc": {"offset": "0x5E260"}, "jitasm::Frontend::Assemble": {"offset": "0x3FCF0"}, "jitasm::Frontend::Frontend": {"offset": "0x5F450"}, "jitasm::Frontend::GetLabelID": {"offset": "0x5E400"}, "jitasm::Frontend::Label::~Label": {"offset": "0x16850"}, "jitasm::Frontend::NewLabelID": {"offset": "0x5E4C0"}, "jitasm::Frontend::ResolveJump": {"offset": "0x4FDC0"}, "jitasm::Frontend::add": {"offset": "0x54860"}, "jitasm::Frontend::call": {"offset": "0x54BE0"}, "jitasm::Frontend::cmp": {"offset": "0x7AE50"}, "jitasm::Frontend::jmp": {"offset": "0x5E5C0"}, "jitasm::Frontend::lea<jitasm::detail::OpdT<64> >": {"offset": "0x791F0"}, "jitasm::Frontend::mov": {"offset": "0x567B0"}, "jitasm::Frontend::movaps": {"offset": "0x56970"}, "jitasm::Frontend::pop": {"offset": "0x56BE0"}, "jitasm::Frontend::push": {"offset": "0x56CE0"}, "jitasm::Frontend::pxor": {"offset": "0x56FF0"}, "jitasm::Frontend::ret": {"offset": "0x57330"}, "jitasm::Frontend::sub": {"offset": "0x57490"}, "jitasm::Frontend::vxorps": {"offset": "0x575C0"}, "jitasm::Frontend::xchg": {"offset": "0x576C0"}, "jitasm::Frontend::xorps": {"offset": "0x577A0"}, "jitasm::Frontend::~Frontend": {"offset": "0x3B1E0"}, "jitasm::Instr::Instr": {"offset": "0x3A210"}, "jitasm::compiler::BasicBlock::BasicBlock": {"offset": "0x3A180"}, "jitasm::compiler::BasicBlock::IsDominated": {"offset": "0x48480"}, "jitasm::compiler::Compile": {"offset": "0x44070"}, "jitasm::compiler::ControlFlowGraph::Build": {"offset": "0x40610"}, "jitasm::compiler::ControlFlowGraph::DetectLoops": {"offset": "0x445A0"}, "jitasm::compiler::ControlFlowGraph::MakeDepthFirstBlocks": {"offset": "0x4DEB0"}, "jitasm::compiler::ControlFlowGraph::clear": {"offset": "0x557E0"}, "jitasm::compiler::ControlFlowGraph::get_block": {"offset": "0x56460"}, "jitasm::compiler::ControlFlowGraph::initialize": {"offset": "0x56560"}, "jitasm::compiler::ControlFlowGraph::~ControlFlowGraph": {"offset": "0x3B070"}, "jitasm::compiler::DominatorFinder::Compress": {"offset": "0x443E0"}, "jitasm::compiler::DominatorFinder::~DominatorFinder": {"offset": "0x3B0E0"}, "jitasm::compiler::GenerateEpilog": {"offset": "0x46DB0"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::GpRegOperator>": {"offset": "0x30D40"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::MmxRegOperator>": {"offset": "0x31220"}, "jitasm::compiler::GenerateInterIntervalInstr<jitasm::compiler::XmmRegOperator>": {"offset": "0x31680"}, "jitasm::compiler::GenerateProlog": {"offset": "0x47100"}, "jitasm::compiler::GetRegFamily": {"offset": "0x47AD0"}, "jitasm::compiler::Lifetime::AddUsePoint": {"offset": "0x3F2A0"}, "jitasm::compiler::Lifetime::AssignRegister": {"offset": "0x400A0"}, "jitasm::compiler::Lifetime::BuildIntervals": {"offset": "0x41080"}, "jitasm::compiler::Lifetime::Interval::Interval": {"offset": "0x3A350"}, "jitasm::compiler::Lifetime::Interval::~Interval": {"offset": "0x3B260"}, "jitasm::compiler::Lifetime::LessAssignOrder::num_of_assignable": {"offset": "0x56A50"}, "jitasm::compiler::Lifetime::Lifetime": {"offset": "0x3A420"}, "jitasm::compiler::Lifetime::SpillIdentification": {"offset": "0x51F30"}, "jitasm::compiler::Lifetime::~Lifetime": {"offset": "0x3B3F0"}, "jitasm::compiler::LinearScanRegisterAlloc": {"offset": "0x487B0"}, "jitasm::compiler::LiveVariableAnalysis": {"offset": "0x48C10"}, "jitasm::compiler::Operations::Operations": {"offset": "0x3A5F0"}, "jitasm::compiler::PrepareCompile": {"offset": "0x4E460"}, "jitasm::compiler::RewriteInstructions": {"offset": "0x50860"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::GpRegOperator> >": {"offset": "0x30880"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::MmxRegOperator> >": {"offset": "0x30AC0"}, "jitasm::compiler::SCCFinder::Find<jitasm::compiler::MoveGenerator<jitasm::compiler::XmmRegOperator> >": {"offset": "0x30C00"}, "jitasm::compiler::VariableManager::AllocSpillSlots": {"offset": "0x3F4F0"}, "jitasm::compiler::VariableManager::UpdateVarSize": {"offset": "0x52D30"}, "jitasm::compiler::VariableManager::~VariableManager": {"offset": "0x3B5B0"}, "jitasm::detail::CodeBuffer::Reset": {"offset": "0x4FCF0"}, "jitasm::detail::CodeBuffer::~CodeBuffer": {"offset": "0x3B030"}, "jitasm::detail::ImmXor8": {"offset": "0x48380"}, "jitasm::detail::Opd::GetDisp": {"offset": "0x479A0"}, "jitasm::detail::Opd::GetImm": {"offset": "0x47A60"}, "jitasm::detail::ScopedLock<jitasm::detail::SpinLock>::~ScopedLock<jitasm::detail::SpinLock>": {"offset": "0x3A730"}, "launch::IsSDK": {"offset": "0x758D0"}, "launch::IsSDKGuest": {"offset": "0x75970"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_array": {"offset": "0x2E010"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_int64<__int64>": {"offset": "0x2CE20"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint32<unsigned int>": {"offset": "0x2D390"}, "msgpack::v1::packer<msgpack::v1::sbuffer>::pack_imp_uint64<unsigned __int64>": {"offset": "0x2D650"}, "msgpack::v1::sbuffer::~sbuffer": {"offset": "0x2DA10"}, "origCfxCollection_AddStreamingFileByTag": {"offset": "0x28490"}, "origCfxCollection_BackoutStreamingTag": {"offset": "0x28610"}, "pgBaseDtorHook": {"offset": "0x76050"}, "pgRawStreamer__GetEntry": {"offset": "0x56AB0"}, "pgRawStreamer__GetEntryNameToBuffer": {"offset": "0x56B10"}, "pgRawStreamer__OpenCollectionEntry": {"offset": "0x56B80"}, "pgStreamerRead": {"offset": "0x62410"}, "rage::GetPgRawStreamerEntries": {"offset": "0x47AB0"}, "rage::`dynamic initializer for '_addKnownRef''": {"offset": "0xAF50"}, "rage::`dynamic initializer for '_removeKnownRef''": {"offset": "0xB000"}, "rage::`dynamic initializer for 'pgRawStreamerInvalidateEntry''": {"offset": "0x9290"}, "rage::fwArchetype::HasEmbeddedCollision": {"offset": "0x206A0"}, "rage::fwArchetype::fwArchetype": {"offset": "0x1D6A0"}, "rage::fwArchetype::~fwArchetype": {"offset": "0x1E0B0"}, "rage::fwArchetypeManager::GetArchetypeFromHashKey": {"offset": "0x2B5E0"}, "rage::fwArchetypeManager::GetStreamingModule": {"offset": "0x2BA10"}, "rage::fwArchetypeManager::LookupModelId": {"offset": "0x2BB50"}, "rage::fwArchetypeManager::RegisterPermanentArchetype": {"offset": "0x2BCA0"}, "rage::fwArchetypeManager::RegisterStreamedArchetype": {"offset": "0x2BCF0"}, "rage::fwArchetypeManager::UnregisterStreamedArchetype": {"offset": "0x2BE80"}, "rage::fwExtension::InitArchetypeExtensionFromDefinition": {"offset": "0x16730"}, "rage::fwExtension::InitEntityExtensionFromDefinition": {"offset": "0x16730"}, "rage::fwExtension::fwExtension": {"offset": "0x1D8B0"}, "rage::fwExtension::~fwExtension": {"offset": "0x16730"}, "rage::fwRefAwareBase::AddKnownRef": {"offset": "0x69B70"}, "rage::fwRefAwareBase::RemoveKnownRef": {"offset": "0x6BFF0"}, "rage::fwRefAwareBase::fwRefAwareBase": {"offset": "0x1D920"}, "rage::fwRefAwareBase::~fwRefAwareBase": {"offset": "0x16730"}, "rage::fwScriptGuid::GetBaseFromGuid": {"offset": "0x2B5F0"}, "rage::fwScriptGuid::GetGuidFromBase": {"offset": "0x2B610"}, "rage::strStreamingAllocator::GetInstance": {"offset": "0x71FB0"}, "rage__fiFile__OpenWrap": {"offset": "0x28650"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x683D0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x69CB0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x69DC0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::GenericStringStream<rapidjson::UTF8<char> > >": {"offset": "0x67520"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::String": {"offset": "0x6C2B0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x16660"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x68400"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x665C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::GenericStringStream<rapidjson::UTF8<char> > >": {"offset": "0x667F0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x668A0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x670E0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x67710"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::GenericStringStream<rapidjson::UTF8<char> >,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x67B20"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x6BED0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x16700"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x16700"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0xDF70"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0x177D0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Begin": {"offset": "0x69B80"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::FindMember<rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x663B0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetDouble": {"offset": "0x69F80"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetString": {"offset": "0x6A060"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Size": {"offset": "0x6C270"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x16730"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0x18940"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0x18A00"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0x18C70"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0x19110"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0x16740"}, "rapidjson::internal::DigitGen": {"offset": "0x17A80"}, "rapidjson::internal::FastPath": {"offset": "0x69ED0"}, "rapidjson::internal::Grisu2": {"offset": "0x18530"}, "rapidjson::internal::Prettify": {"offset": "0x18AB0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x67ED0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0xEA00"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0xEAD0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0x16700"}, "rapidjson::internal::StreamLocalCopy<rapidjson::GenericStringStream<rapidjson::UTF8<char> >,1>::~StreamLocalCopy<rapidjson::GenericStringStream<rapidjson::UTF8<char> >,1>": {"offset": "0x683C0"}, "rapidjson::internal::WriteExponent": {"offset": "0x19080"}, "rapidjson::internal::u32toa": {"offset": "0x19BE0"}, "rapidjson::internal::u64toa": {"offset": "0x19E50"}, "ret0": {"offset": "0x28780"}, "se::Object::~Object": {"offset": "0x16850"}, "sf::AddMinimapOverlay": {"offset": "0x70F00"}, "sf::CallMinimapOverlay": {"offset": "0x70FC0"}, "sf::HasMinimapLoaded": {"offset": "0x711A0"}, "sf::RegisterFontIndex": {"offset": "0x7A1F0"}, "sf::RegisterFontLib": {"offset": "0x7A2F0"}, "sf::RemoveMinimapOverlay": {"offset": "0x711E0"}, "sf::SetMinimapOverlayDisplay": {"offset": "0x712D0"}, "sf::logging::SfCallGameFromFlash": {"offset": "0x714E0"}, "sf::logging::`dynamic initializer for 'g_scaleformDebugLogVar''": {"offset": "0xBEA0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<int,void *> > >": {"offset": "0x2A6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x3A7C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *> > >": {"offset": "0x3A760"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,void *> > >": {"offset": "0x16770"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,MapTypesFile>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,MapTypesFile>,void *> > >": {"offset": "0x1D9A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,void *> > >": {"offset": "0x2A6E0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,int>,void *> > >": {"offset": "0x2A6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >": {"offset": "0x2A6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,void *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,void *>,void *> > >": {"offset": "0x2A6E0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<unsigned int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<unsigned int,void *> > >": {"offset": "0x2A6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<void *,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<void *,void *> > >": {"offset": "0x2A6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x2A6E0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x16770"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x3A760"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<GFxValue> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<GFxValue> >,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >": {"offset": "0x3A780"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A7A0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x16770"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::tuple<streaming::strStreamingModule *,unsigned int> const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::tuple<streaming::strStreamingModule *,unsigned int> const ,unsigned int>,void *> > >": {"offset": "0x3A740"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<streaming::strStreamingModule *,int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<streaming::strStreamingModule *,int>,void *> > >": {"offset": "0x3A7C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A780"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::pair<int,int> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::pair<int,int> >,void *> > >": {"offset": "0x3A760"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A7A0"}, "std::_Buffered_inplace_merge_divide_and_conquer2<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x31DB0"}, "std::_Buffered_inplace_merge_divide_and_conquer<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x31EA0"}, "std::_Buffered_inplace_merge_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x32090"}, "std::_Buffered_merge_sort_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x32430"}, "std::_Buffered_rotate_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *>": {"offset": "0x32580"}, "std::_Chunked_merge_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x327E0"}, "std::_Default_allocator_traits<std::allocator<jitasm::compiler::Lifetime::Interval> >::construct<jitasm::compiler::Lifetime::Interval,jitasm::compiler::Lifetime::Interval const &>": {"offset": "0x39120"}, "std::_Destroy_range<std::allocator<jitasm::compiler::Lifetime::Interval> >": {"offset": "0x32C00"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1BB00"}, "std::_Destroy_range<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags> > >": {"offset": "0x25BD0"}, "std::_Destroy_range<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x32BC0"}, "std::_Destroy_range<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int> > >": {"offset": "0x25BD0"}, "std::_Destroy_range<std::allocator<std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x25BD0"}, "std::_Facet_Register": {"offset": "0x901B4"}, "std::_Find_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x34C40"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x1D9C0"}, "std::_Func_class<bool,char const *>::~_Func_class<bool,char const *>": {"offset": "0x1D9C0"}, "std::_Func_class<bool,enum rage::InitFunctionType>::~_Func_class<bool,enum rage::InitFunctionType>": {"offset": "0x1D9C0"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x1D9C0"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x1D9C0"}, "std::_Func_class<void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1D9C0"}, "std::_Func_class<void,int const &>::~_Func_class<void,int const &>": {"offset": "0x1D9C0"}, "std::_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1D9C0"}, "std::_Func_class<void>::_Reset_move": {"offset": "0x61890"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x1D9C0"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x2F710"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x2F280"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x2F710"}, "std::_Func_impl_no_alloc<<lambda_06002c27587c5bd837fe32ea8964a5e3>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x2F720"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x53200"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x53260"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x53200"}, "std::_Func_impl_no_alloc<<lambda_1272e92d6794d374a07cc21bbd840fca>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x54210"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Copy": {"offset": "0x25AC0"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Do_call": {"offset": "0x25AE0"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Move": {"offset": "0x25AC0"}, "std::_Func_impl_no_alloc<<lambda_200db3e364e98991af7e19cafdd76c6b>,bool>::_Target_type": {"offset": "0x25B00"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Copy": {"offset": "0x2F8D0"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Do_call": {"offset": "0x2F8F0"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Move": {"offset": "0x2F8D0"}, "std::_Func_impl_no_alloc<<lambda_2216ca58037cc50045d9738185512710>,bool>::_Target_type": {"offset": "0x2F910"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Copy": {"offset": "0x5EAD0"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Delete_this": {"offset": "0x5EB10"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Do_call": {"offset": "0x5EAF0"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Move": {"offset": "0x5EAD0"}, "std::_Func_impl_no_alloc<<lambda_231cd2e3a9f3badde0253f3a95afddf7>,void,unsigned int>::_Target_type": {"offset": "0x5EB00"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Copy": {"offset": "0x1A870"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Do_call": {"offset": "0x1A890"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Move": {"offset": "0x1A870"}, "std::_Func_impl_no_alloc<<lambda_23bd7e3f578bb070bf5618f3b20c066d>,bool>::_Target_type": {"offset": "0x1A8E0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0x63220"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0x63240"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0x63220"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0x63320"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x2F2D0"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x22170"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x2F350"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_2730b6c68f930e168344f035418efbb7>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x2F3A0"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x53210"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x53270"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x53210"}, "std::_Func_impl_no_alloc<<lambda_2f2946bc1b88b87d0e7f776deb025502>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x54220"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x22090"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x22170"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x22110"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_3b02a1d8b2e62d4dea5fa914720ca5d8>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x22160"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Copy": {"offset": "0x5EC40"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Do_call": {"offset": "0x5EC60"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Move": {"offset": "0x5EC40"}, "std::_Func_impl_no_alloc<<lambda_417a6858595dac32d948b627106c3c0c>,bool>::_Target_type": {"offset": "0x5EC80"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Copy": {"offset": "0x58E00"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Delete_this": {"offset": "0x58E80"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Do_call": {"offset": "0x58E60"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_41af1ba482b752c39807db2ef15caf48>,void>::_Target_type": {"offset": "0x58E70"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Copy": {"offset": "0x2F920"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Do_call": {"offset": "0x2F940"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Move": {"offset": "0x2F920"}, "std::_Func_impl_no_alloc<<lambda_4d4ff4e0316434221d8a16f47130a9b9>,bool>::_Target_type": {"offset": "0x2F950"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Copy": {"offset": "0x5EB20"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Delete_this": {"offset": "0x5EC00"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Do_call": {"offset": "0x5EBE0"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_55b0750980631035f31e5def29397b5e>,void>::_Target_type": {"offset": "0x5EBF0"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Copy": {"offset": "0x5ECD0"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Do_call": {"offset": "0x5ECF0"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Move": {"offset": "0x5ECD0"}, "std::_Func_impl_no_alloc<<lambda_5e6ea2d0c7e60615bb5a651c6308c8e5>,bool>::_Target_type": {"offset": "0x5ED30"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x661A0"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x661C0"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x661A0"}, "std::_Func_impl_no_alloc<<lambda_6badf1acbbf8484eae1b3ab80efdc47d>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x661E0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x630C0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x58CC0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x63140"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x63190"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x58D20"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x58CC0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x58DA0"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_7604da78e8f33a312c72ae5a43f9b93b>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x58DF0"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Copy": {"offset": "0x2F850"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Do_call": {"offset": "0x2F870"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Move": {"offset": "0x2F850"}, "std::_Func_impl_no_alloc<<lambda_8006531ae4b13c8ea7faf71c8138543a>,bool>::_Target_type": {"offset": "0x2F8C0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x63030"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x58CC0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x58C60"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x630B0"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x2F7D0"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x2F7F0"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x2F7D0"}, "std::_Func_impl_no_alloc<<lambda_84a35d73760c1fad83bbef74c47da93d>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x2F810"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Copy": {"offset": "0x2F820"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Do_call": {"offset": "0x2F7B0"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Move": {"offset": "0x2F820"}, "std::_Func_impl_no_alloc<<lambda_92cfc50fe68071063f51379ef2a0469c>,bool,char const *>::_Target_type": {"offset": "0x2F840"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Copy": {"offset": "0x2FD50"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Do_call": {"offset": "0x2FD60"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Move": {"offset": "0x2FD50"}, "std::_Func_impl_no_alloc<<lambda_98135fb5c535408e12d17fbabc55ae8c>,void>::_Target_type": {"offset": "0x2FD70"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x53220"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x53250"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x53280"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x53220"}, "std::_Func_impl_no_alloc<<lambda_9a365591f2f4777c2ca97b2fd9ddff10>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x54230"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x53240"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x53290"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x53240"}, "std::_Func_impl_no_alloc<<lambda_9a7bc5f32611c5001a914ead383e213e>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x54240"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x728B0"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x22170"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x2F350"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_9eb0329f5ef69cc2910674ebb0cb78aa>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x72930"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Copy": {"offset": "0x2A030"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Do_call": {"offset": "0x2A050"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Move": {"offset": "0x2A030"}, "std::_Func_impl_no_alloc<<lambda_a2a705da55022b960ba03f75920b4eae>,bool>::_Target_type": {"offset": "0x2A070"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x2F270"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x2F280"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x2F270"}, "std::_Func_impl_no_alloc<<lambda_a4df0db871dcfd5dd8b5d7e659554cd2>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x2F2A0"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Copy": {"offset": "0x2F2B0"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Do_call": {"offset": "0x2F280"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Move": {"offset": "0x2F2B0"}, "std::_Func_impl_no_alloc<<lambda_aa691ef91e0e6d57a02b97500c675ee7>,bool,CDataFileMountInterface *,CDataFileMgr::DataFile &>::_Target_type": {"offset": "0x2F2C0"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Copy": {"offset": "0x6FA50"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Do_call": {"offset": "0x6FA70"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Move": {"offset": "0x6FA50"}, "std::_Func_impl_no_alloc<<lambda_ab8404bbb51945a2b053e678f89f6c89>,bool>::_Target_type": {"offset": "0x6FA90"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Copy": {"offset": "0x5EC90"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Do_call": {"offset": "0x5ECB0"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Move": {"offset": "0x5EC90"}, "std::_Func_impl_no_alloc<<lambda_ae7e227b2b04fd90847d50fd4f6f21da>,bool>::_Target_type": {"offset": "0x5ECC0"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Copy": {"offset": "0x2F960"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Do_call": {"offset": "0x2F980"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Move": {"offset": "0x2F960"}, "std::_Func_impl_no_alloc<<lambda_b02a45ca5eca8643ce675259100e4a18>,bool>::_Target_type": {"offset": "0x2F9C0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0x631A0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0x58E80"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0x63200"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0x63210"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Copy": {"offset": "0x2F790"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Do_call": {"offset": "0x2F7B0"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Move": {"offset": "0x2F790"}, "std::_Func_impl_no_alloc<<lambda_c0f7dcc21db5e4a476c835ef64ac06e4>,bool>::_Target_type": {"offset": "0x2F7C0"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Copy": {"offset": "0x25A70"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Do_call": {"offset": "0x25A90"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Move": {"offset": "0x25A70"}, "std::_Func_impl_no_alloc<<lambda_c6e1fe83036411445c3d425ad1c64bd1>,bool>::_Target_type": {"offset": "0x25AB0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Copy": {"offset": "0x58FE0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Do_call": {"offset": "0x59000"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Move": {"offset": "0x58FE0"}, "std::_Func_impl_no_alloc<<lambda_cd07e93ce4e084374db77c62c4f7d117>,void,int const &>::_Target_type": {"offset": "0x590E0"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Copy": {"offset": "0x21F80"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Do_call": {"offset": "0x21FA0"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Move": {"offset": "0x21F80"}, "std::_Func_impl_no_alloc<<lambda_d9656e19c325c30cd335f8ec93b74c68>,void,void const *,int>::_Target_type": {"offset": "0x22060"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Copy": {"offset": "0x78620"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Do_call": {"offset": "0x78640"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Move": {"offset": "0x78620"}, "std::_Func_impl_no_alloc<<lambda_e1e42c5c4c41355e26db279ade4bcd8e>,bool>::_Target_type": {"offset": "0x78690"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Copy": {"offset": "0x78590"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Do_call": {"offset": "0x785B0"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Move": {"offset": "0x78590"}, "std::_Func_impl_no_alloc<<lambda_e3ec813411c41dc3541000444d5ff774>,bool>::_Target_type": {"offset": "0x785D0"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x5E990"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x5EA50"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x5EA30"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_e4ba7588b360fe652a3faff73b7b991b>,void,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x5EA40"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Copy": {"offset": "0x6F550"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Do_call": {"offset": "0x6F570"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Move": {"offset": "0x6F550"}, "std::_Func_impl_no_alloc<<lambda_e76b68dccad249dd70ad4c037713b23e>,bool>::_Target_type": {"offset": "0x6F590"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Copy": {"offset": "0x1A800"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Do_call": {"offset": "0x1A820"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Move": {"offset": "0x1A800"}, "std::_Func_impl_no_alloc<<lambda_e9ea21dee26c305b03315b2667a50268>,bool>::_Target_type": {"offset": "0x1A840"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x219A0"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x219B0"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x219A0"}, "std::_Func_impl_no_alloc<<lambda_eae4a17f64911edbb9552e7485b4bbb0>,void,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x219C0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0x58BE0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0x58CC0"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0x58C60"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0x21030"}, "std::_Func_impl_no_alloc<<lambda_f17ebabe33bc32be107ce6fff046b802>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0x58CB0"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x2FA40"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x2FA60"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x2FA40"}, "std::_Func_impl_no_alloc<<lambda_f331c76443f54e4874cdea1c72b41b06>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x2FAB0"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Copy": {"offset": "0x785E0"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Do_call": {"offset": "0x78600"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Move": {"offset": "0x785E0"}, "std::_Func_impl_no_alloc<<lambda_f5968c4b18e405350bc98239297f0d48>,bool>::_Target_type": {"offset": "0x78610"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Copy": {"offset": "0x2F9D0"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Do_call": {"offset": "0x2F9F0"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Move": {"offset": "0x2F9D0"}, "std::_Func_impl_no_alloc<<lambda_fbd37c60be11893d41ba7850fe1633c9>,bool,enum rage::InitFunctionType>::_Target_type": {"offset": "0x2FA30"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Copy": {"offset": "0x72250"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Delete_this": {"offset": "0x1A860"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Do_call": {"offset": "0x72260"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Get": {"offset": "0x1A850"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Move": {"offset": "0x72250"}, "std::_Func_impl_no_alloc<<lambda_fe88ff6dbc907fb0feeae768dbc364ca>,void>::_Target_type": {"offset": "0x72270"}, "std::_Guess_median_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x34F30"}, "std::_Hash<std::_Umap_traits<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Try_emplace<int>": {"offset": "0x386D0"}, "std::_Hash<std::_Umap_traits<int,std::list<unsigned int,std::allocator<unsigned int> >,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<int,std::list<unsigned int,std::allocator<unsigned int> >,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > >,0> >::_Try_emplace<int>": {"offset": "0x389B0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x22640"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,0> >::_Forced_rehash": {"offset": "0x539F0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,0> >::_Try_emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x383F0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,0> >::_Unchecked_erase": {"offset": "0x54600"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> >,0> >::clear": {"offset": "0x556E0"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<StreamingPackfileEntry>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x22640"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<StreamingPackfileEntry>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > >,0> >::_Forced_rehash": {"offset": "0x25540"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<StreamingPackfileEntry>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > >,0> >::emplace<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > >": {"offset": "0x22710"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x22640"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Forced_rehash": {"offset": "0x25540"}, "std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> >,0> >::_Try_emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x734F0"}, "std::_Hash<std::_Umap_traits<unsigned int,MapTypesFile,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,MapTypesFile> >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<unsigned int,MapTypesFile,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,MapTypesFile> >,0> >::_Unchecked_erase": {"offset": "0x21500"}, "std::_Hash<std::_Umap_traits<unsigned int,MapTypesFile,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,MapTypesFile> >,0> >::clear": {"offset": "0x21830"}, "std::_Hash<std::_Umap_traits<unsigned int,MapTypesFile,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,MapTypesFile> >,0> >::emplace<std::pair<unsigned int const ,MapTypesFile> >": {"offset": "0x1C600"}, "std::_Hash<std::_Umap_traits<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> >,0> >::_Forced_rehash": {"offset": "0x6EAA0"}, "std::_Hash<std::_Umap_traits<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> >,0> >::_Try_emplace<unsigned int const &>": {"offset": "0x6CDD0"}, "std::_Hash<std::_Umap_traits<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> >,0> >::~_Hash<std::_Umap_traits<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> >,0> >": {"offset": "0x6D130"}, "std::_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >::_Unchecked_erase": {"offset": "0x2C1E0"}, "std::_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >::clear": {"offset": "0x2C3B0"}, "std::_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >::emplace<std::pair<unsigned int const ,int> >": {"offset": "0x5F180"}, "std::_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >::~_Hash<std::_Umap_traits<unsigned int,int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,int> >,0> >": {"offset": "0x2A7B0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<unsigned int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Rehash_for_1": {"offset": "0x75FA0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Try_emplace<unsigned int const &>": {"offset": "0x73290"}, "std::_Hash<std::_Umap_traits<unsigned int,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Try_emplace<unsigned int>": {"offset": "0x73290"}, "std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,1> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,1> >::~_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,1> >": {"offset": "0x2A7B0"}, "std::_Hash<std::_Umap_traits<unsigned int,void *,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,void *> >,0> >::_Forced_rehash": {"offset": "0x21270"}, "std::_Hash<std::_Umap_traits<unsigned int,void *,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,void *> >,0> >::emplace<unsigned int &,NewTypeId * &>": {"offset": "0x2A120"}, "std::_Hash<std::_Umap_traits<unsigned int,void *,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,void *> >,0> >::~_Hash<std::_Umap_traits<unsigned int,void *,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,void *> >,0> >": {"offset": "0x2A700"}, "std::_Hash<std::_Uset_traits<int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<int>,0> >::_Forced_rehash": {"offset": "0x2C020"}, "std::_Hash<std::_Uset_traits<int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<int>,0> >::emplace<int>": {"offset": "0x2A400"}, "std::_Hash<std::_Uset_traits<int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<int>,0> >::~_Hash<std::_Uset_traits<int,std::_Uhash_compare<int,std::hash<int>,std::equal_to<int> >,std::allocator<int>,0> >": {"offset": "0x2A7B0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x22640"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Forced_rehash": {"offset": "0x75C40"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x737D0"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::_Forced_rehash": {"offset": "0x2C020"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::_Unchecked_erase": {"offset": "0x2C1E0"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::clear": {"offset": "0x2C3B0"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::emplace<unsigned int const &>": {"offset": "0x2A400"}, "std::_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >::~_Hash<std::_Uset_traits<unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<unsigned int>,0> >": {"offset": "0x2A7B0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<int> >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<int> >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<int> >,std::_Iterator_base0> > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> > > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,MapTypesFile> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,MapTypesFile> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,MapTypesFile> > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > > > > >::_Assign_grow": {"offset": "0x6E960"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > > > > >": {"offset": "0x6D1E0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,int> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,int> > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >": {"offset": "0x1D9F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,void *> > > > > >::_Assign_grow": {"offset": "0x21040"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,void *> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,void *> > > > > >": {"offset": "0x1D9F0"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x299A0"}, "std::_Inplace_merge_buffer_left<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x350F0"}, "std::_Inplace_merge_buffer_right<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x352A0"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x1C010"}, "std::_Insertion_sort_isort_max_chunks<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x35980"}, "std::_Insertion_sort_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x35C20"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Free_non_head<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x261C0"}, "std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *> > >": {"offset": "0x34D10"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0x34DB0"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *>::_Freenode<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0x34E50"}, "std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x34DB0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<int,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<int,void *> > >": {"offset": "0x2A6C0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x73B30"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A7E0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,void *> > >": {"offset": "0x3A820"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,void *> > >": {"offset": "0x3A8B0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,void *> > >": {"offset": "0x22C20"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,void *> > >": {"offset": "0x3A8B0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,MapTypesFile>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,MapTypesFile>,void *> > >": {"offset": "0x1DA50"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,void *> > >": {"offset": "0x2A6E0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,int>,void *> > >": {"offset": "0x2A6C0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A7E0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >": {"offset": "0x2A6C0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,void *>,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,void *>,void *> > >": {"offset": "0x2A6E0"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<unsigned int,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<unsigned int,void *> > >": {"offset": "0x2A6C0"}, "std::_Make_heap_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x35E50"}, "std::_Maklocstr<char>": {"offset": "0x1A770"}, "std::_Med3_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x35FD0"}, "std::_Med3_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x36100"}, "std::_Med3_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x36070"}, "std::_Move_backward_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *>": {"offset": "0x361D0"}, "std::_Move_unchecked<jitasm::Instr *,jitasm::Instr *>": {"offset": "0x36290"}, "std::_Move_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *>": {"offset": "0x36230"}, "std::_Optimistic_temporary_buffer<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Optimistic_temporary_buffer<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x3A940"}, "std::_Partition_by_median_guess_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x362C0"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x36880"}, "std::_Partition_by_median_guess_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x365F0"}, "std::_Pop_heap_hole_by_index<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64>,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x36BE0"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x36E10"}, "std::_Pop_heap_hole_by_index<unsigned __int64 *,unsigned __int64,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x36D10"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0x21030"}, "std::_Ref_count_obj2<GFxValue>::_Delete_this": {"offset": "0x25520"}, "std::_Ref_count_obj2<GFxValue>::_Destroy": {"offset": "0x6F9E0"}, "std::_Ref_count_obj2<StreamingPackfileEntry>::_Delete_this": {"offset": "0x25520"}, "std::_Ref_count_obj2<StreamingPackfileEntry>::_Destroy": {"offset": "0x16730"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0x25520"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0x62D40"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Delete_this": {"offset": "0x25520"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<int> >::_Destroy": {"offset": "0x58F00"}, "std::_Sort_unchecked<jitasm::compiler::OrderedLabel *,std::less<void> >": {"offset": "0x377F0"}, "std::_Sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,jitasm::compiler::ControlFlowGraph::sort_backedge>": {"offset": "0x375D0"}, "std::_Sort_unchecked<unsigned __int64 *,jitasm::compiler::Lifetime::LessCost>": {"offset": "0x37FA0"}, "std::_Sort_unchecked<unsigned __int64 *,std::_Ref_fn<jitasm::compiler::Lifetime::LessAssignOrder> >": {"offset": "0x37CB0"}, "std::_Stable_sort_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x382E0"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x28CE0"}, "std::_Throw_bad_array_new_length": {"offset": "0x194B0"}, "std::_Throw_bad_cast": {"offset": "0x21470"}, "std::_Throw_tree_length_error": {"offset": "0x194D0"}, "std::_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3A950"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x7E990"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,MinimapOverlayLoadRequest,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> >,0> >::_Emplace<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> >": {"offset": "0x70090"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,MinimapOverlayLoadRequest,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x25EF0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,MinimapOverlayLoadRequest,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> >,0> >::_Erase": {"offset": "0x71660"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x32D60"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x25EF0"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<void>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> >::_Erase": {"offset": "0x53470"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0xECA0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0xEF20"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0x16790"}, "std::_Tree<std::_Tmap_traits<unsigned int,std::pair<int,int>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::pair<int,int> > >,1> >::_Erase": {"offset": "0x532A0"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::_Find_hint<int>": {"offset": "0x344C0"}, "std::_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >::~_Tree<std::_Tset_traits<int,std::less<int>,std::allocator<int>,0> >": {"offset": "0x3A970"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,IgnoreCaseLess,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::erase": {"offset": "0x55B90"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Emplace<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x6FEF0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x25EF0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_hint<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x34750"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x349F0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::insert<0,0>": {"offset": "0x790E0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::~_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >": {"offset": "0x3A9D0"}, "std::_Tree<std::_Tset_traits<std::pair<streaming::strStreamingModule *,int>,std::less<std::pair<streaming::strStreamingModule *,int> >,std::allocator<std::pair<streaming::strStreamingModule *,int> >,0> >::_Erase": {"offset": "0x537D0"}, "std::_Tree<std::_Tset_traits<std::pair<streaming::strStreamingModule *,int>,std::less<std::pair<streaming::strStreamingModule *,int> >,std::allocator<std::pair<streaming::strStreamingModule *,int> >,0> >::clear": {"offset": "0x55770"}, "std::_Tree<std::_Tset_traits<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::less<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Eqrange<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x33E30"}, "std::_Tree<std::_Tset_traits<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::less<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Erase": {"offset": "0x53470"}, "std::_Tree<std::_Tset_traits<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::less<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Erase_unchecked": {"offset": "0x53960"}, "std::_Tree<std::_Tset_traits<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::less<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Find_lower_bound<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x34AC0"}, "std::_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >::~_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >": {"offset": "0x86D00"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x34EC0"}, "std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >": {"offset": "0x703B0"}, "std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3AA00"}, "std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::pair<int,int> >,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::pair<int,int> >,void *> > >": {"offset": "0x3A760"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x16770"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >": {"offset": "0x3A780"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x3A7A0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Erase_tree<std::allocator<std::_Tree_node<int,void *> > >": {"offset": "0x341E0"}, "std::_Tree_val<std::_Tree_simple_types<int> >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x34400"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,int>,void *> > >": {"offset": "0x34240"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,int> > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,std::shared_ptr<GFxValue> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<int const ,std::shared_ptr<GFxValue> >,void *> > >": {"offset": "0x70240"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<int const ,std::shared_ptr<GFxValue> > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>,void *> > >": {"offset": "0x702F0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest> > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x342A0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xEC40"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::tuple<streaming::strStreamingModule *,unsigned int> const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::tuple<streaming::strStreamingModule *,unsigned int> const ,unsigned int>,void *> > >": {"offset": "0x73230"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::tuple<streaming::strStreamingModule *,unsigned int> const ,unsigned int> > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<streaming::strStreamingModule *,int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<streaming::strStreamingModule *,int>,void *> > >": {"offset": "0x343A0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<streaming::strStreamingModule *,int> > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<streaming::strStreamingModule *,int> > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<streaming::strStreamingModule *,int> > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<streaming::strStreamingModule *,int> > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x26080"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::pair<int,int> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::pair<int,int> >,void *> > >": {"offset": "0x34240"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::pair<int,int> > > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::pair<int,int> > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::pair<int,int> > > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::pair<int,int> > > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x342A0"}, "std::_Tree_val<std::_Tree_simple_types<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Extract": {"offset": "0x27F60"}, "std::_Tree_val<std::_Tree_simple_types<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0x19250"}, "std::_Tree_val<std::_Tree_simple_types<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Lrotate": {"offset": "0x28330"}, "std::_Tree_val<std::_Tree_simple_types<std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Rrotate": {"offset": "0x28390"}, "std::_Tree_val<std::_Tree_simple_types<tbb::detail::d1::global_control *> >::_Erase_tree<tbb::detail::d1::tbb_allocator<std::_Tree_node<tbb::detail::d1::global_control *,void *> > >": {"offset": "0x86C90"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1DAA0"}, "std::_Uninitialized_backout_al<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~_Uninitialized_backout_al<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x3AA40"}, "std::_Uninitialized_copy_n<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x38C90"}, "std::_Uninitialized_merge_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,<lambda_39060df8cb3a2fcec89be66c996a24e9> >": {"offset": "0x38D20"}, "std::_Uninitialized_move<jitasm::Instr *,std::allocator<jitasm::Instr> >": {"offset": "0x38FE0"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1C580"}, "std::_Uninitialized_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags> *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags> > >": {"offset": "0x26410"}, "std::_Uninitialized_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x38F40"}, "std::_Uninitialized_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int> *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int> > >": {"offset": "0x38EB0"}, "std::_Uninitialized_move<std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x38EB0"}, "std::_Uninitialized_move_unchecked<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *>": {"offset": "0x39040"}, "std::_Xlen_string": {"offset": "0x194F0"}, "std::allocator<CMapDataContents *>::deallocate": {"offset": "0x55A00"}, "std::allocator<char>::allocate": {"offset": "0x19510"}, "std::allocator<char>::deallocate": {"offset": "0x257C0"}, "std::allocator<float>::allocate": {"offset": "0x21750"}, "std::allocator<float>::deallocate": {"offset": "0x21900"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x54990"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x55A00"}, "std::allocator<int>::allocate": {"offset": "0x21750"}, "std::allocator<int>::deallocate": {"offset": "0x21900"}, "std::allocator<jitasm::Instr>::allocate": {"offset": "0x54A70"}, "std::allocator<jitasm::Instr>::deallocate": {"offset": "0x55AA0"}, "std::allocator<jitasm::compiler::BasicBlock *>::allocate": {"offset": "0x54990"}, "std::allocator<jitasm::compiler::BasicBlock *>::deallocate": {"offset": "0x55A00"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::allocate": {"offset": "0x54AE0"}, "std::allocator<jitasm::compiler::Lifetime::Interval>::deallocate": {"offset": "0x55AF0"}, "std::allocator<jitasm::compiler::OrderedLabel>::allocate": {"offset": "0x2E9B0"}, "std::allocator<jitasm::compiler::OrderedLabel>::deallocate": {"offset": "0x2EA20"}, "std::allocator<jitasm::compiler::RegUsePoint>::deallocate": {"offset": "0x2EA20"}, "std::allocator<jitasm::compiler::VarAttribute>::deallocate": {"offset": "0x55B40"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x217C0"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x21950"}, "std::allocator<std::pair<int,int> >::deallocate": {"offset": "0x55A00"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags> >::deallocate": {"offset": "0x28440"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::allocate": {"offset": "0x54A00"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x55A50"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int> >::deallocate": {"offset": "0x28440"}, "std::allocator<std::pair<unsigned __int64,unsigned __int64> >::deallocate": {"offset": "0x2EA20"}, "std::allocator<std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x28440"}, "std::allocator<std::tuple<streaming::strStreamingModule *,int> >::allocate": {"offset": "0x2E9B0"}, "std::allocator<std::tuple<streaming::strStreamingModule *,int> >::deallocate": {"offset": "0x2EA20"}, "std::allocator<unsigned __int64>::allocate": {"offset": "0x54990"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x55A00"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x257C0"}, "std::allocator<unsigned int>::allocate": {"offset": "0x21750"}, "std::allocator<unsigned int>::deallocate": {"offset": "0x21900"}, "std::allocator<wchar_t>::allocate": {"offset": "0x19570"}, "std::bad_alloc::bad_alloc": {"offset": "0x164F0"}, "std::bad_alloc::~bad_alloc": {"offset": "0x16980"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x16570"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0x16980"}, "std::bad_cast::bad_cast": {"offset": "0x1D670"}, "std::bad_cast::~bad_cast": {"offset": "0x16980"}, "std::basic_filebuf<char,std::char_traits<char> >::_Endwrite": {"offset": "0x21180"}, "std::basic_filebuf<char,std::char_traits<char> >::_Lock": {"offset": "0x1A950"}, "std::basic_filebuf<char,std::char_traits<char> >::_Reset_back": {"offset": "0x21430"}, "std::basic_filebuf<char,std::char_traits<char> >::_Unlock": {"offset": "0x1A970"}, "std::basic_filebuf<char,std::char_traits<char> >::imbue": {"offset": "0x1B450"}, "std::basic_filebuf<char,std::char_traits<char> >::overflow": {"offset": "0x1A990"}, "std::basic_filebuf<char,std::char_traits<char> >::pbackfail": {"offset": "0x1AB50"}, "std::basic_filebuf<char,std::char_traits<char> >::seekoff": {"offset": "0x1B1B0"}, "std::basic_filebuf<char,std::char_traits<char> >::seekpos": {"offset": "0x1B280"}, "std::basic_filebuf<char,std::char_traits<char> >::setbuf": {"offset": "0x1B320"}, "std::basic_filebuf<char,std::char_traits<char> >::sync": {"offset": "0x1B400"}, "std::basic_filebuf<char,std::char_traits<char> >::uflow": {"offset": "0x1ACF0"}, "std::basic_filebuf<char,std::char_traits<char> >::underflow": {"offset": "0x1AC70"}, "std::basic_filebuf<char,std::char_traits<char> >::xsgetn": {"offset": "0x1AFB0"}, "std::basic_filebuf<char,std::char_traits<char> >::xsputn": {"offset": "0x1B0F0"}, "std::basic_filebuf<char,std::char_traits<char> >::~basic_filebuf<char,std::char_traits<char> >": {"offset": "0x1DAF0"}, "std::basic_ofstream<char,std::char_traits<char> >::basic_ofstream<char,std::char_traits<char> >": {"offset": "0x1CD10"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x1DFD0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x1E2C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0xEB80"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_from_iter<boost::iterators::transform_iterator<boost::algorithm::detail::to_upperF<char>,std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > >,boost::use_default,boost::use_default>,boost::iterators::transform_iterator<boost::algorithm::detail::to_upperF<char>,std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > >,boost::use_default,boost::use_default>,std::nullptr_t>": {"offset": "0x329D0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x1C280"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x1C3F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x81090"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0x16850"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x61900"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0x19760"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x16310"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0x30350"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str": {"offset": "0x54BD0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x16850"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x7B450"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>": {"offset": "0x6F5E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0x168B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0x195E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x6F760"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x82DD0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x82F30"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x168B0"}, "std::basic_string_view<char,std::char_traits<char> >::basic_string_view<char,std::char_traits<char> >": {"offset": "0x39DA0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x219E0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x21B50"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x21CB0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x21E30"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x21BE0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::~basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x22CA0"}, "std::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >::str": {"offset": "0x25800"}, "std::call_once<<lambda_f5263b7659a0349927d6254c3c7dfeb0> >": {"offset": "0x29770"}, "std::deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >::~deque<jitasm::Frontend::ControlState,std::allocator<jitasm::Frontend::ControlState> >": {"offset": "0x3AAB0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Growmap": {"offset": "0x53E50"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Tidy": {"offset": "0x54310"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::_Xlen": {"offset": "0x54820"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x39DD0"}, "std::deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >::~deque<jitasm::Frontend::Label,std::allocator<jitasm::Frontend::Label> >": {"offset": "0x3AB80"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Growmap": {"offset": "0x53C70"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Tidy": {"offset": "0x54250"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::_Xlen": {"offset": "0x54820"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_back": {"offset": "0x56DE0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::push_front": {"offset": "0x56EA0"}, "std::deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >::~deque<jitasm::compiler::BasicBlock *,std::allocator<jitasm::compiler::BasicBlock *> >": {"offset": "0x3AA80"}, "std::distance<std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x39220"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x22A10"}, "std::exception::exception": {"offset": "0x165A0"}, "std::exception::what": {"offset": "0x1A640"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<int> >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<int> >,std::_Iterator_base0> >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<unsigned int> >,std::_Iterator_base0> >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,MapTypesFile> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,MapTypesFile> > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > > > >": {"offset": "0x6D0A0"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,int> > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >": {"offset": "0x1C940"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,void *> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,void *> > > > >": {"offset": "0x1C940"}, "std::function<bool __cdecl(CDataFileMountInterface *,CDataFileMgr::DataFile &)>::~function<bool __cdecl(CDataFileMountInterface *,CDataFileMgr::DataFile &)>": {"offset": "0x1D9C0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x1D9C0"}, "std::function<bool __cdecl(char const *)>::~function<bool __cdecl(char const *)>": {"offset": "0x1D9C0"}, "std::function<bool __cdecl(enum rage::InitFunctionType)>::~function<bool __cdecl(enum rage::InitFunctionType)>": {"offset": "0x1D9C0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x1D9C0"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x1D9C0"}, "std::function<void __cdecl(int const &)>::~function<void __cdecl(int const &)>": {"offset": "0x1D9C0"}, "std::function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<void __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1D9C0"}, "std::function<void __cdecl(unsigned int)>::~function<void __cdecl(unsigned int)>": {"offset": "0x1D9C0"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)>": {"offset": "0x73AB0"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x1D9C0"}, "std::invalid_argument::invalid_argument": {"offset": "0x85F50"}, "std::invalid_argument::~invalid_argument": {"offset": "0x16980"}, "std::length_error::length_error": {"offset": "0x85FE0"}, "std::length_error::~length_error": {"offset": "0x16980"}, "std::list<int,std::allocator<int> >::~list<int,std::allocator<int> >": {"offset": "0x2A860"}, "std::list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x264C0"}, "std::list<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~list<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x3ABB0"}, "std::list<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,std::allocator<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > >::~list<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > >,std::allocator<std::pair<int const ,std::list<unsigned int,std::allocator<unsigned int> > > > >": {"offset": "0x3AC20"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,int> > >": {"offset": "0x3AC50"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> > > >": {"offset": "0x22DB0"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> > > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::tuple<rage::fiDevice *,unsigned __int64,unsigned __int64> > > >": {"offset": "0x5F6E0"}, "std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >::~list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,unsigned int> > >": {"offset": "0x3AC50"}, "std::list<std::pair<unsigned int const ,MapTypesFile>,std::allocator<std::pair<unsigned int const ,MapTypesFile> > >::~list<std::pair<unsigned int const ,MapTypesFile>,std::allocator<std::pair<unsigned int const ,MapTypesFile> > >": {"offset": "0x1DC80"}, "std::list<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > >::~list<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap>,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > >": {"offset": "0x6D240"}, "std::list<std::pair<unsigned int const ,int>,std::allocator<std::pair<unsigned int const ,int> > >::~list<std::pair<unsigned int const ,int>,std::allocator<std::pair<unsigned int const ,int> > >": {"offset": "0x2A860"}, "std::list<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~list<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x3ABB0"}, "std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >": {"offset": "0x2A860"}, "std::list<std::pair<unsigned int const ,void *>,std::allocator<std::pair<unsigned int const ,void *> > >::~list<std::pair<unsigned int const ,void *>,std::allocator<std::pair<unsigned int const ,void *> > >": {"offset": "0x2A8C0"}, "std::list<unsigned int,std::allocator<unsigned int> >::push_front": {"offset": "0x56F60"}, "std::list<unsigned int,std::allocator<unsigned int> >::~list<unsigned int,std::allocator<unsigned int> >": {"offset": "0x2A860"}, "std::list<void *,std::allocator<void *> >::~list<void *,std::allocator<void *> >": {"offset": "0x2A860"}, "std::locale::~locale": {"offset": "0x1E190"}, "std::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>": {"offset": "0x872C0"}, "std::logic_error::logic_error": {"offset": "0x86030"}, "std::map<int,int,std::less<int>,std::allocator<std::pair<int const ,int> > >::~map<int,int,std::less<int>,std::allocator<std::pair<int const ,int> > >": {"offset": "0x3AC80"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x7F0C0"}, "std::numpunct<char>::do_falsename": {"offset": "0x7F0D0"}, "std::numpunct<char>::do_grouping": {"offset": "0x7F110"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x7F150"}, "std::numpunct<char>::do_truename": {"offset": "0x7F160"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x7E7E0"}, "std::out_of_range::out_of_range": {"offset": "0x86110"}, "std::out_of_range::~out_of_range": {"offset": "0x16980"}, "std::pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3ACE0"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,MinimapOverlayLoadRequest>": {"offset": "0x16850"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3AD50"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<StreamingPackfileEntry> >": {"offset": "0x22E20"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,rage::ResourceFlags>": {"offset": "0x16850"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3AD50"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int>::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,unsigned int>": {"offset": "0x16850"}, "std::pair<unsigned int const ,MapTypesFile>::~pair<unsigned int const ,MapTypesFile>": {"offset": "0x1DCF0"}, "std::pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<unsigned int const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3ACE0"}, "std::rotate<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<jitasm::compiler::BasicBlock *> > > >": {"offset": "0x39800"}, "std::runtime_error::runtime_error": {"offset": "0x86160"}, "std::runtime_error::~runtime_error": {"offset": "0x16980"}, "std::set<int,std::less<int>,std::allocator<int> >::~set<int,std::less<int>,std::allocator<int> >": {"offset": "0x3A970"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x39E20"}, "std::set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~set<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x3A9D0"}, "std::shared_lock<std::shared_mutex>::~shared_lock<std::shared_mutex>": {"offset": "0x3AE70"}, "std::shared_ptr<GFxValue>::~shared_ptr<GFxValue>": {"offset": "0x22ED0"}, "std::shared_ptr<StreamingPackfileEntry>::~shared_ptr<StreamingPackfileEntry>": {"offset": "0x22ED0"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x22ED0"}, "std::shared_ptr<internal::ConsoleVariableEntry<int> >::~shared_ptr<internal::ConsoleVariableEntry<int> >": {"offset": "0x22ED0"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x22ED0"}, "std::to_string": {"offset": "0x25910"}, "std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > ><unsigned int &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &,0>": {"offset": "0x30320"}, "std::tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<int,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x16850"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x16850"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x3AD50"}, "std::unique_lock<std::mutex>::~unique_lock<std::mutex>": {"offset": "0x22F20"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0x73BC0"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x5A1C0"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0x1DD00"}, "std::unique_ptr<fwEvent<DamageEventMetaData const &>::callback,std::default_delete<fwEvent<DamageEventMetaData const &>::callback> >::~unique_ptr<fwEvent<DamageEventMetaData const &>::callback,std::default_delete<fwEvent<DamageEventMetaData const &>::callback> >": {"offset": "0x1DD00"}, "std::unique_ptr<fwEvent<GameEventData const &>::callback,std::default_delete<fwEvent<GameEventData const &>::callback> >::~unique_ptr<fwEvent<GameEventData const &>::callback,std::default_delete<fwEvent<GameEventData const &>::callback> >": {"offset": "0x1DD00"}, "std::unique_ptr<fwEvent<GameEventMetaData const &>::callback,std::default_delete<fwEvent<GameEventMetaData const &>::callback> >::~unique_ptr<fwEvent<GameEventMetaData const &>::callback,std::default_delete<fwEvent<GameEventMetaData const &>::callback> >": {"offset": "0x1DD00"}, "std::unique_ptr<fwEvent<PopulationCreationState *>::callback,std::default_delete<fwEvent<PopulationCreationState *>::callback> >::~unique_ptr<fwEvent<PopulationCreationState *>::callback,std::default_delete<fwEvent<PopulationCreationState *>::callback> >": {"offset": "0x1DD00"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x1DD10"}, "std::unordered_map<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > >::~unordered_map<unsigned int,`ValidateGeometry'::`72'::PolyEdgeMap,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,`ValidateGeometry'::`72'::PolyEdgeMap> > >": {"offset": "0x6D2A0"}, "std::use_facet<std::codecvt<char,char,_Mbstatet> >": {"offset": "0x1CBB0"}, "std::use_facet<std::ctype<char> >": {"offset": "0x39C70"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x7E4B0"}, "streaming::AddDataFileToLoadList": {"offset": "0x3EF50"}, "streaming::AddDefMetaToLoadList": {"offset": "0x3F1E0"}, "streaming::AddMetaToLoadList": {"offset": "0x3F230"}, "streaming::GetRawStreamerByIndex": {"offset": "0x71FC0"}, "streaming::GetRawStreamerForFile": {"offset": "0x71FD0"}, "streaming::GetStreamingBaseNameForHash": {"offset": "0x75510"}, "streaming::GetStreamingIndexForLocalHashKey": {"offset": "0x75620"}, "streaming::GetStreamingIndexForName": {"offset": "0x75680"}, "streaming::GetStreamingNameForIndex": {"offset": "0x757C0"}, "streaming::GetStreamingPackfileByIndex": {"offset": "0x27B40"}, "streaming::GetStreamingPackfileForEntry": {"offset": "0x27B60"}, "streaming::IsRawHandle": {"offset": "0x48790"}, "streaming::IsStreamerShuttingDown": {"offset": "0x487A0"}, "streaming::LoadObjectsNow": {"offset": "0x720C0"}, "streaming::Manager::FindAllDependents": {"offset": "0x71DA0"}, "streaming::Manager::FindDependentsInner": {"offset": "0x71DB0"}, "streaming::Manager::GetInstance": {"offset": "0x71FA0"}, "streaming::Manager::IsObjectReadyToDelete": {"offset": "0x720B0"}, "streaming::Manager::Manager": {"offset": "0x1D3A0"}, "streaming::Manager::RegisterObject": {"offset": "0x720D0"}, "streaming::Manager::ReleaseObject": {"offset": "0x721F0"}, "streaming::Manager::RequestObject": {"offset": "0x72200"}, "streaming::Manager::UnregisterObject": {"offset": "0x72210"}, "streaming::Manager::~Manager": {"offset": "0x1DED0"}, "streaming::RegisterRawStreamingFile": {"offset": "0x720F0"}, "streaming::RemoveDataFileFromLoadList": {"offset": "0x4FA80"}, "streaming::strStreamingModuleMgr::GetStreamingModule": {"offset": "0x720A0"}, "streaming::strStreamingModuleMgr::strStreamingModuleMgr": {"offset": "0x1D990"}, "streaming::strStreamingModuleMgr::~strStreamingModuleMgr": {"offset": "0x1E310"}, "tbb::detail::d0::raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >::~raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >": {"offset": "0x5F7A0"}, "tbb::detail::d0::raii_guard<<lambda_2abf46fda104b340581b3b32bec86e55> >::~raii_guard<<lambda_2abf46fda104b340581b3b32bec86e55> >": {"offset": "0x5F7A0"}, "tbb::detail::d0::raii_guard<<lambda_62f6282f9e0ccd767dd6cd7995d2adb2> >::~raii_guard<<lambda_62f6282f9e0ccd767dd6cd7995d2adb2> >": {"offset": "0x5F7A0"}, "tbb::detail::d0::raii_guard<<lambda_8bd0887c7dc64179bbe9ac0ebe78d5ea> >::~raii_guard<<lambda_8bd0887c7dc64179bbe9ac0ebe78d5ea> >": {"offset": "0x5F870"}, "tbb::detail::d1::unique_scoped_lock<tbb::detail::d1::spin_mutex>::~unique_scoped_lock<tbb::detail::d1::spin_mutex>": {"offset": "0x86D70"}, "tbb::detail::d2::concurrent_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::clear": {"offset": "0x61990"}, "tbb::detail::d2::concurrent_queue<std::list<unsigned int,std::allocator<unsigned int> >,tbb::detail::d1::cache_aligned_allocator<std::list<unsigned int,std::allocator<unsigned int> > > >::clear": {"offset": "0x61C90"}, "tbb::detail::d2::concurrent_queue<void *,tbb::detail::d1::cache_aligned_allocator<void *> >::internal_try_pop": {"offset": "0x62230"}, "tbb::detail::d2::concurrent_queue<void *,tbb::detail::d1::cache_aligned_allocator<void *> >::~concurrent_queue<void *,tbb::detail::d1::cache_aligned_allocator<void *> >": {"offset": "0x5F630"}, "tbb::detail::d2::micro_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::prepare_page": {"offset": "0x62A60"}, "tbb::detail::d2::micro_queue<std::list<unsigned int,std::allocator<unsigned int> >,tbb::detail::d1::cache_aligned_allocator<std::list<unsigned int,std::allocator<unsigned int> > > >::prepare_page": {"offset": "0x62BD0"}, "tbb::detail::d2::micro_queue<void *,tbb::detail::d1::cache_aligned_allocator<void *> >::prepare_page": {"offset": "0x628F0"}, "tbb::detail::r1::AvailableHwConcurrency": {"offset": "0x86FC0"}, "tbb::detail::r1::PrintExtraVersionInfo": {"offset": "0x86DE0"}, "tbb::detail::r1::`dynamic initializer for '__TBB_InitOnceHiddenInstance''": {"offset": "0xDCE0"}, "tbb::detail::r1::`dynamic initializer for 'allowed_parallelism_ctl''": {"offset": "0xDB70"}, "tbb::detail::r1::`dynamic initializer for 'lifetime_ctl''": {"offset": "0xDBC0"}, "tbb::detail::r1::`dynamic initializer for 'stack_size_ctl''": {"offset": "0xDC10"}, "tbb::detail::r1::`dynamic initializer for 'terminate_on_exception_ctl''": {"offset": "0xDC60"}, "tbb::detail::r1::allocate_memory": {"offset": "0x866E0"}, "tbb::detail::r1::allowed_parallelism_control::active_value": {"offset": "0x86A10"}, "tbb::detail::r1::allowed_parallelism_control::apply_active": {"offset": "0x86A00"}, "tbb::detail::r1::allowed_parallelism_control::default_value": {"offset": "0x86970"}, "tbb::detail::r1::allowed_parallelism_control::is_first_arg_preferred": {"offset": "0x869F0"}, "tbb::detail::r1::arena::has_enqueued_tasks": {"offset": "0x87DA0"}, "tbb::detail::r1::bad_last_alloc::bad_last_alloc": {"offset": "0x85EE0"}, "tbb::detail::r1::bad_last_alloc::what": {"offset": "0x86650"}, "tbb::detail::r1::bad_last_alloc::~bad_last_alloc": {"offset": "0x16980"}, "tbb::detail::r1::cache_aligned_allocate": {"offset": "0x86710"}, "tbb::detail::r1::cache_aligned_deallocate": {"offset": "0x86770"}, "tbb::detail::r1::clear_address_waiter_table": {"offset": "0x87B30"}, "tbb::detail::r1::concurrent_monitor_mutex::get_semaphore": {"offset": "0x87520"}, "tbb::detail::r1::control_storage::active_value": {"offset": "0x868E0"}, "tbb::detail::r1::control_storage::apply_active": {"offset": "0x868C0"}, "tbb::detail::r1::control_storage::is_first_arg_preferred": {"offset": "0x868D0"}, "tbb::detail::r1::deallocate_memory": {"offset": "0x86780"}, "tbb::detail::r1::detect_cpu_features": {"offset": "0x86EB0"}, "tbb::detail::r1::do_throw<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x85AD0"}, "tbb::detail::r1::do_throw<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x85B00"}, "tbb::detail::r1::do_throw<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x85B30"}, "tbb::detail::r1::do_throw<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x85B60"}, "tbb::detail::r1::do_throw<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x85B90"}, "tbb::detail::r1::do_throw<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x85BC0"}, "tbb::detail::r1::do_throw<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x85BF0"}, "tbb::detail::r1::do_throw<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x85C20"}, "tbb::detail::r1::do_throw<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x85C50"}, "tbb::detail::r1::do_throw<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x85C80"}, "tbb::detail::r1::do_throw<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x85CB0"}, "tbb::detail::r1::do_throw<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x85CE0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x85D10"}, "tbb::detail::r1::do_throw_noexcept<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x85D30"}, "tbb::detail::r1::do_throw_noexcept<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x85D50"}, "tbb::detail::r1::do_throw_noexcept<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x85D70"}, "tbb::detail::r1::do_throw_noexcept<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x85D90"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x85DB0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x85DD0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x85DF0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x85E10"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x85E30"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x85E50"}, "tbb::detail::r1::do_throw_noexcept<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x85E70"}, "tbb::detail::r1::dummy_allocate_binding_handler": {"offset": "0x21030"}, "tbb::detail::r1::dummy_apply_affinity": {"offset": "0x16730"}, "tbb::detail::r1::dummy_deallocate_binding_handler": {"offset": "0x16730"}, "tbb::detail::r1::dummy_destroy_system_topology": {"offset": "0x16730"}, "tbb::detail::r1::dummy_get_default_concurrency": {"offset": "0x87A90"}, "tbb::detail::r1::dummy_restore_affinity": {"offset": "0x16730"}, "tbb::detail::r1::dynamic_link": {"offset": "0x86DD0"}, "tbb::detail::r1::dynamic_unlink": {"offset": "0x16730"}, "tbb::detail::r1::dynamic_unlink_all": {"offset": "0x16730"}, "tbb::detail::r1::gcc_rethrow_exception_broken": {"offset": "0x28780"}, "tbb::detail::r1::get_address_waiter_table": {"offset": "0x87CD0"}, "tbb::detail::r1::governor::acquire_resources": {"offset": "0x87AA0"}, "tbb::detail::r1::governor::default_num_threads": {"offset": "0x874A0"}, "tbb::detail::r1::governor::release_resources": {"offset": "0x87AF0"}, "tbb::detail::r1::handle_perror": {"offset": "0x864D0"}, "tbb::detail::r1::initialize_allocate_handler": {"offset": "0x86680"}, "tbb::detail::r1::initialize_cache_aligned_allocate_handler": {"offset": "0x866B0"}, "tbb::detail::r1::initialize_cache_aligned_allocator": {"offset": "0x86790"}, "tbb::detail::r1::initialize_hardware_concurrency_info": {"offset": "0x87080"}, "tbb::detail::r1::lifetime_control::apply_active": {"offset": "0x86B20"}, "tbb::detail::r1::lifetime_control::default_value": {"offset": "0x21030"}, "tbb::detail::r1::lifetime_control::is_first_arg_preferred": {"offset": "0x28780"}, "tbb::detail::r1::market::add_ref_unsafe": {"offset": "0x873A0"}, "tbb::detail::r1::market::app_parallelism_limit": {"offset": "0x86D90"}, "tbb::detail::r1::market::release": {"offset": "0x875C0"}, "tbb::detail::r1::market::set_active_num_workers": {"offset": "0x87740"}, "tbb::detail::r1::market::update_allotment": {"offset": "0x87980"}, "tbb::detail::r1::missing_wait::missing_wait": {"offset": "0x860B0"}, "tbb::detail::r1::missing_wait::what": {"offset": "0x86660"}, "tbb::detail::r1::missing_wait::~missing_wait": {"offset": "0x16980"}, "tbb::detail::r1::runtime_warning": {"offset": "0x86F00"}, "tbb::detail::r1::stack_size_control::apply_active": {"offset": "0x868C0"}, "tbb::detail::r1::stack_size_control::default_value": {"offset": "0x86B10"}, "tbb::detail::r1::std_cache_aligned_allocate": {"offset": "0x868A0"}, "tbb::detail::r1::std_cache_aligned_deallocate": {"offset": "0x868B0"}, "tbb::detail::r1::terminate_on_exception": {"offset": "0x86DB0"}, "tbb::detail::r1::terminate_on_exception_control::default_value": {"offset": "0x21030"}, "tbb::detail::r1::throw_exception": {"offset": "0x865A0"}, "tbb::detail::r1::unsafe_wait::unsafe_wait": {"offset": "0x861F0"}, "tbb::detail::r1::unsafe_wait::~unsafe_wait": {"offset": "0x16980"}, "tbb::detail::r1::user_abort::user_abort": {"offset": "0x86280"}, "tbb::detail::r1::user_abort::what": {"offset": "0x86670"}, "tbb::detail::r1::user_abort::~user_abort": {"offset": "0x16980"}, "utf8::exception::exception": {"offset": "0x82160"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x811E0"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x81B10"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x821F0"}, "utf8::invalid_code_point::what": {"offset": "0x830E0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0x16980"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x82260"}, "utf8::invalid_utf8::what": {"offset": "0x830F0"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0x16980"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x822C0"}, "utf8::not_enough_room::what": {"offset": "0x83100"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0x16980"}, "va<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x22A50"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x818F0"}, "vva": {"offset": "0x830C0"}, "xbr::GetGameBuild": {"offset": "0x20470"}, "xbr::GetReplaceExecutableInit": {"offset": "0x83A50"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x83C70"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1D50"}}}