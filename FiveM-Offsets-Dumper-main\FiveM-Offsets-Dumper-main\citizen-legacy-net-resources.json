{"citizen-legacy-net-resources.dll": {"<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>::~<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>": {"offset": "0x1C250"}, "<lambda_22efe08b9cf28c27189cd77a7cbf0d03>::~<lambda_22efe08b9cf28c27189cd77a7cbf0d03>": {"offset": "0x1C2C0"}, "<lambda_4bd345ce870bcb6a9dba57dde6595eaf>::<lambda_4bd345ce870bcb6a9dba57dde6595eaf>": {"offset": "0x3CBF0"}, "<lambda_72ae5b0e2f813e08b01a5e2876e67561>::~<lambda_72ae5b0e2f813e08b01a5e2876e67561>": {"offset": "0x1C3A0"}, "<lambda_814d5e0b3a45fa1dd158672480f6c4ca>::~<lambda_814d5e0b3a45fa1dd158672480f6c4ca>": {"offset": "0x1C3A0"}, "<lambda_8bea7dc45d5ccdc05cd4c7e496964c06>::~<lambda_8bea7dc45d5ccdc05cd4c7e496964c06>": {"offset": "0x1C3D0"}, "<lambda_93f9a070d138a1ef45eae8a2debfa335>::~<lambda_93f9a070d138a1ef45eae8a2debfa335>": {"offset": "0x1C400"}, "<lambda_9972b16a2e64036ea33e6044fce7be78>::~<lambda_9972b16a2e64036ea33e6044fce7be78>": {"offset": "0x1C480"}, "<lambda_a9ea5f9372bb612812c52de723ed7985>::~<lambda_a9ea5f9372bb612812c52de723ed7985>": {"offset": "0x1C530"}, "<lambda_b00284be9ce584f6e333a1bd27218e06>::~<lambda_b00284be9ce584f6e333a1bd27218e06>": {"offset": "0x1C3A0"}, "<lambda_bb63d0011cba966169dbe6af3f2322ad>::~<lambda_bb63d0011cba966169dbe6af3f2322ad>": {"offset": "0x1C570"}, "<lambda_be3e5d9dce35d2c8dbfa8485373731d5>::~<lambda_be3e5d9dce35d2c8dbfa8485373731d5>": {"offset": "0x1C600"}, "<lambda_bf7610c50ebb1884e5917f6a81016c78>::~<lambda_bf7610c50ebb1884e5917f6a81016c78>": {"offset": "0xA5B0"}, "<lambda_dddb32a458266b5f83be2f932cb33dba>::~<lambda_dddb32a458266b5f83be2f932cb33dba>": {"offset": "0x1C3A0"}, "<lambda_dfadb08385c0ecb100cd522a7df6a8ea>::~<lambda_dfadb08385c0ecb100cd522a7df6a8ea>": {"offset": "0x1C3A0"}, "<lambda_f25c37099038263181b5186a3fa41b37>::~<lambda_f25c37099038263181b5186a3fa41b37>": {"offset": "0x1C620"}, "<unnamed-type-g_eventSink>::SendPacket": {"offset": "0xFA70"}, "<unnamed-type-g_eventSink>::SendPacketV2": {"offset": "0xFAB0"}, "AddCrashometry": {"offset": "0x39F40"}, "CfxState::CfxState": {"offset": "0x39580"}, "Component::As": {"offset": "0xE7B0"}, "Component::IsA": {"offset": "0xE870"}, "Component::SetCommandLine": {"offset": "0xA490"}, "Component::SetUserData": {"offset": "0xE880"}, "ComponentInstance::DoGameLoad": {"offset": "0xE850"}, "ComponentInstance::Initialize": {"offset": "0xE860"}, "ComponentInstance::Shutdown": {"offset": "0xE880"}, "ConVar<bool>::ConVar<bool>": {"offset": "0x1A290"}, "Concurrency::_Ppltask_awaiter<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::await_suspend": {"offset": "0x301E0"}, "Concurrency::_Ppltask_awaiter<void>::await_suspend": {"offset": "0x30280"}, "Concurrency::cancellation_token::~cancellation_token": {"offset": "0x1DC00"}, "Concurrency::cancellation_token_source::cancel": {"offset": "0x30340"}, "Concurrency::cancellation_token_source::cancellation_token_source": {"offset": "0x1BC30"}, "Concurrency::cancellation_token_source::~cancellation_token_source": {"offset": "0x1DC40"}, "Concurrency::details::_CancellationTokenCallback<<lambda_be3e5d9dce35d2c8dbfa8485373731d5> >::_Exec": {"offset": "0x2DA40"}, "Concurrency::details::_CancellationTokenRegistration::_Invoke": {"offset": "0x2E770"}, "Concurrency::details::_CancellationTokenState::TokenRegistrationContainer::~TokenRegistrationContainer": {"offset": "0x1D630"}, "Concurrency::details::_CancellationTokenState::_DeregisterCallback": {"offset": "0x2CCA0"}, "Concurrency::details::_CancellationTokenState::_RegisterCallback": {"offset": "0x2EBA0"}, "Concurrency::details::_ContextCallback::~_ContextCallback": {"offset": "0x1D660"}, "Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore::_Callback": {"offset": "0x2AA20"}, "Concurrency::details::_DefaultPPLTaskScheduler::schedule": {"offset": "0x336F0"}, "Concurrency::details::_DefaultTaskHelper::_NoCallOnDefaultTask_ErrorImpl": {"offset": "0x2EAE0"}, "Concurrency::details::_ExceptionHolder::_RethrowUserException": {"offset": "0x2F070"}, "Concurrency::details::_ExceptionHolder::~_ExceptionHolder": {"offset": "0x1D680"}, "Concurrency::details::_Internal_task_options::~_Internal_task_options": {"offset": "0x1D840"}, "Concurrency::details::_RefCounter::_Destroy": {"offset": "0xFAD0"}, "Concurrency::details::_ScheduleFuncWithAutoInline": {"offset": "0x2F510"}, "Concurrency::details::_TaskCollectionBaseImpl::_Complete": {"offset": "0x2B230"}, "Concurrency::details::_TaskCollectionBaseImpl::_Wait": {"offset": "0x2FD90"}, "Concurrency::details::_TaskCollectionBaseImpl::~_TaskCollectionBaseImpl": {"offset": "0x1D8E0"}, "Concurrency::details::_TaskCreationCallstack::_TaskCreationCallstack": {"offset": "0x1B920"}, "Concurrency::details::_TaskCreationCallstack::~_TaskCreationCallstack": {"offset": "0x1D950"}, "Concurrency::details::_TaskProcHandle::_RunChoreBridge": {"offset": "0x2F110"}, "Concurrency::details::_TaskProcHandle::~_TaskProcHandle": {"offset": "0x1D9B0"}, "Concurrency::details::_TaskProcThunk::_Bridge": {"offset": "0x2A9C0"}, "Concurrency::details::_TaskProcThunk::_Holder::~_Holder": {"offset": "0x1D7F0"}, "Concurrency::details::_Task_impl<unsigned char>::_CancelAndRunContinuations": {"offset": "0x2AA60"}, "Concurrency::details::_Task_impl<unsigned char>::_FinalizeAndRunContinuations": {"offset": "0x2DEE0"}, "Concurrency::details::_Task_impl_base::_CancelWithException": {"offset": "0x2AF60"}, "Concurrency::details::_Task_impl_base::_HasUserException": {"offset": "0x2E760"}, "Concurrency::details::_Task_impl_base::_RegisterCancellation": {"offset": "0x2EC70"}, "Concurrency::details::_Task_impl_base::_RunTaskContinuations": {"offset": "0x2F150"}, "Concurrency::details::_Task_impl_base::_ScheduleContinuation": {"offset": "0x2F260"}, "Concurrency::details::_Task_impl_base::_ScheduleContinuationTask": {"offset": "0x2F460"}, "Concurrency::details::_Task_impl_base::_ScheduleTask": {"offset": "0x2F660"}, "Concurrency::details::_Task_impl_base::_Task_impl_base": {"offset": "0x1B9E0"}, "Concurrency::details::_Task_impl_base::_Wait": {"offset": "0x2FDF0"}, "Concurrency::details::_Task_impl_base::~_Task_impl_base": {"offset": "0x1D9C0"}, "Concurrency::details::_ThenImplOptions::_CreateOptions": {"offset": "0x2C450"}, "Concurrency::details::_ThenImplOptions::~_ThenImplOptions": {"offset": "0x1DAC0"}, "Concurrency::details::atomic_exchange<long>": {"offset": "0x171A0"}, "Concurrency::get_ambient_scheduler": {"offset": "0x322D0"}, "Concurrency::invalid_operation::invalid_operation": {"offset": "0x1BDA0"}, "Concurrency::invalid_operation::~invalid_operation": {"offset": "0xA6E0"}, "Concurrency::scheduler_ptr::scheduler_ptr": {"offset": "0x1C000"}, "Concurrency::scheduler_ptr::~scheduler_ptr": {"offset": "0x1CE20"}, "Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::then<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e> >": {"offset": "0x19BE0"}, "Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >::~task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >": {"offset": "0x1CE20"}, "Concurrency::task<unsigned char>::_CreateImpl": {"offset": "0x2C020"}, "Concurrency::task<unsigned char>::~task<unsigned char>": {"offset": "0x1CE20"}, "Concurrency::task<void>::_CreateImpl": {"offset": "0x2C3B0"}, "Concurrency::task<void>::task<void><Concurrency::task_completion_event<void> >": {"offset": "0x10470"}, "Concurrency::task<void>::then<<lambda_cdec71b40b220d4479babf37493ab33c> >": {"offset": "0x198B0"}, "Concurrency::task<void>::~task<void>": {"offset": "0x1CE20"}, "Concurrency::task_canceled::task_canceled": {"offset": "0x1C0D0"}, "Concurrency::task_canceled::~task_canceled": {"offset": "0xA6E0"}, "Concurrency::task_completion_event<unsigned char>::_RegisterTask": {"offset": "0x2EDD0"}, "Concurrency::task_completion_event<unsigned char>::set": {"offset": "0x33AD0"}, "Concurrency::task_completion_event<void>::~task_completion_event<void>": {"offset": "0x1CE20"}, "Concurrency::task_continuation_context::get_current_winrt_context": {"offset": "0x32390"}, "Concurrency::task_continuation_context::~task_continuation_context": {"offset": "0x1DD10"}, "Concurrency::task_options::task_options": {"offset": "0x1C190"}, "Concurrency::task_options::~task_options": {"offset": "0x1DD30"}, "ConsoleArgumentType<bool,void>::Parse": {"offset": "0x287C0"}, "ConsoleCommand::ConsoleCommand<<lambda_2729e31fb8c726b5ef153dd449ae69e5> >": {"offset": "0xFDF0"}, "ConsoleCommand::ConsoleCommand<<lambda_bf7610c50ebb1884e5917f6a81016c78> >": {"offset": "0xFFD0"}, "ConsoleCommand::~ConsoleCommand": {"offset": "0x1D4B0"}, "ConsoleFlagsToString": {"offset": "0x26410"}, "CoreGetComponentRegistry": {"offset": "0x26730"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x267C0"}, "CreateComponent": {"offset": "0xE890"}, "CreateVariableEntry<bool>": {"offset": "0x11080"}, "DllMain": {"offset": "0x4E830"}, "DoNtRaiseException": {"offset": "0x3B950"}, "DownloadResources": {"offset": "0x27F50"}, "DownloadResources$_InitCoro$2": {"offset": "0x26850"}, "DownloadResources$_ResumeCoro$1": {"offset": "0x269E0"}, "FatalErrorNoExceptRealV": {"offset": "0xBB60"}, "FatalErrorRealV": {"offset": "0xBB90"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x22C0"}, "GetAbsoluteCitPath": {"offset": "0x3A0C0"}, "GlobalErrorHandler": {"offset": "0xBDD0"}, "GlobalErrorRealV": {"offset": "0xC290"}, "HookFunctionBase::RunAll": {"offset": "0x3C770"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x390E0"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x39640"}, "HttpRequestOptions::~HttpRequestOptions": {"offset": "0x1D540"}, "IScriptRuntimeHandler::GetIID": {"offset": "0x28450"}, "InitFunction::Run": {"offset": "0xE8C0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x3B700"}, "InitFunctionBase::Register": {"offset": "0x3BAC0"}, "InitFunctionBase::RunAll": {"offset": "0x3BB10"}, "Instance<ICoreGameInit>::Get": {"offset": "0x28390"}, "Instance<fx::ResourceManager>::Get": {"offset": "0x283F0"}, "MakeRelativeCitPath": {"offset": "0xC6B0"}, "NetLibrary::SendNetPacket<net::packet::ClientServerCommandPacket>": {"offset": "0x13B00"}, "NetLibrary::SendNetPacket<net::packet::ClientServerEventPacket>": {"offset": "0x13E50"}, "NetLibrary::SendNetPacket<net::packet::ReassembledEventPacket>": {"offset": "0x141B0"}, "NetLibrary::SendNetPacket<net::packet::ReassembledEventV2Packet>": {"offset": "0x144C0"}, "NetLibraryResourcesComponent::AttachToObject": {"offset": "0xE8D0"}, "NetLibraryResourcesComponent::UpdateOneResource": {"offset": "0x29490"}, "NetLibraryResourcesComponent::UpdateResources": {"offset": "0x29660"}, "RaiseDebugException": {"offset": "0x3BA30"}, "ScopedError::~ScopedError": {"offset": "0xA680"}, "SysError": {"offset": "0xCC30"}, "ThrottledConnectionProgress": {"offset": "0x290D0"}, "ToNarrow": {"offset": "0x3BB40"}, "ToWide": {"offset": "0x3BC30"}, "TraceReal<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x14800"}, "TraceRealV": {"offset": "0x3BF40"}, "UrlEncodeWrap": {"offset": "0x2A570"}, "Win32TrapAndJump64": {"offset": "0x3C810"}, "_DllMainCRTStartup": {"offset": "0x4E170"}, "_Init_thread_abort": {"offset": "0x4D47C"}, "_Init_thread_footer": {"offset": "0x4D4AC"}, "_Init_thread_header": {"offset": "0x4D50C"}, "_Init_thread_notify": {"offset": "0x4D574"}, "_Init_thread_wait": {"offset": "0x4D5B8"}, "_RTC_Initialize": {"offset": "0x4E880"}, "_RTC_Terminate": {"offset": "0x4E8BC"}, "_Smtx_lock_exclusive": {"offset": "0x4D258"}, "_Smtx_lock_shared": {"offset": "0x4D260"}, "_Smtx_unlock_exclusive": {"offset": "0x4D268"}, "_Smtx_unlock_shared": {"offset": "0x4D270"}, "__ArrayUnwind": {"offset": "0x4DD84"}, "__GSHandlerCheck": {"offset": "0x4DB98"}, "__GSHandlerCheckCommon": {"offset": "0x4DBB8"}, "__GSHandlerCheck_EH": {"offset": "0x4DC14"}, "__GSHandlerCheck_SEH": {"offset": "0x4E32C"}, "__chkstk": {"offset": "0x4DE00"}, "__crt_debugger_hook": {"offset": "0x4E5FC"}, "__dyn_tls_init": {"offset": "0x4D9E0"}, "__dyn_tls_on_demand_init": {"offset": "0x4DA48"}, "__isa_available_init": {"offset": "0x4E3C4"}, "__local_stdio_printf_options": {"offset": "0xE690"}, "__local_stdio_scanf_options": {"offset": "0x4E854"}, "__raise_securityfailure": {"offset": "0x4E1B0"}, "__report_gsfailure": {"offset": "0x4E1E4"}, "__scrt_acquire_startup_lock": {"offset": "0x4D660"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x4D69C"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x4D6D0"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x4D6E8"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x4D710"}, "__scrt_dllmain_exception_filter": {"offset": "0x4D728"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x4D788"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x4D7B8"}, "__scrt_fastfail": {"offset": "0x4E604"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x4E878"}, "__scrt_initialize_crt": {"offset": "0x4D7CC"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x4E85C"}, "__scrt_initialize_onexit_tables": {"offset": "0x4D818"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x4D384"}, "__scrt_initialize_type_info": {"offset": "0x4DCFC"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x4D8A4"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x51D4C"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x4E778"}, "__scrt_release_startup_lock": {"offset": "0x4D93C"}, "__scrt_stub_for_acrt_initialize": {"offset": "0xE880"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0xE880"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0xE880"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0xE880"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0xE880"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0xE7B0"}, "__scrt_throw_std_bad_alloc": {"offset": "0x4E750"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0xD430"}, "__scrt_uninitialize_crt": {"offset": "0x4D960"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x4D454"}, "__scrt_uninitialize_type_info": {"offset": "0x4DD0C"}, "__security_check_cookie": {"offset": "0x4DCB0"}, "__security_init_cookie": {"offset": "0x4E784"}, "__std_find_trivial_1": {"offset": "0x4D130"}, "__std_init_once_link_alternate_names_and_abort": {"offset": "0x4D2A0"}, "_get_startup_argv_mode": {"offset": "0x4E770"}, "_guard_check_icall_nop": {"offset": "0xA490"}, "_guard_dispatch_icall_nop": {"offset": "0x4EA20"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x4EA40"}, "_onexit": {"offset": "0x4D98C"}, "_wwassert": {"offset": "0x3A440"}, "`DownloadResources'::`22'::<parameters>::~<parameters>": {"offset": "0x1C670"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x51D90"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x51DEF"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x51E06"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x51E1F"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x51E33"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x1210"}, "`dynamic initializer for '_init_instance_24''": {"offset": "0x1240"}, "`dynamic initializer for '_init_instance_25''": {"offset": "0x1270"}, "`dynamic initializer for '_init_instance_26''": {"offset": "0x12A0"}, "`dynamic initializer for '_init_instance_27''": {"offset": "0x12D0"}, "`dynamic initializer for '_init_instance_30''": {"offset": "0x1300"}, "`dynamic initializer for '_init_instance_31''": {"offset": "0x1330"}, "`dynamic initializer for '_init_instance_32''": {"offset": "0x1360"}, "`dynamic initializer for '_init_instance_33''": {"offset": "0x1390"}, "`dynamic initializer for '_init_instance_34''": {"offset": "0x13C0"}, "`dynamic initializer for '_init_instance_35''": {"offset": "0x13F0"}, "`dynamic initializer for '_init_instance_36''": {"offset": "0x1420"}, "`dynamic initializer for '_init_instance_37''": {"offset": "0x1450"}, "`dynamic initializer for '_init_instance_38''": {"offset": "0x1480"}, "`dynamic initializer for '_init_instance_39''": {"offset": "0x14B0"}, "`dynamic initializer for 'cts''": {"offset": "0x14E0"}, "`dynamic initializer for 'executeNextGameFrame''": {"offset": "0x1560"}, "`dynamic initializer for 'g_resourceStartRequestMutex''": {"offset": "0x1870"}, "`dynamic initializer for 'g_resourceStartRequestSet''": {"offset": "0x18A0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x1030"}, "`dynamic initializer for 'initFunction''": {"offset": "0x18E0"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x1960"}, "`dynamic initializer for 'nextProgress''": {"offset": "0x1920"}, "`dynamic initializer for 'progressMutex''": {"offset": "0x1930"}, "`dynamic initializer for 'tbb::detail::r1::concurrent_monitor_mutex::my_init_mutex''": {"offset": "0x1AF0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>,<lambda_22efe08b9cf28c27189cd77a7cbf0d03> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D710"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>,<lambda_22efe08b9cf28c27189cd77a7cbf0d03> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D710"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>,<lambda_72ae5b0e2f813e08b01a5e2876e67561> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>,<lambda_814d5e0b3a45fa1dd158672480f6c4ca> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>,<lambda_93f9a070d138a1ef45eae8a2debfa335> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D770"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>,<lambda_93f9a070d138a1ef45eae8a2debfa335> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D770"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>,<lambda_a9ea5f9372bb612812c52de723ed7985> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D790"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>,<lambda_a9ea5f9372bb612812c52de723ed7985> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D790"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>,<lambda_bb63d0011cba966169dbe6af3f2322ad> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D7B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>,<lambda_bb63d0011cba966169dbe6af3f2322ad> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D7B0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D7D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>,<lambda_bf7610c50ebb1884e5917f6a81016c78> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D7D0"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >,<lambda_dddb32a458266b5f83be2f932cb33dba> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >,<lambda_dddb32a458266b5f83be2f932cb33dba> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >,<lambda_dfadb08385c0ecb100cd522a7df6a8ea> >'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "`std::_Global_new<std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >,<lambda_dfadb08385c0ecb100cd522a7df6a8ea> const &>'::`2'::_Guard_type::~_Guard_type": {"offset": "0x1D730"}, "atexit": {"offset": "0x4D9C8"}, "boost::algorithm::detail::find_format_all_impl2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_equal>,boost::algorithm::detail::const_formatF<boost::iterator_range<char const *> >,boost::iterator_range<std::_String_iterator<std::_String_val<std::_Simple_types<char> > > >,boost::iterator_range<char const *> >": {"offset": "0x18360"}, "boost::algorithm::detail::find_format_all_impl2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_equal>,boost::algorithm::detail::const_formatF<boost::iterator_range<std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > > >,boost::iterator_range<std::_String_iterator<std::_String_val<std::_Simple_types<char> > > >,boost::iterator_range<std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > > >": {"offset": "0x187F0"}, "boost::algorithm::find_format_all<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_equal>,boost::algorithm::detail::const_formatF<boost::iterator_range<std::_String_const_iterator<std::_String_val<std::_Simple_types<char> > > > > >": {"offset": "0x18260"}, "boost::algorithm::first_finder<char [11]>": {"offset": "0x18C80"}, "boost::algorithm::replace_all<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char [12],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x191D0"}, "capture_previous_context": {"offset": "0x4E2B8"}, "console::PrintWarning<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x13220"}, "console::Printfv": {"offset": "0x288F0"}, "debug::Alias": {"offset": "0xA490"}, "dllmain_crt_dispatch": {"offset": "0x4DE50"}, "dllmain_crt_process_attach": {"offset": "0x4DEA0"}, "dllmain_crt_process_detach": {"offset": "0x4DFB8"}, "dllmain_dispatch": {"offset": "0x4E03C"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0xDA70"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0xA550"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x38900"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x37F40"}, "fmt::v8::detail::add_compare": {"offset": "0x38110"}, "fmt::v8::detail::assert_fail": {"offset": "0x38250"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x382A0"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x38470"}, "fmt::v8::detail::bigint::square": {"offset": "0x38CD0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x37F40"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x2D60"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x38F90"}, "fmt::v8::detail::compare": {"offset": "0x383D0"}, "fmt::v8::detail::count_digits": {"offset": "0xD850"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x34A20"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x2E10"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x387D0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x368F0"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x38AB0"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x36920"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x375E0"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x371B0"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x38A30"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x34B30"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x34B30"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x38760"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x2E40"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x35FE0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x3110"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x35EC0"}, "fmt::v8::detail::format_float<double>": {"offset": "0x34570"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x36120"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x31F0"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x36480"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x3310"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x3470"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x36D0"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0xE5E0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x36B30"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x36DB0"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x37040"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0xA5B0"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x37A0"}, "fmt::v8::detail::utf8_decode": {"offset": "0xE420"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x4DA0"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0x5DA0"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0x5950"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0x61E0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x37C40"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x37B20"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0x5880"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0x6620"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0x6660"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0x67F0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x71B0"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0x6BC0"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0xA140"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0x78E0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0x7D00"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0x7ED0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0x8070"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0x8070"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0x8200"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0x8420"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0x85A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x9D30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0x8730"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0x8950"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0x8BF0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0x8E10"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0x8F90"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0x91B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0x93D0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0x9550"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0x9770"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0x98F0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0x9B10"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9EC0"}, "fmt::v8::format_error::format_error": {"offset": "0xA340"}, "fmt::v8::format_error::~format_error": {"offset": "0xA6E0"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x3AD30"}, "fmt::v8::sprintf<char [7],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char>": {"offset": "0x19480"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x39F0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x38C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x37D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3C10"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3AE0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x3D40"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x48A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x42D0"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x1A0C0"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x3B5D0"}, "fprintf": {"offset": "0xE6A0"}, "fwEvent<>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<>::callback::~callback": {"offset": "0x1DB60"}, "fwEvent<NetAddress>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<NetLibrary *>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<char const *>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<fx::ResourceManager *>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::ConnectInternal": {"offset": "0x26240"}, "fwEvent<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::ConnectInternal": {"offset": "0x26240"}, "fwPlatformString::~fwPlatformString": {"offset": "0xA700"}, "fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >::~fwRefContainer<InstanceRegistryBase<fwRefContainer<fwRefCountable> > >": {"offset": "0x1CDB0"}, "fwRefContainer<NetLibraryResourcesComponent>::~fwRefContainer<NetLibraryResourcesComponent>": {"offset": "0x1CDB0"}, "fwRefContainer<fwRefCountable>::~fwRefContainer<fwRefCountable>": {"offset": "0x1CDB0"}, "fwRefContainer<fx::CachedResourceMounter>::fwRefContainer<fx::CachedResourceMounter><fx::ResourceMounter>": {"offset": "0x106B0"}, "fwRefContainer<fx::CachedResourceMounter>::~fwRefContainer<fx::CachedResourceMounter>": {"offset": "0x1CDB0"}, "fwRefContainer<fx::CachedResourceMounterWrap>::~fwRefContainer<fx::CachedResourceMounterWrap>": {"offset": "0x1CDB0"}, "fwRefContainer<fx::EventReassemblyComponent>::~fwRefContainer<fx::EventReassemblyComponent>": {"offset": "0x1CDB0"}, "fwRefContainer<fx::Resource>::~fwRefContainer<fx::Resource>": {"offset": "0x1CDB0"}, "fwRefContainer<fx::ResourceMounter>::~fwRefContainer<fx::ResourceMounter>": {"offset": "0x1CDB0"}, "fwRefCountable::AddRef": {"offset": "0x3C730"}, "fwRefCountable::Release": {"offset": "0x3C740"}, "fwRefCountable::~fwRefCountable": {"offset": "0x3C720"}, "fx::EnableEventReassemblyChanged": {"offset": "0x27FD0"}, "fx::OMPtr<IScriptRuntime>::~OMPtr<IScriptRuntime>": {"offset": "0x1C680"}, "fx::ReassembledEventPacketHandler::Process<net::ByteReader>": {"offset": "0x13470"}, "fx::ReassembledEventPacketV2Handler::Process<net::ByteReader>": {"offset": "0x13630"}, "fx::StreamingEntryData::~StreamingEntryData": {"offset": "0x1CEA0"}, "fx::TriggerLatentServerEventInternal": {"offset": "0x29230"}, "fx::`dynamic initializer for 'g_enableEventReassembly''": {"offset": "0x15D0"}, "fxCreateObjectInstance": {"offset": "0x3C7A0"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::Call": {"offset": "0x25550"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(bool const &)> >::CallInternal<0,0,std::tuple<> >": {"offset": "0x10D30"}, "internal::ConsoleCommandFunction<std::function<void __cdecl(void)> >::Call": {"offset": "0x25960"}, "internal::ConsoleVariableEntry<bool>::ConsoleVariableEntry<bool>": {"offset": "0x1A420"}, "internal::ConsoleVariableEntry<bool>::GetOfflineValue": {"offset": "0xF3F0"}, "internal::ConsoleVariableEntry<bool>::GetValue": {"offset": "0xF270"}, "internal::ConsoleVariableEntry<bool>::SaveOfflineValue": {"offset": "0xF450"}, "internal::ConsoleVariableEntry<bool>::SetRawValue": {"offset": "0x28BD0"}, "internal::ConsoleVariableEntry<bool>::SetValue": {"offset": "0xF2F0"}, "internal::ConsoleVariableEntry<bool>::UpdateTrackingVariable": {"offset": "0xF460"}, "internal::Constraints<bool,void>::Compare": {"offset": "0x25DA0"}, "launch::IsSDKGuest": {"offset": "0x3A3C0"}, "net::PacketHandler<net::packet::ServerClientEvent,1933049210>::ProcessPacket<net::ByteReader,<lambda_b878cc653313d5017b1f299ecf96c164> >": {"offset": "0x13830"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[29],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[5],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x171B0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[39],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x172C0"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,char const (&)[52],char const *>": {"offset": "0x17380"}, "nlohmann::json_abi_v3_11_2::detail::concat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x17450"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::format_buffer": {"offset": "0x32100"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2": {"offset": "0x323F0"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2<double>": {"offset": "0x18CA0"}, "nlohmann::json_abi_v3_11_2::detail::dtoa_impl::grisu2_digit_gen": {"offset": "0x32640"}, "nlohmann::json_abi_v3_11_2::detail::exception::exception": {"offset": "0x1BCC0"}, "nlohmann::json_abi_v3_11_2::detail::exception::name": {"offset": "0x32F30"}, "nlohmann::json_abi_v3_11_2::detail::exception::what": {"offset": "0x344A0"}, "nlohmann::json_abi_v3_11_2::detail::other_error::create<std::nullptr_t,0>": {"offset": "0x17520"}, "nlohmann::json_abi_v3_11_2::detail::other_error::other_error": {"offset": "0x1BF80"}, "nlohmann::json_abi_v3_11_2::detail::other_error::~other_error": {"offset": "0x1DC80"}, "nlohmann::json_abi_v3_11_2::detail::output_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~output_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CE20"}, "nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::write_character": {"offset": "0x344C0"}, "nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::write_characters": {"offset": "0x34500"}, "nlohmann::json_abi_v3_11_2::detail::type_error::create<std::nullptr_t,0>": {"offset": "0x17760"}, "nlohmann::json_abi_v3_11_2::detail::type_error::type_error": {"offset": "0x1C220"}, "nlohmann::json_abi_v3_11_2::detail::type_error::~type_error": {"offset": "0x1DC80"}, "ranges::_get_::get<0,ranges::variant<char32_t *,std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > > &>": {"offset": "0x44F20"}, "ranges::_get_::get<1,ranges::variant<char32_t *,std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > > &>": {"offset": "0x44DC0"}, "ranges::bad_variant_access::bad_variant_access": {"offset": "0x45C00"}, "ranges::bad_variant_access::~bad_variant_access": {"offset": "0xA6E0"}, "ranges::detail::move<ranges::detail::get_fn<char32_t * const,0> &>": {"offset": "0x191C0"}, "ranges::detail::move<ranges::detail::get_fn<char32_t *,0> &>": {"offset": "0x191C0"}, "ranges::detail::move<ranges::detail::get_fn<ranges::default_sentinel_t const ,1> &>": {"offset": "0x191C0"}, "ranges::detail::move<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > const ,1> &>": {"offset": "0x191C0"}, "ranges::detail::move<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,1> &>": {"offset": "0x191C0"}, "ranges::detail::move<ranges::detail::indexed_element_fn &>": {"offset": "0x191C0"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<char32_t * const,0>,ranges::detail::indexed_element_fn>": {"offset": "0x45B90"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<char32_t *,0>,ranges::detail::indexed_element_fn>": {"offset": "0x45B90"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<ranges::default_sentinel_t const ,1>,ranges::detail::indexed_element_fn>": {"offset": "0x45B90"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > const ,1>,ranges::detail::indexed_element_fn>": {"offset": "0x45B90"}, "ranges::detail::variant_visit_<ranges::detail::get_fn<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,1>,ranges::detail::indexed_element_fn>": {"offset": "0x45B90"}, "ranges::detail::variant_visit_<ranges::detail::variant_data_<meta::list<ranges::detail::indexed_datum<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,std::integral_constant<unsigned __int64,1> > >,1>::type const ,ranges::detail::get_fn<char32_t * const,0>,ranges::detail::indexed_element_fn>": {"offset": "0x45B10"}, "ranges::detail::variant_visit_<ranges::detail::variant_data_<meta::list<ranges::detail::indexed_datum<std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > >,std::integral_constant<unsigned __int64,1> > >,1>::type,ranges::detail::get_fn<char32_t *,0>,ranges::detail::indexed_element_fn>": {"offset": "0x45B10"}, "ranges::transform_view<ranges::split_view<ranges::ref_view<std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >,ranges::single_view<char32_t> >,`skyr::v1::`anonymous namespace'::domain_to_ascii'::`2'::<lambda_1> >::~transform_view<ranges::split_view<ranges::ref_view<std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >,ranges::single_view<char32_t> >,`skyr::v1::`anonymous namespace'::domain_to_ascii'::`2'::<lambda_1> >": {"offset": "0x45CA0"}, "ranges::transform_view<ranges::split_when_view<std::basic_string_view<char,std::char_traits<char> >,ranges::views::split_when_base_fn::predicate_pred_<`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_1> > >,`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_2> >::~transform_view<ranges::split_when_view<std::basic_string_view<char,std::char_traits<char> >,ranges::views::split_when_base_fn::predicate_pred_<`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_1> > >,`skyr::v1::url_search_parameters::initialize'::`2'::<lambda_2> >": {"offset": "0x43060"}, "rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>::Peek": {"offset": "0x288B0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1D470"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndArray": {"offset": "0x280C0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::EndObject": {"offset": "0x281D0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::ParseStream<0,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x12710"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::String": {"offset": "0x28F60"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0xA3C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ClearStackOnExit::~ClearStackOnExit": {"offset": "0x1D4A0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::NumberStream<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,0,0>::Peek": {"offset": "0x288D0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseArray<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x11570"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseHex4<rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream> >": {"offset": "0x117C0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseNumber<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x11890"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseObject<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x12290"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseStringToStream<0,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char> >": {"offset": "0x12920"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::ParseValue<0,rapidjson::EncodedInputStream<rapidjson::UTF8<char>,rapidjson::MemoryStream>,rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator> >": {"offset": "0x12DB0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::StackStream<char>::Put": {"offset": "0x28AB0"}, "rapidjson::GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericReader<rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA460"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0xA460"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x1CD0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0xB530"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Begin": {"offset": "0x25510"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::FindMember<rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x11360"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetString": {"offset": "0x28570"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::GetUint": {"offset": "0x285D0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::HasMember": {"offset": "0x28610"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::MemberBegin": {"offset": "0x28700"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::MemberEnd": {"offset": "0x28760"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0xA490"}, "rapidjson::GetParseError_En": {"offset": "0x28470"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0xC8C0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0xC980"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0xCBF0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0xD090"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0xA4A0"}, "rapidjson::internal::DigitGen": {"offset": "0xB7E0"}, "rapidjson::internal::FastPath": {"offset": "0x282E0"}, "rapidjson::internal::Grisu2": {"offset": "0xC4D0"}, "rapidjson::internal::Prettify": {"offset": "0xCA30"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> > >": {"offset": "0x139E0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x2760"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x2830"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0xA460"}, "rapidjson::internal::WriteExponent": {"offset": "0xD000"}, "rapidjson::internal::u32toa": {"offset": "0xDB60"}, "rapidjson::internal::u64toa": {"offset": "0xDDD0"}, "se::Object::~Object": {"offset": "0xA5B0"}, "skyr::v1::`anonymous namespace'::domain_to_ascii": {"offset": "0x46080"}, "skyr::v1::`anonymous namespace'::is_double_dot_path_segment": {"offset": "0x48470"}, "skyr::v1::`anonymous namespace'::is_url_code_point": {"offset": "0x48770"}, "skyr::v1::`anonymous namespace'::is_windows_drive_letter": {"offset": "0x487E0"}, "skyr::v1::`anonymous namespace'::map_code_points<skyr::v1::unicode::transform_u32_range<skyr::v1::unicode::view_u8_range<std::basic_string_view<char,std::char_traits<char> > > > &>": {"offset": "0x45000"}, "skyr::v1::`anonymous namespace'::parse_next": {"offset": "0x47A40"}, "skyr::v1::`anonymous namespace'::parse_opaque_host": {"offset": "0x4CD10"}, "skyr::v1::`anonymous namespace'::shorten_path": {"offset": "0x4C470"}, "skyr::v1::`anonymous namespace'::validate_label": {"offset": "0x47300"}, "skyr::v1::default_port": {"offset": "0x48360"}, "skyr::v1::details::basic_parse": {"offset": "0x474C0"}, "skyr::v1::details::make_url": {"offset": "0x423E0"}, "skyr::v1::details::to_u8<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19F10"}, "skyr::v1::domain::~domain": {"offset": "0xA5B0"}, "skyr::v1::domain_to_ascii": {"offset": "0x46CA0"}, "skyr::v1::host::host": {"offset": "0x48000"}, "skyr::v1::host::serialize": {"offset": "0x33A20"}, "skyr::v1::host::~host": {"offset": "0x42170"}, "skyr::v1::idna::code_point_status": {"offset": "0x47C70"}, "skyr::v1::idna::idna_code_point_map_iterator<skyr::v1::unicode::u8_range_iterator<std::_String_view_iterator<std::char_traits<char> > >,skyr::v1::unicode::sentinel>::increment": {"offset": "0x46DB0"}, "skyr::v1::idna::map_code_point": {"offset": "0x47CE0"}, "skyr::v1::ipv4_address::serialize": {"offset": "0x3F270"}, "skyr::v1::ipv6_address::serialize": {"offset": "0x40A80"}, "skyr::v1::is_special": {"offset": "0x48660"}, "skyr::v1::make_url<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x190F0"}, "skyr::v1::opaque_host::~opaque_host": {"offset": "0xA5B0"}, "skyr::v1::parse_host": {"offset": "0x4C5D0"}, "skyr::v1::parse_ipv4_address": {"offset": "0x3EC70"}, "skyr::v1::parse_ipv6_address": {"offset": "0x40520"}, "skyr::v1::percent_decode": {"offset": "0x43BB0"}, "skyr::v1::percent_encoding::is_percent_encoded": {"offset": "0x485A0"}, "skyr::v1::percent_encoding::percent_encoded_char::~percent_encoded_char": {"offset": "0xA5B0"}, "skyr::v1::punycode_decode<char32_t,std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > >": {"offset": "0x454A0"}, "skyr::v1::punycode_encode<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x45770"}, "skyr::v1::serialize_excluding_fragment": {"offset": "0x424B0"}, "skyr::v1::unicode::details::from_four_byte_sequence<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x44BA0"}, "skyr::v1::unicode::find_code_point<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x44940"}, "skyr::v1::url::host": {"offset": "0x32900"}, "skyr::v1::url::update_record": {"offset": "0x429D0"}, "skyr::v1::url::url": {"offset": "0x41EE0"}, "skyr::v1::url::~url": {"offset": "0x1DE20"}, "skyr::v1::url_parser_context::parse_authority": {"offset": "0x48880"}, "skyr::v1::url_parser_context::parse_cannot_be_a_base_url": {"offset": "0x48E80"}, "skyr::v1::url_parser_context::parse_file": {"offset": "0x49260"}, "skyr::v1::url_parser_context::parse_file_host": {"offset": "0x49630"}, "skyr::v1::url_parser_context::parse_file_slash": {"offset": "0x49A90"}, "skyr::v1::url_parser_context::parse_fragment": {"offset": "0x49C10"}, "skyr::v1::url_parser_context::parse_hostname": {"offset": "0x49F30"}, "skyr::v1::url_parser_context::parse_no_scheme": {"offset": "0x4A310"}, "skyr::v1::url_parser_context::parse_path": {"offset": "0x4A4D0"}, "skyr::v1::url_parser_context::parse_path_or_authority": {"offset": "0x4AD80"}, "skyr::v1::url_parser_context::parse_path_start": {"offset": "0x4ADC0"}, "skyr::v1::url_parser_context::parse_port": {"offset": "0x4AFE0"}, "skyr::v1::url_parser_context::parse_query": {"offset": "0x4B290"}, "skyr::v1::url_parser_context::parse_relative": {"offset": "0x4B5A0"}, "skyr::v1::url_parser_context::parse_relative_slash": {"offset": "0x4BA10"}, "skyr::v1::url_parser_context::parse_scheme": {"offset": "0x4BB30"}, "skyr::v1::url_parser_context::parse_scheme_start": {"offset": "0x4C030"}, "skyr::v1::url_parser_context::parse_special_authority_ignore_slashes": {"offset": "0x4C160"}, "skyr::v1::url_parser_context::parse_special_authority_slashes": {"offset": "0x4C1A0"}, "skyr::v1::url_parser_context::parse_special_relative_or_authority": {"offset": "0x4C280"}, "skyr::v1::url_parser_context::url_parser_context": {"offset": "0x48090"}, "skyr::v1::url_parser_context::~url_parser_context": {"offset": "0x47440"}, "skyr::v1::url_record::is_special": {"offset": "0x48620"}, "skyr::v1::url_record::url_record": {"offset": "0x41FC0"}, "skyr::v1::url_record::~url_record": {"offset": "0x1DEB0"}, "skyr::v1::url_search_parameters::initialize": {"offset": "0x432F0"}, "skyr::v1::url_search_parameters::url_search_parameters": {"offset": "0x42FE0"}, "snprintf": {"offset": "0x34510"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >,void *> > >": {"offset": "0x1C6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA4D0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x1C6C0"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0xA4D0"}, "std::_Buffered_inplace_merge_divide_and_conquer2<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x3F740"}, "std::_Buffered_inplace_merge_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x3F830"}, "std::_Buffered_inplace_merge_unchecked_impl<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x3F970"}, "std::_Buffered_merge_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x3FD30"}, "std::_Buffered_rotate_unchecked<std::pair<unsigned __int64,unsigned __int64> *>": {"offset": "0x3FF80"}, "std::_Chunked_merge_unchecked<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x401B0"}, "std::_Destroy_in_place<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xA5B0"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x14DC0"}, "std::_Destroy_range<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x42D10"}, "std::_Destroy_range<std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<unsigned char> > > >": {"offset": "0x14E50"}, "std::_Destroy_range<std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x14ED0"}, "std::_Facet_Register": {"offset": "0x4D32C"}, "std::_Func_class<bool,ConsoleExecutionContext &>::~_Func_class<bool,ConsoleExecutionContext &>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,NetAddress>::~_Func_class<bool,NetAddress>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,NetLibrary *>::~_Func_class<bool,NetLibrary *>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,char const *>::~_Func_class<bool,char const *>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,fx::ResourceManager *>::~_Func_class<bool,fx::ResourceManager *>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::~_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>": {"offset": "0x1C3A0"}, "std::_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::~_Func_class<bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>": {"offset": "0x1C3A0"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x1C3A0"}, "std::_Func_class<unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::~_Func_class<unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >": {"offset": "0x1C3A0"}, "std::_Func_class<unsigned char,Concurrency::task<void> >::~_Func_class<unsigned char,Concurrency::task<void> >": {"offset": "0x1C3A0"}, "std::_Func_class<void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::~_Func_class<void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >": {"offset": "0x1C3A0"}, "std::_Func_class<void,Concurrency::task<void> >::~_Func_class<void,Concurrency::task<void> >": {"offset": "0x1C3A0"}, "std::_Func_class<void,ProgressInfo const &>::_Reset_move": {"offset": "0x2F000"}, "std::_Func_class<void,bool const &>::~_Func_class<void,bool const &>": {"offset": "0x1C3A0"}, "std::_Func_class<void,bool,char const *,unsigned __int64>::~_Func_class<void,bool,char const *,unsigned __int64>": {"offset": "0x1C3A0"}, "std::_Func_class<void,char const *,unsigned __int64>::~_Func_class<void,char const *,unsigned __int64>": {"offset": "0x1C3A0"}, "std::_Func_class<void>::_Reset_move": {"offset": "0x2F000"}, "std::_Func_class<void>::~_Func_class<void>": {"offset": "0x1C3A0"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Copy": {"offset": "0x2B860"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Do_call": {"offset": "0x2D030"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Move": {"offset": "0x2B860"}, "std::_Func_impl_no_alloc<<lambda_0d613e42a8c7ff4bb02646a0e9a5aff9>,void,ProgressInfo const &>::_Target_type": {"offset": "0x2F6E0"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Copy": {"offset": "0x2B870"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Do_call": {"offset": "0x2D040"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Move": {"offset": "0x2B870"}, "std::_Func_impl_no_alloc<<lambda_179acf0219430079cb4930948cf2e4eb>,bool>::_Target_type": {"offset": "0x2F6F0"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Copy": {"offset": "0x2B890"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Delete_this": {"offset": "0x2CA20"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Do_call": {"offset": "0x2D050"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Move": {"offset": "0x2E9F0"}, "std::_Func_impl_no_alloc<<lambda_196fffd2c22ffbf1c7b1d04bd7dd6594>,void>::_Target_type": {"offset": "0x2F700"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Copy": {"offset": "0x2B8D0"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x2D1B0"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Move": {"offset": "0x2B8D0"}, "std::_Func_impl_no_alloc<<lambda_1f11cdf8c101bc77e5c2178a300185a7>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x2F710"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Copy": {"offset": "0x2B8E0"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Delete_this": {"offset": "0x2CA60"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D1C0"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_22efe08b9cf28c27189cd77a7cbf0d03>,void,bool,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F720"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Copy": {"offset": "0xF860"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Do_call": {"offset": "0xF880"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Move": {"offset": "0xF860"}, "std::_Func_impl_no_alloc<<lambda_2729e31fb8c726b5ef153dd449ae69e5>,void,bool const &>::_Target_type": {"offset": "0xF960"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Copy": {"offset": "0x2B990"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Do_call": {"offset": "0x2D1E0"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Move": {"offset": "0x2B990"}, "std::_Func_impl_no_alloc<<lambda_2fa3e3d11fb97352afa77a4a13bfb543>,void>::_Target_type": {"offset": "0x2F730"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Copy": {"offset": "0x2B9B0"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Do_call": {"offset": "0x2D1F0"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Move": {"offset": "0x2B9B0"}, "std::_Func_impl_no_alloc<<lambda_335ad4db0ee159c8d9e0e7ff6fe3e728>,bool,NetAddress>::_Target_type": {"offset": "0x2F740"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Copy": {"offset": "0x2B9D0"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Do_call": {"offset": "0x2D220"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Move": {"offset": "0x2B9D0"}, "std::_Func_impl_no_alloc<<lambda_38e7a4dba05480968f14417b3798ac43>,void>::_Target_type": {"offset": "0x2F750"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Copy": {"offset": "0x2B9F0"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Do_call": {"offset": "0x2D230"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Move": {"offset": "0x2B9F0"}, "std::_Func_impl_no_alloc<<lambda_3a37de25294b3d036207234ce313b878>,void,fwRefContainer<fx::Resource> const &>::_Target_type": {"offset": "0x2F760"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Copy": {"offset": "0x2BA00"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Do_call": {"offset": "0x2D2F0"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Move": {"offset": "0x2BA00"}, "std::_Func_impl_no_alloc<<lambda_4749571fa4dd7e70c811ea8a5c47faca>,bool>::_Target_type": {"offset": "0x2F770"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Copy": {"offset": "0x2BA20"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x2D310"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Move": {"offset": "0x2BA20"}, "std::_Func_impl_no_alloc<<lambda_4a7dfda14872507c0a68962f508a1f49>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x2F780"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Copy": {"offset": "0x2BA40"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Do_call": {"offset": "0x2D320"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Move": {"offset": "0x2BA40"}, "std::_Func_impl_no_alloc<<lambda_535be59fd0441ec5bc2a631fcb52fc8e>,bool>::_Target_type": {"offset": "0x2F790"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Copy": {"offset": "0x2BA60"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Do_call": {"offset": "0x2D340"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Move": {"offset": "0x2BA60"}, "std::_Func_impl_no_alloc<<lambda_5c0a15cde5f0849ad52cef3892e75c3e>,void>::_Target_type": {"offset": "0x2F7A0"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Copy": {"offset": "0x2BA70"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D3A0"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Move": {"offset": "0x2BA70"}, "std::_Func_impl_no_alloc<<lambda_61aed51429ff120d658c470b73ba5923>,void,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F7B0"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Copy": {"offset": "0x2BA80"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Do_call": {"offset": "0x2D3E0"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Move": {"offset": "0x2BA80"}, "std::_Func_impl_no_alloc<<lambda_64ec6c4912b532fea5ebdf86fd2a1493>,void>::_Target_type": {"offset": "0x2F7C0"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Copy": {"offset": "0x2BA90"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Do_call": {"offset": "0xA490"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Move": {"offset": "0x2BA90"}, "std::_Func_impl_no_alloc<<lambda_65d01f85a5e3e73a3c2523a7d0261de5>,void>::_Target_type": {"offset": "0x2F7D0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xF680"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xF620"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xF700"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_72ae5b0e2f813e08b01a5e2876e67561>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xF750"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Copy": {"offset": "0x2BAA0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Do_call": {"offset": "0x2D430"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Move": {"offset": "0x2BAA0"}, "std::_Func_impl_no_alloc<<lambda_763529b0c7473cbc215a52d189ac9b18>,void>::_Target_type": {"offset": "0x2F7E0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Copy": {"offset": "0x2BAC0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Do_call": {"offset": "0x2D3E0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Move": {"offset": "0x2BAC0"}, "std::_Func_impl_no_alloc<<lambda_77821ff4e20c1b7a8e3e8bc0ba023bfc>,void>::_Target_type": {"offset": "0x2F7F0"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Copy": {"offset": "0x2BAD0"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Do_call": {"offset": "0x2D440"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Move": {"offset": "0x2BAD0"}, "std::_Func_impl_no_alloc<<lambda_7cff303255de45501809d9568d3d6029>,bool,fx::ResourceManager *>::_Target_type": {"offset": "0x2F800"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Copy": {"offset": "0x2BAF0"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Do_call": {"offset": "0x2D430"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Move": {"offset": "0x2BAF0"}, "std::_Func_impl_no_alloc<<lambda_7f42fd4ededb0f2ffe7fdd3cb9ccc2fc>,void>::_Target_type": {"offset": "0x2F810"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Copy": {"offset": "0xF540"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Delete_this": {"offset": "0xF620"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Do_call": {"offset": "0xF5C0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_814d5e0b3a45fa1dd158672480f6c4ca>,bool,ConsoleExecutionContext &>::_Target_type": {"offset": "0xF610"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Copy": {"offset": "0x2BB10"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D460"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Move": {"offset": "0x2BB10"}, "std::_Func_impl_no_alloc<<lambda_8892d27287343574a78df06617f4581e>,void,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F820"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Copy": {"offset": "0xF4F0"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Do_call": {"offset": "0xF510"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Move": {"offset": "0xF4F0"}, "std::_Func_impl_no_alloc<<lambda_8cd3625eb407bd8277f6f13182e8d40e>,bool,NetLibrary *>::_Target_type": {"offset": "0xF530"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Copy": {"offset": "0x2BB30"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Do_call": {"offset": "0x2D4B0"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Move": {"offset": "0x2BB30"}, "std::_Func_impl_no_alloc<<lambda_8d69c0a1316b94453128759bad65ad58>,void>::_Target_type": {"offset": "0x2F840"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Copy": {"offset": "0x2BB40"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Delete_this": {"offset": "0x2CB00"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Do_call": {"offset": "0x2D4C0"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_93f9a070d138a1ef45eae8a2debfa335>,void>::_Target_type": {"offset": "0x2F850"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Copy": {"offset": "0x2BBD0"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Delete_this": {"offset": "0x2CB40"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Do_call": {"offset": "0x2D520"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Move": {"offset": "0x2EA40"}, "std::_Func_impl_no_alloc<<lambda_9972b16a2e64036ea33e6044fce7be78>,void,enum fx::CachedResourceMounter::StatusType,int,int>::_Target_type": {"offset": "0x2F860"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Copy": {"offset": "0x2BC40"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Do_call": {"offset": "0x2D540"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Move": {"offset": "0x2BC40"}, "std::_Func_impl_no_alloc<<lambda_a144fb46b425fc3b58f533c8c43b50dc>,bool,NetAddress>::_Target_type": {"offset": "0x2F870"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Copy": {"offset": "0x2BC60"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Do_call": {"offset": "0x2D570"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Move": {"offset": "0x2BC60"}, "std::_Func_impl_no_alloc<<lambda_a8054199a4ddf1cc38d5d6e286b094f9>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>::_Target_type": {"offset": "0x2F880"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Copy": {"offset": "0x2BC80"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Delete_this": {"offset": "0x2CB80"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Do_call": {"offset": "0x2D220"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_a9ea5f9372bb612812c52de723ed7985>,void>::_Target_type": {"offset": "0x2F890"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Copy": {"offset": "0x2BCE0"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Do_call": {"offset": "0x2D640"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Move": {"offset": "0x2BCE0"}, "std::_Func_impl_no_alloc<<lambda_aede245bf1e1a01b9dbdbbcdff145d56>,bool,char const *>::_Target_type": {"offset": "0x2F8A0"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Copy": {"offset": "0x2BD00"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Do_call": {"offset": "0x2D660"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Move": {"offset": "0x2BD00"}, "std::_Func_impl_no_alloc<<lambda_af2f656c2b4e658b33e73066d2c0aac3>,void>::_Target_type": {"offset": "0x2F8B0"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Copy": {"offset": "0x2BDA0"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Delete_this": {"offset": "0x2CBE0"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Do_call": {"offset": "0x2D710"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_bb63d0011cba966169dbe6af3f2322ad>,void>::_Target_type": {"offset": "0x2F8D0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Copy": {"offset": "0xF760"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Delete_this": {"offset": "0xF7E0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Do_call": {"offset": "0xF7C0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_bf7610c50ebb1884e5917f6a81016c78>,void>::_Target_type": {"offset": "0xF7D0"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Copy": {"offset": "0x2BE20"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Do_call": {"offset": "0x2D720"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Move": {"offset": "0x2BE20"}, "std::_Func_impl_no_alloc<<lambda_c30517fa295c761fed13897c08746987>,void,fx::ScriptContext &>::_Target_type": {"offset": "0x2F8E0"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Copy": {"offset": "0xF480"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Do_call": {"offset": "0xF4A0"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Move": {"offset": "0xF480"}, "std::_Func_impl_no_alloc<<lambda_ca3a5f8b88d0cff5b76decee376953c6>,bool,fx::ResourceManager *>::_Target_type": {"offset": "0xF4C0"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Copy": {"offset": "0x2BE40"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D810"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Move": {"offset": "0x2BE40"}, "std::_Func_impl_no_alloc<<lambda_ccef67ee9e1f3964b744327541e660f7>,void,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F8F0"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Copy": {"offset": "0x2BE50"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Do_call": {"offset": "0x2D850"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Move": {"offset": "0x2BE50"}, "std::_Func_impl_no_alloc<<lambda_cdec71b40b220d4479babf37493ab33c>,void,Concurrency::task<void> >::_Target_type": {"offset": "0x2F900"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Copy": {"offset": "0x2BE70"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Do_call": {"offset": "0x2D860"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Move": {"offset": "0x2BE70"}, "std::_Func_impl_no_alloc<<lambda_d3f726135981b674c6372c3155e227f0>,bool>::_Target_type": {"offset": "0x2F910"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Copy": {"offset": "0x2BE90"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Delete_this": {"offset": "0xF620"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Do_call": {"offset": "0x2D880"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_dddb32a458266b5f83be2f932cb33dba>,unsigned char,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Target_type": {"offset": "0x2F920"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Copy": {"offset": "0x2BF10"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Delete_this": {"offset": "0xF620"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Do_call": {"offset": "0x2D880"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Move": {"offset": "0xE7B0"}, "std::_Func_impl_no_alloc<<lambda_dfadb08385c0ecb100cd522a7df6a8ea>,unsigned char,Concurrency::task<void> >::_Target_type": {"offset": "0x2F930"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Copy": {"offset": "0x2BF90"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D980"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Move": {"offset": "0x2BF90"}, "std::_Func_impl_no_alloc<<lambda_e43928a6c9631a7864693de043813e0c>,void,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F940"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Copy": {"offset": "0x2BFA0"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Do_call": {"offset": "0x2D9C0"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Move": {"offset": "0x2BFA0"}, "std::_Func_impl_no_alloc<<lambda_e7f392b84489a21e401e6f6ee18028f7>,void,char const *,unsigned __int64>::_Target_type": {"offset": "0x2F950"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Copy": {"offset": "0x2BFB0"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Do_call": {"offset": "0x2D9D0"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Move": {"offset": "0x2BFB0"}, "std::_Func_impl_no_alloc<<lambda_e928cb0d4d4fac6734ea867cf0bd12b5>,bool,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *>::_Target_type": {"offset": "0x2F960"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Copy": {"offset": "0x2BFC0"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Delete_this": {"offset": "0x2CC20"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Do_call": {"offset": "0x2DA20"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Move": {"offset": "0x2EAA0"}, "std::_Func_impl_no_alloc<<lambda_f25c37099038263181b5186a3fa41b37>,void>::_Target_type": {"offset": "0x2F970"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Copy": {"offset": "0x2C000"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Delete_this": {"offset": "0xF4E0"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Do_call": {"offset": "0x2D850"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Get": {"offset": "0xF4D0"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Move": {"offset": "0x2C000"}, "std::_Func_impl_no_alloc<<lambda_fb97e3c9cd5e41f7ab47d234a2ca4c5e>,void,Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::_Target_type": {"offset": "0x2F980"}, "std::_Hash<std::_Umap_traits<unsigned int,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool>,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> > >,1> >::_Forced_rehash": {"offset": "0x2E0A0"}, "std::_Hash<std::_Umap_traits<unsigned int,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool>,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> > >,1> >::emplace<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> > >": {"offset": "0x17F00"}, "std::_Init_once_completer::~_Init_once_completer": {"offset": "0x3D420"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x161E0"}, "std::_Insertion_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x40300"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >,void *> > >": {"offset": "0x1C700"}, "std::_Maklocstr<char>": {"offset": "0xE6F0"}, "std::_Move_unchecked<std::pair<unsigned __int64,unsigned __int64> *,std::pair<unsigned __int64,unsigned __int64> *>": {"offset": "0x403C0"}, "std::_Optimistic_temporary_buffer<std::pair<unsigned __int64,unsigned __int64> >::~_Optimistic_temporary_buffer<std::pair<unsigned __int64,unsigned __int64> >": {"offset": "0x40500"}, "std::_Optional_construct_base<skyr::v1::host>::_Construct<skyr::v1::host const &>": {"offset": "0x414D0"}, "std::_Optional_construct_base<skyr::v1::host>::~_Optional_construct_base<skyr::v1::host>": {"offset": "0x42160"}, "std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x42150"}, "std::_Ref_count_base::_Decref": {"offset": "0x2C9D0"}, "std::_Ref_count_base::_Get_deleter": {"offset": "0xE7B0"}, "std::_Ref_count_obj2<Concurrency::details::_ExceptionHolder>::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<Concurrency::details::_ExceptionHolder>::_Destroy": {"offset": "0x2CF80"}, "std::_Ref_count_obj2<Concurrency::details::_Task_completion_event_impl<unsigned char> >::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<Concurrency::details::_Task_completion_event_impl<unsigned char> >::_Destroy": {"offset": "0x2CE00"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<unsigned char> >::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<Concurrency::details::_Task_impl<unsigned char> >::_Destroy": {"offset": "0x2CF70"}, "std::_Ref_count_obj2<`DownloadResources'::`2'::ProgressData>::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<`DownloadResources'::`2'::ProgressData>::_Destroy": {"offset": "0xA490"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<internal::ConsoleVariableEntry<bool> >::_Destroy": {"offset": "0xF970"}, "std::_Ref_count_obj2<nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Delete_this": {"offset": "0xFA20"}, "std::_Ref_count_obj2<nlohmann::json_abi_v3_11_2::detail::output_string_adapter<char,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Destroy": {"offset": "0x2CF90"}, "std::_Stable_sort_unchecked<std::pair<unsigned __int64,unsigned __int64> *,`skyr::v1::ipv6_address::serialize'::`29'::<lambda_1> >": {"offset": "0x403F0"}, "std::_String_val<std::_Simple_types<char32_t> >::_Xran": {"offset": "0x2FF00"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x2FF00"}, "std::_Throw_bad_array_new_length": {"offset": "0xD430"}, "std::_Throw_bad_cast": {"offset": "0x380F0"}, "std::_Throw_bad_optional_access": {"offset": "0x2F990"}, "std::_Throw_bad_variant_access": {"offset": "0x2F9C0"}, "std::_Throw_tree_length_error": {"offset": "0xD450"}, "std::_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~_Tidy_deallocate_guard<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x45C50"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x37F00"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Find_hint<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15A50"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::_Find_lower_bound<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15D50"}, "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >::~_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,0> >": {"offset": "0x1C9B0"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2A00"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x2C80"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0xA4F0"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Eqrange<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x15700"}, "std::_Tree<std::_Tset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::erase": {"offset": "0x31FA0"}, "std::_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >::~_Tree<std::_Tset_traits<tbb::detail::d1::global_control *,tbb::detail::r1::control_storage_comparator,tbb::detail::d1::tbb_allocator<tbb::detail::d1::global_control *>,0> >": {"offset": "0x3DA70"}, "std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Freenode<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x15E20"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0xA4D0"}, "std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x1C6C0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x15990"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Extract": {"offset": "0x2DB10"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Insert_node": {"offset": "0xD1D0"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Lrotate": {"offset": "0x2E800"}, "std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Rrotate": {"offset": "0x2F0B0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> > >": {"offset": "0x15890"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Insert_node": {"offset": "0xD1D0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x29A0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0xD1D0"}, "std::_Tree_val<std::_Tree_simple_types<tbb::detail::d1::global_control *> >::_Erase_tree<tbb::detail::d1::tbb_allocator<std::_Tree_node<tbb::detail::d1::global_control *,void *> > >": {"offset": "0x3DA00"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1CA70"}, "std::_Uninitialized_backout_al<std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~_Uninitialized_backout_al<std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1CA80"}, "std::_Uninitialized_copy<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x16D30"}, "std::_Uninitialized_copy_n<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x41560"}, "std::_Uninitialized_copy_n<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x16DF0"}, "std::_Uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char> > *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x16EA0"}, "std::_Uninitialized_move<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x42EA0"}, "std::_Uninitialized_move<std::shared_ptr<Concurrency::details::_Task_impl<unsigned char> > *,std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<unsigned char> > > >": {"offset": "0x16F20"}, "std::_Uninitialized_move<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *,std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x16F90"}, "std::_Variant_base<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>::_Destroy": {"offset": "0x2CFA0"}, "std::_Variant_destroy_layer_<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>::~_Variant_destroy_layer_<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>": {"offset": "0x42170"}, "std::_Variant_dispatcher<std::integer_sequence<unsigned __int64,0> >::_Dispatch2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_46ee34ba601a29fbc6a3c1440c18ac8c> const &,std::variant<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> const &,1>": {"offset": "0x14F10"}, "std::_Variant_dispatcher<std::integer_sequence<unsigned __int64,0> >::_Dispatch2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,`skyr::v1::host::serialize'::`2'::<lambda_1> const &,std::variant<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> const &,1>": {"offset": "0x14F10"}, "std::_Variant_raw_visit1<2>::_Visit<std::_Variant_assign_visitor<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>,std::_Variant_storage_<0,skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> >": {"offset": "0x41870"}, "std::_Variant_raw_visit1<2>::_Visit<std::_Variant_assign_visitor<skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host>,std::_Variant_storage_<0,skyr::v1::ipv4_address,skyr::v1::ipv6_address,skyr::v1::domain,skyr::v1::opaque_host,skyr::v1::empty_host> const &>": {"offset": "0x415F0"}, "std::_Xlen_string": {"offset": "0xD470"}, "std::allocator<char32_t>::allocate": {"offset": "0x45FC0"}, "std::allocator<char32_t>::deallocate": {"offset": "0x46030"}, "std::allocator<char>::allocate": {"offset": "0xD490"}, "std::allocator<char>::deallocate": {"offset": "0x30730"}, "std::allocator<std::_Tree_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void *> >::allocate": {"offset": "0x2FF90"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::allocate": {"offset": "0x30070"}, "std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::deallocate": {"offset": "0x307C0"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::allocate": {"offset": "0x43150"}, "std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x431C0"}, "std::allocator<std::shared_ptr<Concurrency::details::_Task_impl<unsigned char> > >::deallocate": {"offset": "0x30770"}, "std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::allocate": {"offset": "0x300E0"}, "std::allocator<std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::deallocate": {"offset": "0x30810"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x30730"}, "std::allocator<void *>::allocate": {"offset": "0x2FF20"}, "std::allocator<wchar_t>::allocate": {"offset": "0xD4F0"}, "std::bad_alloc::bad_alloc": {"offset": "0x3CC00"}, "std::bad_alloc::~bad_alloc": {"offset": "0xA6E0"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0xA2D0"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0xA6E0"}, "std::bad_cast::bad_cast": {"offset": "0x37ED0"}, "std::bad_cast::~bad_cast": {"offset": "0xA6E0"}, "std::bad_optional_access::bad_optional_access": {"offset": "0x1BBB0"}, "std::bad_optional_access::what": {"offset": "0x34480"}, "std::bad_optional_access::~bad_optional_access": {"offset": "0xA6E0"}, "std::bad_variant_access::bad_variant_access": {"offset": "0x1BC10"}, "std::bad_variant_access::what": {"offset": "0x34490"}, "std::bad_variant_access::~bad_variant_access": {"offset": "0xA6E0"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x1D8A0"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x1DCC0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x28E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x16450"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x165C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x16750"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>": {"offset": "0x168A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x16A30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append'::`2'::<lambda_1>,char const *,unsigned __int64>": {"offset": "0x165C0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert'::`2'::<lambda_1>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x16A30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::push_back'::`2'::<lambda_1>,char>": {"offset": "0x16450"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0xA5B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x30150"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0xD6E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::back": {"offset": "0x30320"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1B3F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> ><char *,0>": {"offset": "0xFD00"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert": {"offset": "0x32B50"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<char> > >,0>": {"offset": "0x18E30"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::push_back": {"offset": "0x4C3F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::reserve": {"offset": "0x334F0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::resize": {"offset": "0x335A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0xA5B0"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Reallocate_grow_by<`std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::insert'::`2'::<lambda_1>,unsigned __int64,unsigned __int64,char32_t>": {"offset": "0x446E0"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Tidy_deallocate": {"offset": "0x45F50"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >": {"offset": "0x45BA0"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::push_back": {"offset": "0x46FF0"}, "std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::~basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >": {"offset": "0x45C60"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x34950"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0xD560"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x394B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x3C3E0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x3C540"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0xA610"}, "std::basic_string_view<char,std::char_traits<char> >::_Xran": {"offset": "0x3EC50"}, "std::basic_string_view<char,std::char_traits<char> >::basic_string_view<char,std::char_traits<char> >": {"offset": "0x1B520"}, "std::basic_string_view<char,std::char_traits<char> >::find_first_of": {"offset": "0x43210"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x330D0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x33240"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x33790"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x33910"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x34380"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::~basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1CAE0"}, "std::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x1B550"}, "std::current_exception": {"offset": "0x30710"}, "std::deque<char,std::allocator<char> >::_Emplace_back_internal<char const &>": {"offset": "0x14F20"}, "std::deque<char,std::allocator<char> >::_Growmap": {"offset": "0x2E3A0"}, "std::deque<char,std::allocator<char> >::_Insert_range<1,char const *,char const *>": {"offset": "0x15F10"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<0>::~_Restore_old_size_guard<0>": {"offset": "0x1C940"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<1>::~_Restore_old_size_guard<1>": {"offset": "0x1C910"}, "std::deque<char,std::allocator<char> >::_Xlen": {"offset": "0x2FEC0"}, "std::deque<char,std::allocator<char> >::~deque<char,std::allocator<char> >": {"offset": "0x1CBB0"}, "std::deque<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Growmap": {"offset": "0x2E580"}, "std::deque<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy": {"offset": "0x2F9F0"}, "std::deque<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Xlen": {"offset": "0x2FEC0"}, "std::distance<std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x17A80"}, "std::endl<char,std::char_traits<char> >": {"offset": "0x18190"}, "std::exception::exception": {"offset": "0xA300"}, "std::exception::what": {"offset": "0xE5C0"}, "std::exception_ptr::exception_ptr": {"offset": "0x1BD40"}, "std::exception_ptr::~exception_ptr": {"offset": "0x1DC70"}, "std::fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> > > > > >": {"offset": "0x181D0"}, "std::function<bool __cdecl(ConsoleExecutionContext &)>::~function<bool __cdecl(ConsoleExecutionContext &)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(NetAddress)>::~function<bool __cdecl(NetAddress)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(NetLibrary *)>::~function<bool __cdecl(NetLibrary *)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(char const *)>::~function<bool __cdecl(char const *)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(fx::ResourceManager *)>::~function<bool __cdecl(fx::ResourceManager *)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>::~function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,ProgramArguments const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *)>::~function<bool __cdecl(std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &,bool *)>": {"offset": "0x1C3A0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x1C3A0"}, "std::function<unsigned char __cdecl(Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >)>::~function<unsigned char __cdecl(Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >)>": {"offset": "0x1C3A0"}, "std::function<unsigned char __cdecl(Concurrency::task<void>)>::~function<unsigned char __cdecl(Concurrency::task<void>)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >)>::~function<void __cdecl(Concurrency::task<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> >)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(Concurrency::task<void>)>::~function<void __cdecl(Concurrency::task<void>)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(bool const &)>::~function<void __cdecl(bool const &)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(bool,char const *,unsigned __int64)>::~function<void __cdecl(bool,char const *,unsigned __int64)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(enum fx::CachedResourceMounter::StatusType,int,int)>::~function<void __cdecl(enum fx::CachedResourceMounter::StatusType,int,int)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(fwRefContainer<fx::Resource> const &)>::~function<void __cdecl(fwRefContainer<fx::Resource> const &)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(fx::ScriptContext &)>::~function<void __cdecl(fx::ScriptContext &)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &)>::~function<void __cdecl(std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > > const &)>": {"offset": "0x1C3A0"}, "std::function<void __cdecl(void)>::function<void __cdecl(void)>": {"offset": "0x1B690"}, "std::function<void __cdecl(void)>::~function<void __cdecl(void)>": {"offset": "0x1C3A0"}, "std::invalid_argument::invalid_argument": {"offset": "0x3CCE0"}, "std::invalid_argument::~invalid_argument": {"offset": "0xA6E0"}, "std::length_error::length_error": {"offset": "0x3CD70"}, "std::length_error::~length_error": {"offset": "0xA6E0"}, "std::locale::~locale": {"offset": "0x37FA0"}, "std::lock_guard<std::mutex>::lock_guard<std::mutex>": {"offset": "0x1B710"}, "std::lock_guard<std::mutex>::~lock_guard<std::mutex>": {"offset": "0x1CE10"}, "std::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>::lock_guard<tbb::detail::r1::concurrent_monitor_mutex>": {"offset": "0x3E030"}, "std::logic_error::logic_error": {"offset": "0x3CDC0"}, "std::make_shared<Concurrency::details::_ExceptionHolder,std::exception_ptr &,Concurrency::details::_TaskCreationCallstack const &>": {"offset": "0x19030"}, "std::map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::~map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >": {"offset": "0x1C9B0"}, "std::move<tl::unexpected<fx::ResourceManagerError> &>": {"offset": "0x191C0"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x38680"}, "std::numpunct<char>::do_falsename": {"offset": "0x38690"}, "std::numpunct<char>::do_grouping": {"offset": "0x386D0"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x38710"}, "std::numpunct<char>::do_truename": {"offset": "0x38720"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x37CE0"}, "std::optional<skyr::v1::host>::~optional<skyr::v1::host>": {"offset": "0x42160"}, "std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x42150"}, "std::out_of_range::out_of_range": {"offset": "0x3CEA0"}, "std::out_of_range::~out_of_range": {"offset": "0xA6E0"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1B740"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CEA0"}, "std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1CEA0"}, "std::pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >::~pair<unsigned int const ,std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool> >": {"offset": "0x1CE70"}, "std::rethrow_exception": {"offset": "0x336D0"}, "std::rotate<std::_Deque_unchecked_iterator<std::_Deque_val<std::_Deque_simple_types<char> > > >": {"offset": "0x19250"}, "std::runtime_error::runtime_error": {"offset": "0x1BFB0"}, "std::runtime_error::~runtime_error": {"offset": "0xA6E0"}, "std::shared_lock<std::shared_mutex>::~shared_lock<std::shared_mutex>": {"offset": "0x1D150"}, "std::shared_ptr<Concurrency::details::_ExceptionHolder>::~shared_ptr<Concurrency::details::_ExceptionHolder>": {"offset": "0x1CE20"}, "std::shared_ptr<Concurrency::details::_Task_impl<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >::~shared_ptr<Concurrency::details::_Task_impl<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError> > >": {"offset": "0x1CE20"}, "std::shared_ptr<Concurrency::details::_Task_impl<unsigned char> >::~shared_ptr<Concurrency::details::_Task_impl<unsigned char> >": {"offset": "0x1CE20"}, "std::shared_ptr<Concurrency::details::_Task_impl_base>::~shared_ptr<Concurrency::details::_Task_impl_base>": {"offset": "0x1CE20"}, "std::shared_ptr<Concurrency::scheduler_interface>::~shared_ptr<Concurrency::scheduler_interface>": {"offset": "0x1CE20"}, "std::shared_ptr<`DownloadResources'::`2'::ProgressData>::~shared_ptr<`DownloadResources'::`2'::ProgressData>": {"offset": "0x1D160"}, "std::shared_ptr<internal::ConsoleVariableEntry<bool> >::~shared_ptr<internal::ConsoleVariableEntry<bool> >": {"offset": "0x1CE20"}, "std::shared_ptr<internal::ConsoleVariableEntryBase>::~shared_ptr<internal::ConsoleVariableEntryBase>": {"offset": "0x1CE20"}, "std::shared_ptr<nlohmann::json_abi_v3_11_2::detail::output_adapter_protocol<char> >::~shared_ptr<nlohmann::json_abi_v3_11_2::detail::output_adapter_protocol<char> >": {"offset": "0x1CE20"}, "std::to_string": {"offset": "0x34260"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0xA5B0"}, "std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,int,bool>::~tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,int,int,bool>": {"offset": "0x1D1B0"}, "std::tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool>::~tuple<std::function<void __cdecl(char const *,unsigned __int64)>,bool>": {"offset": "0x1D290"}, "std::tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::~tuple<tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1D220"}, "std::unique_lock<std::shared_mutex>::~unique_lock<std::shared_mutex>": {"offset": "0x3E110"}, "std::unique_ptr<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore,std::default_delete<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore> >::~unique_ptr<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore,std::default_delete<Concurrency::details::_DefaultPPLTaskScheduler::_PPLTaskChore> >": {"offset": "0x1D3A0"}, "std::unique_ptr<Concurrency::details::_TaskProcHandle,std::default_delete<Concurrency::details::_TaskProcHandle> >::~unique_ptr<Concurrency::details::_TaskProcHandle,std::default_delete<Concurrency::details::_TaskProcHandle> >": {"offset": "0x1D2C0"}, "std::unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >::~unique_ptr<ConsoleCommand,std::default_delete<ConsoleCommand> >": {"offset": "0x1D370"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0x1D2E0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x1D2C0"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_2b6e2eb532003cdbbbdd24e42b6b50a7> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_2b6e2eb532003cdbbbdd24e42b6b50a7> >": {"offset": "0x1D2F0"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_467247bbe980e6f07ddf4bbced9fbdec> >": {"offset": "0x1D2F0"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_780e14317b8bd94ca5e61a43c5b6e20e> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_780e14317b8bd94ca5e61a43c5b6e20e> >": {"offset": "0x1D2F0"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_a886e99c8d250c772cbca4cc3a028cee> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_a886e99c8d250c772cbca4cc3a028cee> >": {"offset": "0x1D2F0"}, "std::unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >::~unique_ptr<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,<lambda_f51660135b5693812451c8a92d76b514> >": {"offset": "0x1D2F0"}, "std::use_facet<std::ctype<char> >": {"offset": "0x3EB60"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x379B0"}, "std::weak_ptr<Concurrency::details::_Task_impl_base>::~weak_ptr<Concurrency::details::_Task_impl_base>": {"offset": "0x1C600"}, "tbb::detail::d0::raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >::~raii_guard<<lambda_0e1f910659e28e696fc4a81fef6b33ad> >": {"offset": "0x1CFD0"}, "tbb::detail::d1::unique_scoped_lock<tbb::detail::d1::spin_mutex>::~unique_scoped_lock<tbb::detail::d1::spin_mutex>": {"offset": "0x3DAE0"}, "tbb::detail::d2::concurrent_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::clear": {"offset": "0x30410"}, "tbb::detail::d2::concurrent_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::push": {"offset": "0x33440"}, "tbb::detail::d2::micro_queue<std::function<void __cdecl(void)>,tbb::detail::d1::cache_aligned_allocator<std::function<void __cdecl(void)> > >::prepare_page": {"offset": "0x332D0"}, "tbb::detail::r1::AvailableHwConcurrency": {"offset": "0x3DD30"}, "tbb::detail::r1::PrintExtraVersionInfo": {"offset": "0x3DB50"}, "tbb::detail::r1::`dynamic initializer for '__TBB_InitOnceHiddenInstance''": {"offset": "0x1B20"}, "tbb::detail::r1::`dynamic initializer for 'allowed_parallelism_ctl''": {"offset": "0x19B0"}, "tbb::detail::r1::`dynamic initializer for 'lifetime_ctl''": {"offset": "0x1A00"}, "tbb::detail::r1::`dynamic initializer for 'stack_size_ctl''": {"offset": "0x1A50"}, "tbb::detail::r1::`dynamic initializer for 'terminate_on_exception_ctl''": {"offset": "0x1AA0"}, "tbb::detail::r1::allocate_memory": {"offset": "0x3D450"}, "tbb::detail::r1::allowed_parallelism_control::active_value": {"offset": "0x3D780"}, "tbb::detail::r1::allowed_parallelism_control::apply_active": {"offset": "0x3D770"}, "tbb::detail::r1::allowed_parallelism_control::default_value": {"offset": "0x3D6E0"}, "tbb::detail::r1::allowed_parallelism_control::is_first_arg_preferred": {"offset": "0x3D760"}, "tbb::detail::r1::arena::has_enqueued_tasks": {"offset": "0x3EB20"}, "tbb::detail::r1::bad_last_alloc::bad_last_alloc": {"offset": "0x3CC70"}, "tbb::detail::r1::bad_last_alloc::what": {"offset": "0x3D390"}, "tbb::detail::r1::bad_last_alloc::~bad_last_alloc": {"offset": "0xA6E0"}, "tbb::detail::r1::cache_aligned_allocate": {"offset": "0x3D480"}, "tbb::detail::r1::cache_aligned_deallocate": {"offset": "0x3D4E0"}, "tbb::detail::r1::clear_address_waiter_table": {"offset": "0x3E8B0"}, "tbb::detail::r1::concurrent_monitor_mutex::get_semaphore": {"offset": "0x3E2A0"}, "tbb::detail::r1::control_storage::active_value": {"offset": "0x3D650"}, "tbb::detail::r1::control_storage::apply_active": {"offset": "0x3D630"}, "tbb::detail::r1::control_storage::is_first_arg_preferred": {"offset": "0x3D640"}, "tbb::detail::r1::deallocate_memory": {"offset": "0x3D4F0"}, "tbb::detail::r1::detect_cpu_features": {"offset": "0x3DC20"}, "tbb::detail::r1::do_throw<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x3C830"}, "tbb::detail::r1::do_throw<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x3C860"}, "tbb::detail::r1::do_throw<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x3C890"}, "tbb::detail::r1::do_throw<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x3C8C0"}, "tbb::detail::r1::do_throw<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x3C8F0"}, "tbb::detail::r1::do_throw<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x3C920"}, "tbb::detail::r1::do_throw<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x3C950"}, "tbb::detail::r1::do_throw<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x3C980"}, "tbb::detail::r1::do_throw<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x3C9B0"}, "tbb::detail::r1::do_throw<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x3C9E0"}, "tbb::detail::r1::do_throw<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x3CA10"}, "tbb::detail::r1::do_throw<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x3CA40"}, "tbb::detail::r1::do_throw_noexcept<<lambda_148cdb9fe4058b498675dc761af2b55f> >": {"offset": "0x3CA70"}, "tbb::detail::r1::do_throw_noexcept<<lambda_4bd345ce870bcb6a9dba57dde6595eaf> >": {"offset": "0x3CA90"}, "tbb::detail::r1::do_throw_noexcept<<lambda_62d39c82ce1150ac872f53fd210c4174> >": {"offset": "0x3CAB0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_89482d616e0aac800ea8950dff220b82> >": {"offset": "0x3CAD0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_a1465ae1007134a6074a1c25f5c5eb73> >": {"offset": "0x3CAF0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ae2846614598588cc214b4c5ac29e5b9> >": {"offset": "0x3CB10"}, "tbb::detail::r1::do_throw_noexcept<<lambda_b99707c9ac0dceece1b9d1a5418a9e2c> >": {"offset": "0x3CB30"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c2671fb7d58401c3433fec7a51743eed> >": {"offset": "0x3CB50"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c4a59ba630f650a3c6857dbdee4106d9> >": {"offset": "0x3CB70"}, "tbb::detail::r1::do_throw_noexcept<<lambda_c7d6cdb286355fa15477645785091432> >": {"offset": "0x3CB90"}, "tbb::detail::r1::do_throw_noexcept<<lambda_ea483f4268cd915b2f894dd67bba646e> >": {"offset": "0x3CBB0"}, "tbb::detail::r1::do_throw_noexcept<<lambda_f39008d8a31f439e214a4bdb6047acdc> >": {"offset": "0x3CBD0"}, "tbb::detail::r1::dummy_allocate_binding_handler": {"offset": "0xE7B0"}, "tbb::detail::r1::dummy_apply_affinity": {"offset": "0xA490"}, "tbb::detail::r1::dummy_deallocate_binding_handler": {"offset": "0xA490"}, "tbb::detail::r1::dummy_destroy_system_topology": {"offset": "0xA490"}, "tbb::detail::r1::dummy_get_default_concurrency": {"offset": "0x3E810"}, "tbb::detail::r1::dummy_restore_affinity": {"offset": "0xA490"}, "tbb::detail::r1::dynamic_link": {"offset": "0x3DB40"}, "tbb::detail::r1::dynamic_unlink": {"offset": "0xA490"}, "tbb::detail::r1::dynamic_unlink_all": {"offset": "0xA490"}, "tbb::detail::r1::gcc_rethrow_exception_broken": {"offset": "0xE870"}, "tbb::detail::r1::get_address_waiter_table": {"offset": "0x3EA50"}, "tbb::detail::r1::governor::acquire_resources": {"offset": "0x3E820"}, "tbb::detail::r1::governor::default_num_threads": {"offset": "0x3E220"}, "tbb::detail::r1::governor::release_resources": {"offset": "0x3E870"}, "tbb::detail::r1::handle_perror": {"offset": "0x3D210"}, "tbb::detail::r1::initialize_allocate_handler": {"offset": "0x3D3C0"}, "tbb::detail::r1::initialize_cache_aligned_allocate_handler": {"offset": "0x3D3F0"}, "tbb::detail::r1::initialize_cache_aligned_allocator": {"offset": "0x3D500"}, "tbb::detail::r1::initialize_hardware_concurrency_info": {"offset": "0x3DDF0"}, "tbb::detail::r1::lifetime_control::apply_active": {"offset": "0x3D890"}, "tbb::detail::r1::lifetime_control::default_value": {"offset": "0xE7B0"}, "tbb::detail::r1::lifetime_control::is_first_arg_preferred": {"offset": "0xE870"}, "tbb::detail::r1::market::add_ref_unsafe": {"offset": "0x3E120"}, "tbb::detail::r1::market::app_parallelism_limit": {"offset": "0x3DB00"}, "tbb::detail::r1::market::release": {"offset": "0x3E340"}, "tbb::detail::r1::market::set_active_num_workers": {"offset": "0x3E4C0"}, "tbb::detail::r1::market::update_allotment": {"offset": "0x3E700"}, "tbb::detail::r1::missing_wait::missing_wait": {"offset": "0x3CE40"}, "tbb::detail::r1::missing_wait::what": {"offset": "0x3D3A0"}, "tbb::detail::r1::missing_wait::~missing_wait": {"offset": "0xA6E0"}, "tbb::detail::r1::runtime_warning": {"offset": "0x3DC70"}, "tbb::detail::r1::stack_size_control::apply_active": {"offset": "0x3D630"}, "tbb::detail::r1::stack_size_control::default_value": {"offset": "0x3D880"}, "tbb::detail::r1::std_cache_aligned_allocate": {"offset": "0x3D610"}, "tbb::detail::r1::std_cache_aligned_deallocate": {"offset": "0x3D620"}, "tbb::detail::r1::terminate_on_exception": {"offset": "0x3DB20"}, "tbb::detail::r1::terminate_on_exception_control::default_value": {"offset": "0xE7B0"}, "tbb::detail::r1::throw_exception": {"offset": "0x3D2E0"}, "tbb::detail::r1::unsafe_wait::unsafe_wait": {"offset": "0x3CF30"}, "tbb::detail::r1::unsafe_wait::~unsafe_wait": {"offset": "0xA6E0"}, "tbb::detail::r1::user_abort::user_abort": {"offset": "0x3CFC0"}, "tbb::detail::r1::user_abort::what": {"offset": "0x3D3B0"}, "tbb::detail::r1::user_abort::~user_abort": {"offset": "0xA6E0"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::bad_expected_access<enum skyr::v1::domain_errc>": {"offset": "0x41DB0"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::what": {"offset": "0x42BF0"}, "tl::bad_expected_access<enum skyr::v1::domain_errc>::~bad_expected_access<enum skyr::v1::domain_errc>": {"offset": "0xA6E0"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0x42FC0"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::what": {"offset": "0x42BF0"}, "tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>::~bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0xA6E0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::bad_expected_access<enum skyr::v1::url_parse_errc>": {"offset": "0x47FE0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::what": {"offset": "0x42BF0"}, "tl::bad_expected_access<enum skyr::v1::url_parse_errc>::~bad_expected_access<enum skyr::v1::url_parse_errc>": {"offset": "0xA6E0"}, "tl::detail::and_then_impl<tl::expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>,`skyr::v1::details::make_url'::`2'::<lambda_1>,0,tl::expected<skyr::v1::url,enum skyr::v1::url_parse_errc> >": {"offset": "0x41B60"}, "tl::detail::expected_operations_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::assign<fwRefContainer<fx::Resource>,0>": {"offset": "0x17040"}, "tl::detail::expected_operations_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::geterr": {"offset": "0x191C0"}, "tl::detail::expected_storage_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError,0,0>::~expected_storage_base<fwRefContainer<fx::Resource>,fx::ResourceManagerError,0,0>": {"offset": "0x1CD20"}, "tl::detail::expected_storage_base<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc,0,1>::~expected_storage_base<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc,0,1>": {"offset": "0x1CCB0"}, "tl::detail::expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc,0,1>::~expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc,0,1>": {"offset": "0x1CCB0"}, "tl::detail::expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc,0,1>::~expected_storage_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc,0,1>": {"offset": "0x1CCB0"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::domain_errc> >": {"offset": "0x41D30"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::percent_encoding::percent_encode_errc> >": {"offset": "0x42F40"}, "tl::detail::throw_exception<tl::bad_expected_access<enum skyr::v1::url_parse_errc> >": {"offset": "0x47FB0"}, "tl::expected<char,enum skyr::v1::percent_encoding::percent_encode_errc>::err": {"offset": "0x191C0"}, "tl::expected<char32_t,enum skyr::v1::domain_errc>::err": {"offset": "0x191C0"}, "tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>": {"offset": "0x1B610"}, "tl::expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>::~expected<fwRefContainer<fx::Resource>,fx::ResourceManagerError>": {"offset": "0x1CC90"}, "tl::expected<skyr::v1::host,enum skyr::v1::url_parse_errc>::err": {"offset": "0x191C0"}, "tl::expected<skyr::v1::host,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::host,enum skyr::v1::url_parse_errc>": {"offset": "0x42160"}, "tl::expected<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::opaque_host,enum skyr::v1::url_parse_errc>": {"offset": "0x1CC80"}, "tl::expected<skyr::v1::url,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::url,enum skyr::v1::url_parse_errc>": {"offset": "0x1CCA0"}, "tl::expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>::~expected<skyr::v1::url_record,enum skyr::v1::url_parse_errc>": {"offset": "0x42180"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::err": {"offset": "0x191C0"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::value<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>": {"offset": "0x4C510"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::domain_errc>": {"offset": "0x1CCB0"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::percent_encoding::percent_encode_errc>": {"offset": "0x1CC80"}, "tl::expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc>::~expected<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,enum skyr::v1::unicode::unicode_errc>": {"offset": "0x1CC80"}, "tl::expected<unsigned short,enum skyr::v1::url_parse_errc>::err": {"offset": "0x191C0"}, "tl::unexpected<enum skyr::v1::domain_errc>::value": {"offset": "0x191C0"}, "tl::unexpected<enum skyr::v1::percent_encoding::percent_encode_errc>::value": {"offset": "0x191C0"}, "tl::unexpected<enum skyr::v1::url_parse_errc>::value": {"offset": "0x191C0"}, "tl::unexpected<fx::ResourceManagerError>::~unexpected<fx::ResourceManagerError>": {"offset": "0xA5B0"}, "utf8::exception::exception": {"offset": "0x3B720"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x3A7A0"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x3B0D0"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x3B7B0"}, "utf8::invalid_code_point::what": {"offset": "0x3C6F0"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0xA6E0"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x3B820"}, "utf8::invalid_utf8::what": {"offset": "0x3C700"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0xA6E0"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x3B880"}, "utf8::not_enough_room::what": {"offset": "0x3C710"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0xA6E0"}, "va<int>": {"offset": "0x19FD0"}, "va<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x1A040"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x3AEB0"}, "vva": {"offset": "0x3C6D0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x1060"}}}