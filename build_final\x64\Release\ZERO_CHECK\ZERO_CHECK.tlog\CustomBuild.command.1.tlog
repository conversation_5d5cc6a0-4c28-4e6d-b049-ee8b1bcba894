^C:\USERS\<USER>\DESKTOP\FIVEM OFFSETS\BUILD_FINAL\CMAKEFILES\9087BC2AA933AE037247CAECBFA81F53\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/fivem offsets" "-BC:/Users/<USER>/Desktop/fivem offsets/build_final" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Desktop/fivem offsets/build_final/FiveMOffsetDumper.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
