cmake_minimum_required(VERSION 3.16)
project(FiveMSimpleCheat)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Windows
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Incluir Dear ImGui
set(IMGUI_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../external/imgui")

# Verificar se ImGui existe
if(NOT EXISTS "${IMGUI_DIR}")
    message(FATAL_ERROR "Dear ImGui not found! Please run setup_imgui.bat first")
endif()

# Arquivos do ImGui
set(IMGUI_SOURCES
    ${IMGUI_DIR}/imgui.cpp
    ${IMGUI_DIR}/imgui_demo.cpp
    ${IMGUI_DIR}/imgui_draw.cpp
    ${IMGUI_DIR}/imgui_tables.cpp
    ${IMGUI_DIR}/imgui_widgets.cpp
    ${IMGUI_DIR}/backends/imgui_impl_win32.cpp
    ${IMGUI_DIR}/backends/imgui_impl_dx11.cpp
)

# Arquivos do projeto - Cheat simples sem ImGui
set(SOURCES_SIMPLE
    simple_cheat_final.cpp
)

# Criar a DLL simples (sem ImGui)
add_library(FiveMCheat_FINAL SHARED ${SOURCES_SIMPLE})

# Arquivos do projeto - Cheat com ImGui
set(SOURCES
    simple_cheat.cpp
    ${IMGUI_SOURCES}
)

# Criar a DLL com ImGui
add_library(FiveMSimpleCheat SHARED ${SOURCES})

# Configurações para cheat simples
target_include_directories(FiveMCheat_FINAL PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

target_link_libraries(FiveMCheat_FINAL
    user32
    kernel32
)

# Incluir diretórios para cheat com ImGui
target_include_directories(FiveMSimpleCheat PRIVATE
    ${IMGUI_DIR}
    ${IMGUI_DIR}/backends
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Bibliotecas do Windows para cheat com ImGui
target_link_libraries(FiveMSimpleCheat
    d3d11
    dxgi
    user32
    kernel32
    gdi32
    winspool
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
)

# Configurações de compilação para ambos
if(MSVC)
    target_compile_options(FiveMCheat_FINAL PRIVATE /W3)
    target_compile_definitions(FiveMCheat_FINAL PRIVATE _CRT_SECURE_NO_WARNINGS)

    target_compile_options(FiveMSimpleCheat PRIVATE /W3)
    target_compile_definitions(FiveMSimpleCheat PRIVATE _CRT_SECURE_NO_WARNINGS)
endif()

# Configurações de saída para cheat simples
set_target_properties(FiveMCheat_FINAL PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
    LIBRARY_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    LIBRARY_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# Configurações de saída para cheat com ImGui
set_target_properties(FiveMSimpleCheat PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
    LIBRARY_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    LIBRARY_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# Copiar DLLs para diretório raiz após build
add_custom_command(TARGET FiveMCheat_FINAL POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:FiveMCheat_FINAL> ${CMAKE_CURRENT_SOURCE_DIR}/../FiveMCheat_FINAL.dll
    COMMENT "Copying simple cheat DLL to root directory"
)

add_custom_command(TARGET FiveMSimpleCheat POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:FiveMSimpleCheat> ${CMAKE_CURRENT_SOURCE_DIR}/../FiveMSimpleCheat.dll
    COMMENT "Copying ImGui cheat DLL to root directory"
)
