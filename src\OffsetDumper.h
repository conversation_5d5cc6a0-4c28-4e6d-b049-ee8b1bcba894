#pragma once
#include <Windows.h>
#include <vector>
#include <string>
#include <unordered_map>
#include "PatternScanner.h"
#include "OffsetDatabase.h"

struct OffsetInfo {
    std::string name;
    std::string description;
    uintptr_t address;
    uintptr_t offset;
    std::string pattern;
    bool found;
    std::string category;
};

class OffsetDumper {
public:
    OffsetDumper();
    ~OffsetDumper();
    
    // Main functions
    bool Initialize();
    void ScanAllOffsets();
    void ScanSpecificOffset(const std::string& name);
    
    // Getters
    const std::vector<OffsetInfo>& GetOffsets() const { return m_offsets; }
    const OffsetInfo* GetOffset(const std::string& name) const;
    
    // Export functions
    bool ExportToJSON(const std::string& filename) const;
    bool ExportToCppHeader(const std::string& filename) const;
    bool ExportToCheatEngineTable(const std::string& filename) const;
    
    // Status
    bool IsScanning() const { return m_isScanning; }
    float GetScanProgress() const { return m_scanProgress; }
    const std::string& GetLastError() const { return m_lastError; }
    
private:
    void InitializeOffsetDatabase();
    void ScanOffset(OffsetInfo& offset);
    uintptr_t GetModuleBase() const;
    uintptr_t GetModuleSize() const;
    
    PatternScanner m_scanner;
    OffsetDatabase m_database;
    std::vector<OffsetInfo> m_offsets;
    
    bool m_isScanning;
    float m_scanProgress;
    std::string m_lastError;
    
    HMODULE m_gameModule;
    uintptr_t m_moduleBase;
    size_t m_moduleSize;
};
