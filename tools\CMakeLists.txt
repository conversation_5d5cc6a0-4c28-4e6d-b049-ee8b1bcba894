cmake_minimum_required(VERSION 3.16)
project(SimpleInjector)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Windows
if(WIN32)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Criar executável
add_executable(SimpleInjector simple_injector.cpp)

# Bibliotecas do Windows
target_link_libraries(SimpleInjector
    kernel32
    user32
    advapi32
)

# Configurações de compilação
if(MSVC)
    target_compile_options(SimpleInjector PRIVATE /W3)
    target_compile_definitions(SimpleInjector PRIVATE _CRT_SECURE_NO_WARNINGS)
endif()

# Configurações de saída
set_target_properties(SimpleInjector PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# Copiar executável para diretório raiz após build
add_custom_command(TARGET SimpleInjector POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:SimpleInjector> ${CMAKE_CURRENT_SOURCE_DIR}/../SimpleInjector.exe
    COMMENT "Copying injector to root directory"
)
