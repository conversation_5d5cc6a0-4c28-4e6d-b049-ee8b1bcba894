@echo off
echo Setting up Dear ImGui...

:: Create external directory
if not exist "external" mkdir external
cd external

:: Check if Im<PERSON><PERSON> already exists
if exist "imgui" (
    echo ImGui already exists. Updating...
    cd imgui
    git pull
    cd ..
) else (
    echo Cloning Dear ImGui...
    git clone https://github.com/ocornut/imgui.git
)

if %ERRORLEVEL% neq 0 (
    echo Failed to clone/update ImGui repository!
    echo You can manually download ImGui from: https://github.com/ocornut/imgui
    echo Extract it to: external/imgui/
    pause
    exit /b 1
)

echo Dear ImGui setup completed!
echo.
echo Required ImGui files:
echo - external/imgui/imgui.cpp
echo - external/imgui/imgui_demo.cpp  
echo - external/imgui/imgui_draw.cpp
echo - external/imgui/imgui_tables.cpp
echo - external/imgui/imgui_widgets.cpp
echo - external/imgui/backends/imgui_impl_win32.cpp
echo - external/imgui/backends/imgui_impl_dx11.cpp
echo.

cd ..
pause
