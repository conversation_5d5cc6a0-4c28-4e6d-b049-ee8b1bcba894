# 🎯 Exemplos de Hacks Possíveis com os Offsets

Com os offsets que nosso dumper encontra, você pode criar diversos tipos de hacks para FiveM. Aqui estão exemplos práticos:

## 🔍 ESP (Extra Sensory Perception)

### Player ESP
```cpp
#include "offsets.h"
#include <vector>
#include <Windows.h>

struct Vector3 {
    float x, y, z;
};

struct PlayerData {
    Vector3 position;
    float health;
    float armor;
    bool isValid;
};

class PlayerESP {
private:
    uintptr_t moduleBase;
    
public:
    PlayerESP() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    std::vector<PlayerData> GetAllPlayers() {
        std::vector<PlayerData> players;
        
        // Obter ponteiro para lista de jogadores
        uintptr_t playerListPtr = moduleBase + FiveMOffsets::Player::PlayerList;
        uintptr_t playerList = *reinterpret_cast<uintptr_t*>(playerListPtr);
        
        if (!playerList) return players;
        
        // Iterar pelos jogadores (assumindo máximo 32 players)
        for (int i = 0; i < 32; i++) {
            uintptr_t playerPtr = *reinterpret_cast<uintptr_t*>(playerList + (i * 8));
            if (!playerPtr) continue;
            
            PlayerData data;
            
            // Ler coordenadas
            uintptr_t coordsPtr = playerPtr + FiveMOffsets::Player::PlayerCoords;
            data.position = *reinterpret_cast<Vector3*>(coordsPtr);
            
            // Ler vida
            uintptr_t healthPtr = playerPtr + FiveMOffsets::Player::PlayerHealth;
            data.health = *reinterpret_cast<float*>(healthPtr);
            
            // Ler armadura
            uintptr_t armorPtr = playerPtr + FiveMOffsets::Player::PlayerArmor;
            data.armor = *reinterpret_cast<float*>(armorPtr);
            
            data.isValid = true;
            players.push_back(data);
        }
        
        return players;
    }
    
    Vector3 GetLocalPlayerPosition() {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t coordsPtr = localPlayer + FiveMOffsets::Player::PlayerCoords;
            return *reinterpret_cast<Vector3*>(coordsPtr);
        }
        
        return {0, 0, 0};
    }
};
```

### Vehicle ESP
```cpp
class VehicleESP {
private:
    uintptr_t moduleBase;
    
public:
    VehicleESP() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    struct VehicleData {
        Vector3 position;
        float speed;
        float health;
        bool isValid;
    };
    
    std::vector<VehicleData> GetAllVehicles() {
        std::vector<VehicleData> vehicles;
        
        // Obter pool de veículos
        uintptr_t vehiclePoolPtr = moduleBase + FiveMOffsets::Vehicle::VehiclePool;
        uintptr_t vehiclePool = *reinterpret_cast<uintptr_t*>(vehiclePoolPtr);
        
        if (!vehiclePool) return vehicles;
        
        // Iterar pelos veículos
        for (int i = 0; i < 300; i++) { // Máximo de veículos
            uintptr_t vehiclePtr = *reinterpret_cast<uintptr_t*>(vehiclePool + (i * 8));
            if (!vehiclePtr) continue;
            
            VehicleData data;
            
            // Coordenadas
            data.position = *reinterpret_cast<Vector3*>(vehiclePtr + FiveMOffsets::Vehicle::VehicleCoords);
            
            // Velocidade
            data.speed = *reinterpret_cast<float*>(vehiclePtr + FiveMOffsets::Vehicle::VehicleSpeed);
            
            // Vida
            data.health = *reinterpret_cast<float*>(vehiclePtr + FiveMOffsets::Vehicle::VehicleHealth);
            
            data.isValid = true;
            vehicles.push_back(data);
        }
        
        return vehicles;
    }
};
```

## 🎯 Aimbot

```cpp
class Aimbot {
private:
    uintptr_t moduleBase;
    
    float CalculateDistance(const Vector3& pos1, const Vector3& pos2) {
        float dx = pos1.x - pos2.x;
        float dy = pos1.y - pos2.y;
        float dz = pos1.z - pos2.z;
        return sqrt(dx*dx + dy*dy + dz*dz);
    }
    
public:
    Aimbot() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    Vector3 FindClosestEnemy() {
        Vector3 localPos = GetLocalPlayerPosition();
        Vector3 closestEnemy = {0, 0, 0};
        float closestDistance = 999999.0f;
        
        auto players = GetAllPlayers();
        
        for (const auto& player : players) {
            if (!player.isValid || player.health <= 0) continue;
            
            float distance = CalculateDistance(localPos, player.position);
            
            if (distance < closestDistance && distance > 1.0f) { // Não mirar em si mesmo
                closestDistance = distance;
                closestEnemy = player.position;
            }
        }
        
        return closestEnemy;
    }
    
    void AimAtTarget(const Vector3& target) {
        // Implementar lógica de mira aqui
        // Calcular ângulos e aplicar ao mouse/câmera
    }
};
```

## 🚗 Vehicle Hacks

```cpp
class VehicleHacks {
private:
    uintptr_t moduleBase;
    
public:
    VehicleHacks() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    void SetVehicleGravity(float gravity) {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            // Assumindo que o jogador está em um veículo
            uintptr_t vehiclePtr = *reinterpret_cast<uintptr_t*>(localPlayer + 0xD30); // Offset do veículo
            
            if (vehiclePtr) {
                uintptr_t gravityPtr = vehiclePtr + FiveMOffsets::Vehicle::VehicleGravity;
                *reinterpret_cast<float*>(gravityPtr) = gravity;
            }
        }
    }
    
    void SetVehicleSpeed(float speed) {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t vehiclePtr = *reinterpret_cast<uintptr_t*>(localPlayer + 0xD30);
            
            if (vehiclePtr) {
                uintptr_t speedPtr = vehiclePtr + FiveMOffsets::Vehicle::VehicleSpeed;
                *reinterpret_cast<float*>(speedPtr) = speed;
            }
        }
    }
    
    void RepairVehicle() {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t vehiclePtr = *reinterpret_cast<uintptr_t*>(localPlayer + 0xD30);
            
            if (vehiclePtr) {
                uintptr_t healthPtr = vehiclePtr + FiveMOffsets::Vehicle::VehicleHealth;
                *reinterpret_cast<float*>(healthPtr) = 1000.0f; // Vida máxima
            }
        }
    }
};
```

## 🛡️ Player Hacks

```cpp
class PlayerHacks {
private:
    uintptr_t moduleBase;
    
public:
    PlayerHacks() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    void SetGodMode(bool enabled) {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t healthPtr = localPlayer + FiveMOffsets::Player::PlayerHealth;
            
            if (enabled) {
                *reinterpret_cast<float*>(healthPtr) = 999999.0f;
            }
        }
    }
    
    void SetInfiniteArmor(bool enabled) {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t armorPtr = localPlayer + FiveMOffsets::Player::PlayerArmor;
            
            if (enabled) {
                *reinterpret_cast<float*>(armorPtr) = 999999.0f;
            }
        }
    }
    
    void TeleportTo(const Vector3& position) {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t coordsPtr = localPlayer + FiveMOffsets::Player::PlayerCoords;
            *reinterpret_cast<Vector3*>(coordsPtr) = position;
        }
    }
    
    Vector3 GetPosition() {
        uintptr_t localPlayerPtr = moduleBase + FiveMOffsets::Player::LocalPlayer;
        uintptr_t localPlayer = *reinterpret_cast<uintptr_t*>(localPlayerPtr);
        
        if (localPlayer) {
            uintptr_t coordsPtr = localPlayer + FiveMOffsets::Player::PlayerCoords;
            return *reinterpret_cast<Vector3*>(coordsPtr);
        }
        
        return {0, 0, 0};
    }
};
```

## 🔫 Weapon Hacks

```cpp
class WeaponHacks {
private:
    uintptr_t moduleBase;
    
public:
    WeaponHacks() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    void SetInfiniteAmmo(bool enabled) {
        uintptr_t weaponManagerPtr = moduleBase + FiveMOffsets::Weapon::WeaponManager;
        uintptr_t weaponManager = *reinterpret_cast<uintptr_t*>(weaponManagerPtr);
        
        if (weaponManager) {
            uintptr_t currentWeaponPtr = weaponManager + FiveMOffsets::Weapon::CurrentWeapon;
            uintptr_t currentWeapon = *reinterpret_cast<uintptr_t*>(currentWeaponPtr);
            
            if (currentWeapon && enabled) {
                uintptr_t ammoPtr = currentWeapon + FiveMOffsets::Weapon::WeaponAmmo;
                *reinterpret_cast<int*>(ammoPtr) = 999999;
            }
        }
    }
    
    void SetDamageMultiplier(float multiplier) {
        uintptr_t weaponManagerPtr = moduleBase + FiveMOffsets::Weapon::WeaponManager;
        uintptr_t weaponManager = *reinterpret_cast<uintptr_t*>(weaponManagerPtr);
        
        if (weaponManager) {
            uintptr_t currentWeaponPtr = weaponManager + FiveMOffsets::Weapon::CurrentWeapon;
            uintptr_t currentWeapon = *reinterpret_cast<uintptr_t*>(currentWeaponPtr);
            
            if (currentWeapon) {
                uintptr_t damagePtr = currentWeapon + FiveMOffsets::Weapon::WeaponDamage;
                *reinterpret_cast<float*>(damagePtr) = multiplier;
            }
        }
    }
};
```

## 🌍 World Hacks

```cpp
class WorldHacks {
private:
    uintptr_t moduleBase;
    
public:
    WorldHacks() {
        moduleBase = reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr));
    }
    
    void SetTimeScale(float scale) {
        uintptr_t timeScalePtr = moduleBase + FiveMOffsets::World::TimeScale;
        *reinterpret_cast<float*>(timeScalePtr) = scale;
    }
    
    void SetWeather(int weatherType) {
        uintptr_t weatherPtr = moduleBase + FiveMOffsets::World::Weather;
        *reinterpret_cast<int*>(weatherPtr) = weatherType;
    }
    
    int GetPlayerCount() {
        uintptr_t playerCountPtr = moduleBase + FiveMOffsets::Network::PlayerCount;
        return *reinterpret_cast<int*>(playerCountPtr);
    }
};
```

## 🎮 Exemplo de Menu Principal

```cpp
class CheatMenu {
private:
    PlayerHacks playerHacks;
    VehicleHacks vehicleHacks;
    WeaponHacks weaponHacks;
    WorldHacks worldHacks;
    PlayerESP playerESP;
    VehicleESP vehicleESP;
    
    bool showMenu = false;
    bool godMode = false;
    bool infiniteAmmo = false;
    bool vehicleGodMode = false;
    
public:
    void Update() {
        // Toggle menu com INSERT
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            showMenu = !showMenu;
        }
        
        if (!showMenu) return;
        
        // Renderizar menu ImGui
        if (ImGui::Begin("FiveM Cheat Menu")) {
            
            if (ImGui::CollapsingHeader("Player Hacks")) {
                if (ImGui::Checkbox("God Mode", &godMode)) {
                    playerHacks.SetGodMode(godMode);
                }
                
                if (ImGui::Button("Heal")) {
                    playerHacks.SetGodMode(false);
                    playerHacks.SetGodMode(true);
                }
                
                static float teleportPos[3] = {0, 0, 0};
                ImGui::InputFloat3("Teleport Position", teleportPos);
                if (ImGui::Button("Teleport")) {
                    playerHacks.TeleportTo({teleportPos[0], teleportPos[1], teleportPos[2]});
                }
            }
            
            if (ImGui::CollapsingHeader("Vehicle Hacks")) {
                if (ImGui::Button("Repair Vehicle")) {
                    vehicleHacks.RepairVehicle();
                }
                
                static float gravity = 9.8f;
                ImGui::SliderFloat("Gravity", &gravity, -50.0f, 50.0f);
                if (ImGui::Button("Set Gravity")) {
                    vehicleHacks.SetVehicleGravity(gravity);
                }
            }
            
            if (ImGui::CollapsingHeader("Weapon Hacks")) {
                if (ImGui::Checkbox("Infinite Ammo", &infiniteAmmo)) {
                    weaponHacks.SetInfiniteAmmo(infiniteAmmo);
                }
                
                static float damage = 1.0f;
                ImGui::SliderFloat("Damage Multiplier", &damage, 0.1f, 10.0f);
                if (ImGui::Button("Set Damage")) {
                    weaponHacks.SetDamageMultiplier(damage);
                }
            }
            
            if (ImGui::CollapsingHeader("ESP")) {
                auto players = playerESP.GetAllPlayers();
                ImGui::Text("Players Found: %d", (int)players.size());
                
                for (size_t i = 0; i < players.size(); i++) {
                    if (players[i].isValid) {
                        ImGui::Text("Player %d: (%.1f, %.1f, %.1f) HP: %.1f", 
                                   (int)i, players[i].position.x, players[i].position.y, 
                                   players[i].position.z, players[i].health);
                    }
                }
            }
            
            if (ImGui::CollapsingHeader("World")) {
                static float timeScale = 1.0f;
                ImGui::SliderFloat("Time Scale", &timeScale, 0.1f, 5.0f);
                if (ImGui::Button("Set Time Scale")) {
                    worldHacks.SetTimeScale(timeScale);
                }
                
                ImGui::Text("Player Count: %d", worldHacks.GetPlayerCount());
            }
        }
        ImGui::End();
    }
};
```

## ⚠️ Avisos Importantes

1. **Detecção**: Estes hacks podem ser detectados por anti-cheats
2. **Banimento**: Uso pode resultar em banimento permanente
3. **Ética**: Use apenas para fins educacionais ou servidores próprios
4. **Estabilidade**: Sempre verifique se os ponteiros são válidos antes de usar
5. **Atualizações**: Offsets podem mudar com updates do FiveM

## 🔧 Como Implementar

1. Use o offset dumper para obter os offsets atuais
2. Exporte para C++ header
3. Inclua o header em seu projeto de cheat
4. Implemente as classes acima
5. Compile e injete no FiveM

Com esses offsets, você tem acesso a praticamente todos os aspectos do jogo para criar cheats avançados!
