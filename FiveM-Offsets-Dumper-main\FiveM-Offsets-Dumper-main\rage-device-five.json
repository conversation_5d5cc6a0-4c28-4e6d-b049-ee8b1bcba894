{"rage-device-five.dll": {"AllocateBuffer": {"offset": "0x3AFE0"}, "CRYPTO_memcmp": {"offset": "0x12A0"}, "CallInitialMount": {"offset": "0x29C70"}, "CfxState::CfxState": {"offset": "0x21A10"}, "Component::As": {"offset": "0x14A90"}, "Component::IsA": {"offset": "0x15350"}, "Component::SetCommandLine": {"offset": "0x10920"}, "Component::SetUserData": {"offset": "0x28D60"}, "ComponentInstance::DoGameLoad": {"offset": "0x28D40"}, "ComponentInstance::Initialize": {"offset": "0x28D50"}, "ComponentInstance::Shutdown": {"offset": "0x28D60"}, "CoreGetComponentRegistry": {"offset": "0x25DD0"}, "CoreGetGlobalInstanceRegistry": {"offset": "0x11BE0"}, "CorruptedErrorCodes": {"offset": "0x29CD0"}, "CreateComponent": {"offset": "0x28D70"}, "CreateFileWDummy": {"offset": "0x29FB0"}, "CreateTrampolineFunction": {"offset": "0x3B330"}, "DllMain": {"offset": "0x3DD1C"}, "DoNtRaiseException": {"offset": "0x38600"}, "EnableAllHooksLL": {"offset": "0x3A540"}, "EnableHook": {"offset": "0x3A690"}, "EnableHookLL": {"offset": "0x3A7B0"}, "FatalErrorNoExceptRealV": {"offset": "0x11FF0"}, "FatalErrorRealV": {"offset": "0x12020"}, "FilterDevice::Close": {"offset": "0x2CEA0"}, "FilterDevice::Create": {"offset": "0x2CEB0"}, "FilterDevice::CreateLocal": {"offset": "0x2CEC0"}, "FilterDevice::FilterDevice": {"offset": "0x2BE60"}, "FilterDevice::GetFileAttributesW": {"offset": "0x2CF70"}, "FilterDevice::GetFileLengthLong": {"offset": "0x2D030"}, "FilterDevice::GetFileTime": {"offset": "0x2D0E0"}, "FilterDevice::GetName": {"offset": "0x2D190"}, "FilterDevice::GetResourceVersion": {"offset": "0x2D240"}, "FilterDevice::IsApproved": {"offset": "0x2D300"}, "FilterDevice::Open": {"offset": "0x2D590"}, "FilterDevice::OpenBulk": {"offset": "0x2D660"}, "FilterDevice::Read": {"offset": "0x2D960"}, "FilterDevice::ReplaceRoot": {"offset": "0x2D970"}, "FilterDevice::Seek": {"offset": "0x2DAB0"}, "FilterDevice::SeekLong": {"offset": "0x2DAC0"}, "FilterDevice::SetFileTime": {"offset": "0x15350"}, "FilterDevice::Write": {"offset": "0x2DAD0"}, "FilterDevice::m_xy": {"offset": "0x2DB50"}, "FilterDevice::m_yx": {"offset": "0x2DC10"}, "FormatErrorPickup<std::tuple<std::basic_string_view<char,std::char_traits<char> >,int,unsigned int> >": {"offset": "0x8750"}, "FreeBuffer": {"offset": "0x3B010"}, "Freeze": {"offset": "0x3A8C0"}, "GetAbsoluteCitPath": {"offset": "0x36790"}, "GetAbsoluteGamePath": {"offset": "0x36A90"}, "GetMemoryBlock": {"offset": "0x3B090"}, "GlobalErrorHandler": {"offset": "0x12260"}, "HookFunction::Run": {"offset": "0x27CA0"}, "HookFunctionBase::Register": {"offset": "0x398F0"}, "HookFunctionBase::RunAll": {"offset": "0x39910"}, "HostSharedData<CfxState>::HostSharedData<CfxState>": {"offset": "0x20F30"}, "HostSharedData<CfxState>::~HostSharedData<CfxState>": {"offset": "0x35FB0"}, "InitFunction::Run": {"offset": "0x149C0"}, "InitFunctionBase::InitFunctionBase": {"offset": "0x383A0"}, "InitFunctionBase::Register": {"offset": "0x38770"}, "InitFunctionBase::RunAll": {"offset": "0x387C0"}, "InitializeBuffer": {"offset": "0x10920"}, "IsCodePadding": {"offset": "0x3B6E0"}, "IsExecutableAddress": {"offset": "0x3B2F0"}, "MH_CreateHook": {"offset": "0x3AA70"}, "MH_EnableHook": {"offset": "0x3AD00"}, "MH_Initialize": {"offset": "0x3AD10"}, "MakeRelativeCitPath": {"offset": "0x12900"}, "MakeRelativeGamePath": {"offset": "0x2D430"}, "OPENSSL_atomic_add": {"offset": "0x1000"}, "OPENSSL_cleanse": {"offset": "0x1240"}, "OPENSSL_cpuid_setup": {"offset": "0x3C380"}, "OPENSSL_ia32_cpuid": {"offset": "0x1030"}, "OPENSSL_ia32_rdrand_bytes": {"offset": "0x13F0"}, "OPENSSL_ia32_rdseed_bytes": {"offset": "0x1450"}, "OPENSSL_instrument_bus": {"offset": "0x1330"}, "OPENSSL_instrument_bus2": {"offset": "0x1380"}, "OPENSSL_rdtsc": {"offset": "0x1020"}, "OPENSSL_wipe_cpu": {"offset": "0x12F0"}, "OpenArchiveWrap": {"offset": "0x2A070"}, "OpenArchiveWrapInner": {"offset": "0x2A370"}, "OpenArchiveWrapSeh": {"offset": "0x28DC0"}, "PackfileEncryptionError": {"offset": "0x2A440"}, "PackfileOpen": {"offset": "0x2B8A0"}, "PathFilteringDevice::FilterFile": {"offset": "0x25E60"}, "PathFilteringDevice::GetAttributes": {"offset": "0x15480"}, "PathFilteringDevice::Open": {"offset": "0x15420"}, "ProcessThreadIPs": {"offset": "0x3ADB0"}, "RaiseDebugException": {"offset": "0x386E0"}, "ReMountDefaultDevice": {"offset": "0x2D730"}, "ResetPackfile": {"offset": "0x2AC10"}, "SHA256_Final": {"offset": "0x3BEE0"}, "SHA256_Init": {"offset": "0x3C200"}, "SHA256_Update": {"offset": "0x3C260"}, "ScopedError::~ScopedError": {"offset": "0x10B10"}, "SetPackfileValidationRoutine": {"offset": "0x2B980"}, "SysError": {"offset": "0x12E80"}, "ToNarrow": {"offset": "0x387F0"}, "ToWide": {"offset": "0x388E0"}, "TraceRealV": {"offset": "0x38BF0"}, "Unfreeze": {"offset": "0x3AF60"}, "Win32TrapAndJump64": {"offset": "0x3A520"}, "WrapPackfile": {"offset": "0x2B200"}, "_DllMainCRTStartup": {"offset": "0x3D760"}, "_Init_thread_abort": {"offset": "0x3C9DC"}, "_Init_thread_footer": {"offset": "0x3CA0C"}, "_Init_thread_header": {"offset": "0x3CA6C"}, "_Init_thread_notify": {"offset": "0x3CAD4"}, "_Init_thread_wait": {"offset": "0x3CB18"}, "_RTC_Initialize": {"offset": "0x3DD6C"}, "_RTC_Terminate": {"offset": "0x3DDA8"}, "__ArrayUnwind": {"offset": "0x3D2E4"}, "__GSHandlerCheck": {"offset": "0x3D0F8"}, "__GSHandlerCheckCommon": {"offset": "0x3D118"}, "__GSHandlerCheck_EH": {"offset": "0x3D174"}, "__GSHandlerCheck_SEH": {"offset": "0x3D3B0"}, "__chkstk": {"offset": "0x3D360"}, "__crt_debugger_hook": {"offset": "0x3DAC8"}, "__dyn_tls_init": {"offset": "0x3CF40"}, "__dyn_tls_on_demand_init": {"offset": "0x3CFA8"}, "__isa_available_init": {"offset": "0x3D91C"}, "__local_stdio_printf_options": {"offset": "0x148E0"}, "__local_stdio_scanf_options": {"offset": "0x3DD40"}, "__raise_securityfailure": {"offset": "0x3D7A0"}, "__report_gsfailure": {"offset": "0x3D7D4"}, "__scrt_acquire_startup_lock": {"offset": "0x3CBC0"}, "__scrt_dllmain_after_initialize_c": {"offset": "0x3CBFC"}, "__scrt_dllmain_before_initialize_c": {"offset": "0x3CC30"}, "__scrt_dllmain_crt_thread_attach": {"offset": "0x3CC48"}, "__scrt_dllmain_crt_thread_detach": {"offset": "0x3CC70"}, "__scrt_dllmain_exception_filter": {"offset": "0x3CC88"}, "__scrt_dllmain_uninitialize_c": {"offset": "0x3CCE8"}, "__scrt_dllmain_uninitialize_critical": {"offset": "0x3CD18"}, "__scrt_fastfail": {"offset": "0x3DAD0"}, "__scrt_get_dyn_tls_init_callback": {"offset": "0x3DD64"}, "__scrt_initialize_crt": {"offset": "0x3CD2C"}, "__scrt_initialize_default_local_stdio_options": {"offset": "0x3DD48"}, "__scrt_initialize_onexit_tables": {"offset": "0x3CD78"}, "__scrt_initialize_thread_safe_statics": {"offset": "0x3C8E4"}, "__scrt_initialize_type_info": {"offset": "0x3D25C"}, "__scrt_is_nonwritable_in_current_image": {"offset": "0x3CE04"}, "__scrt_is_nonwritable_in_current_image$filt$0": {"offset": "0x3F7BC"}, "__scrt_is_ucrt_dll_in_use": {"offset": "0x3DC64"}, "__scrt_release_startup_lock": {"offset": "0x3CE9C"}, "__scrt_stub_for_acrt_initialize": {"offset": "0x28D60"}, "__scrt_stub_for_acrt_thread_attach": {"offset": "0x28D60"}, "__scrt_stub_for_acrt_thread_detach": {"offset": "0x28D60"}, "__scrt_stub_for_acrt_uninitialize": {"offset": "0x28D60"}, "__scrt_stub_for_acrt_uninitialize_critical": {"offset": "0x28D60"}, "__scrt_stub_for_is_c_termination_complete": {"offset": "0x14A90"}, "__scrt_throw_std_bad_alloc": {"offset": "0x3DC3C"}, "__scrt_throw_std_bad_array_new_length": {"offset": "0x13680"}, "__scrt_uninitialize_crt": {"offset": "0x3CEC0"}, "__scrt_uninitialize_thread_safe_statics": {"offset": "0x3C9B4"}, "__scrt_uninitialize_type_info": {"offset": "0x3D26C"}, "__security_check_cookie": {"offset": "0x3D210"}, "__security_init_cookie": {"offset": "0x3DC70"}, "__std_find_trivial_1": {"offset": "0x3C6A0"}, "__std_find_trivial_2": {"offset": "0x3C770"}, "_get_startup_argv_mode": {"offset": "0x3DC5C"}, "_guard_check_icall_nop": {"offset": "0x10920"}, "_guard_dispatch_icall_nop": {"offset": "0x3DF20"}, "_guard_xfg_dispatch_icall_nop": {"offset": "0x3DF40"}, "_onexit": {"offset": "0x3CEEC"}, "_wwassert": {"offset": "0x36E40"}, "`OpenArchiveWrapSeh'::`1'::filt$0": {"offset": "0x3ED50"}, "`__ArrayUnwind'::`1'::filt$0": {"offset": "0x3F800"}, "`dllmain_crt_process_attach'::`1'::fin$0": {"offset": "0x3F85F"}, "`dllmain_crt_process_detach'::`1'::fin$0": {"offset": "0x3F876"}, "`dllmain_crt_process_detach'::`1'::fin$1": {"offset": "0x3F88F"}, "`dllmain_dispatch'::`1'::filt$0": {"offset": "0x3F8A3"}, "`dynamic initializer for '_init_instance_0''": {"offset": "0x6290"}, "`dynamic initializer for '_init_instance_22''": {"offset": "0x64B0"}, "`dynamic initializer for 'currentPack''": {"offset": "0x6BF0"}, "`dynamic initializer for 'g_basePaths''": {"offset": "0x64E0"}, "`dynamic initializer for 'g_thisError''": {"offset": "0x62C0"}, "`dynamic initializer for 'hookFunction''": {"offset": "0x6C30"}, "`dynamic initializer for 'initFunction''": {"offset": "0x6840"}, "`dynamic initializer for 'initHookingFunction''": {"offset": "0x7E10"}, "`dynamic initializer for 'initIoBuf''": {"offset": "0x7C40"}, "`dynamic initializer for 'originalMount''": {"offset": "0x6DF0"}, "`dynamic initializer for 'rage::fiDevice::OnInitialMount''": {"offset": "0x7470"}, "`dynamic initializer for 'xbr::virt::Base<1,2802,2,6>::ms_initFunction''": {"offset": "0x6470"}, "atexit": {"offset": "0x3CF28"}, "boost::algorithm::detail::find_format_all_impl2<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,boost::algorithm::detail::first_finderF<char const *,boost::algorithm::is_equal>,boost::algorithm::detail::const_formatF<boost::iterator_range<char const *> >,boost::iterator_range<std::_String_iterator<std::_String_val<std::_Simple_types<char> > > >,boost::iterator_range<char const *> >": {"offset": "0x19CA0"}, "boost::algorithm::hex_decode_error::hex_decode_error": {"offset": "0x16720"}, "boost::algorithm::non_hex_input::non_hex_input": {"offset": "0x21E60"}, "boost::algorithm::not_enough_input::not_enough_input": {"offset": "0x21F00"}, "boost::algorithm::unhex<std::_String_view_iterator<std::char_traits<char> >,std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x1B080"}, "boost::checked_delete<boost::error_info<boost::algorithm::bad_char_,char> >": {"offset": "0x19530"}, "boost::checked_delete<boost::exception_detail::error_info_base>": {"offset": "0x19530"}, "boost::detail::shared_count::shared_count<boost::error_info<boost::algorithm::bad_char_,char> >": {"offset": "0x16C10"}, "boost::detail::shared_count::shared_count<boost::exception_detail::error_info_base>": {"offset": "0x16C80"}, "boost::detail::shared_count::~shared_count": {"offset": "0x22880"}, "boost::detail::sp_counted_base::destroy": {"offset": "0x14EA0"}, "boost::detail::sp_counted_impl_p<boost::error_info<boost::algorithm::bad_char_,char> >::dispose": {"offset": "0x14A70"}, "boost::detail::sp_counted_impl_p<boost::error_info<boost::algorithm::bad_char_,char> >::get_deleter": {"offset": "0x14A90"}, "boost::detail::sp_counted_impl_p<boost::error_info<boost::algorithm::bad_char_,char> >::get_local_deleter": {"offset": "0x14A90"}, "boost::detail::sp_counted_impl_p<boost::error_info<boost::algorithm::bad_char_,char> >::get_untyped_deleter": {"offset": "0x14A90"}, "boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::dispose": {"offset": "0x14A70"}, "boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_deleter": {"offset": "0x14A90"}, "boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_local_deleter": {"offset": "0x14A90"}, "boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_untyped_deleter": {"offset": "0x14A90"}, "boost::detail::sp_enable_shared_from_this": {"offset": "0x27AC0"}, "boost::error_info<boost::algorithm::bad_char_,char>::clone": {"offset": "0x14DE0"}, "boost::error_info<boost::algorithm::bad_char_,char>::error_info<boost::algorithm::bad_char_,char>": {"offset": "0x215A0"}, "boost::error_info<boost::algorithm::bad_char_,char>::name_value_string": {"offset": "0x14E20"}, "boost::error_info<boost::algorithm::bad_char_,char>::~error_info<boost::algorithm::bad_char_,char>": {"offset": "0x22470"}, "boost::exception::exception": {"offset": "0x149D0"}, "boost::exception::~exception": {"offset": "0x227B0"}, "boost::exception_detail::clone_base::clone_base": {"offset": "0x14A30"}, "boost::exception_detail::clone_base::~clone_base": {"offset": "0x22770"}, "boost::exception_detail::copy_boost_exception": {"offset": "0x27000"}, "boost::exception_detail::error_info_container::~error_info_container": {"offset": "0x227A0"}, "boost::exception_detail::error_info_container_impl::add_ref": {"offset": "0x15B30"}, "boost::exception_detail::error_info_container_impl::clone": {"offset": "0x15C00"}, "boost::exception_detail::error_info_container_impl::diagnostic_information": {"offset": "0x15750"}, "boost::exception_detail::error_info_container_impl::error_info_container_impl": {"offset": "0x21B00"}, "boost::exception_detail::error_info_container_impl::get": {"offset": "0x156C0"}, "boost::exception_detail::error_info_container_impl::release": {"offset": "0x15B40"}, "boost::exception_detail::error_info_container_impl::set": {"offset": "0x15540"}, "boost::exception_detail::refcount_ptr<boost::exception_detail::error_info_container>::~refcount_ptr<boost::exception_detail::error_info_container>": {"offset": "0x22540"}, "boost::exception_detail::set_info_rv<boost::error_info<boost::algorithm::bad_char_,char> >::set<boost::algorithm::non_hex_input>": {"offset": "0x1AA40"}, "boost::shared_ptr<boost::error_info<boost::algorithm::bad_char_,char> >::~shared_ptr<boost::error_info<boost::algorithm::bad_char_,char> >": {"offset": "0x22580"}, "boost::shared_ptr<boost::exception_detail::error_info_base>::~shared_ptr<boost::exception_detail::error_info_base>": {"offset": "0x22580"}, "boost::source_location::source_location": {"offset": "0x21FF0"}, "boost::throw_exception<boost::algorithm::non_hex_input>": {"offset": "0x1AC10"}, "boost::throw_exception<boost::algorithm::not_enough_input>": {"offset": "0x1AC40"}, "boost::to_string<boost::algorithm::bad_char_,char>": {"offset": "0x1AC70"}, "boost::wrapexcept<boost::algorithm::non_hex_input>::clone": {"offset": "0x164F0"}, "boost::wrapexcept<boost::algorithm::non_hex_input>::deleter::~deleter": {"offset": "0x22780"}, "boost::wrapexcept<boost::algorithm::non_hex_input>::rethrow": {"offset": "0x16590"}, "boost::wrapexcept<boost::algorithm::non_hex_input>::wrapexcept<boost::algorithm::non_hex_input>": {"offset": "0x215C0"}, "boost::wrapexcept<boost::algorithm::not_enough_input>::clone": {"offset": "0x14AD0"}, "boost::wrapexcept<boost::algorithm::not_enough_input>::deleter::~deleter": {"offset": "0x22780"}, "boost::wrapexcept<boost::algorithm::not_enough_input>::rethrow": {"offset": "0x14B70"}, "boost::wrapexcept<boost::algorithm::not_enough_input>::wrapexcept<boost::algorithm::not_enough_input>": {"offset": "0x21740"}, "capture_previous_context": {"offset": "0x3D8A8"}, "dllmain_crt_dispatch": {"offset": "0x3D440"}, "dllmain_crt_process_attach": {"offset": "0x3D490"}, "dllmain_crt_process_detach": {"offset": "0x3D5A8"}, "dllmain_dispatch": {"offset": "0x3D62C"}, "fiDeviceRelative_GetLength": {"offset": "0x2B850"}, "fiDeviceRelative_GetLengthInt64": {"offset": "0x2B860"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::grow": {"offset": "0x13CC0"}, "fmt::v8::basic_memory_buffer<char,500,std::allocator<char> >::~basic_memory_buffer<char,500,std::allocator<char> >": {"offset": "0x109E0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::grow": {"offset": "0x357D0"}, "fmt::v8::basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >::~basic_memory_buffer<unsigned int,32,std::allocator<unsigned int> >": {"offset": "0x34B70"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::grow": {"offset": "0x15A10"}, "fmt::v8::basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >::~basic_memory_buffer<wchar_t,500,std::allocator<wchar_t> >": {"offset": "0x22270"}, "fmt::v8::detail::add_compare": {"offset": "0x34F40"}, "fmt::v8::detail::assert_fail": {"offset": "0x35080"}, "fmt::v8::detail::bigint::assign_pow10": {"offset": "0x350D0"}, "fmt::v8::detail::bigint::divmod_assign": {"offset": "0x352A0"}, "fmt::v8::detail::bigint::square": {"offset": "0x35BA0"}, "fmt::v8::detail::bigint::~bigint": {"offset": "0x34B70"}, "fmt::v8::detail::buffer<char>::append<char>": {"offset": "0x91F0"}, "fmt::v8::detail::buffer<char>::try_resize": {"offset": "0x35E60"}, "fmt::v8::detail::check_int_type_spec<fmt::v8::detail::error_handler &>": {"offset": "0x19470"}, "fmt::v8::detail::compare": {"offset": "0x35200"}, "fmt::v8::detail::copy_str<wchar_t,char const *,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x19A40"}, "fmt::v8::detail::count_digits": {"offset": "0x13AA0"}, "fmt::v8::detail::decimal_point_impl<char>": {"offset": "0x31410"}, "fmt::v8::detail::decimal_point_impl<wchar_t>": {"offset": "0x31520"}, "fmt::v8::detail::do_throw<fmt::v8::format_error>": {"offset": "0x92A0"}, "fmt::v8::detail::dragonbox::cache_accessor<double>::get_cached_power": {"offset": "0x356A0"}, "fmt::v8::detail::dragonbox::is_endpoint_integer<double>": {"offset": "0x33390"}, "fmt::v8::detail::dragonbox::remove_trailing_zeros": {"offset": "0x35980"}, "fmt::v8::detail::dragonbox::shorter_interval_case<float>": {"offset": "0x333C0"}, "fmt::v8::detail::dragonbox::to_decimal<double>": {"offset": "0x34190"}, "fmt::v8::detail::dragonbox::to_decimal<float>": {"offset": "0x33D60"}, "fmt::v8::detail::error_handler::on_error": {"offset": "0x35900"}, "fmt::v8::detail::fallback_format<double>": {"offset": "0x315D0"}, "fmt::v8::detail::fallback_format<long double>": {"offset": "0x315D0"}, "fmt::v8::detail::fill<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x19B60"}, "fmt::v8::detail::fill_n<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x19C20"}, "fmt::v8::detail::fmt_snprintf": {"offset": "0x35630"}, "fmt::v8::detail::for_each_codepoint<`fmt::v8::detail::compute_width'::`2'::count_code_points>": {"offset": "0x92D0"}, "fmt::v8::detail::format_decimal<char,unsigned __int64,fmt::v8::appender,0>": {"offset": "0x32A80"}, "fmt::v8::detail::format_decimal<char,unsigned __int64>": {"offset": "0x95A0"}, "fmt::v8::detail::format_decimal<char,unsigned int,fmt::v8::appender,0>": {"offset": "0x32960"}, "fmt::v8::detail::format_float<double>": {"offset": "0x30F20"}, "fmt::v8::detail::format_float<long double>": {"offset": "0x32BC0"}, "fmt::v8::detail::format_uint<4,char,fmt::v8::appender,unsigned __int64>": {"offset": "0x9680"}, "fmt::v8::detail::format_uint<4,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x1A130"}, "fmt::v8::detail::grisu_gen_digits<fmt::v8::detail::fixed_handler>": {"offset": "0x32F20"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,char>": {"offset": "0x97A0"}, "fmt::v8::detail::parse_float_type_spec<fmt::v8::detail::error_handler,wchar_t>": {"offset": "0x97A0"}, "fmt::v8::detail::parse_header<char,<lambda_10b59f138e704e35b5296527d0235f76> >": {"offset": "0x9900"}, "fmt::v8::detail::parse_header<wchar_t,<lambda_9f149cc28ea8d29dc23928a98d99e4aa> >": {"offset": "0x1A4A0"}, "fmt::v8::detail::parse_nonnegative_int<char>": {"offset": "0x9B60"}, "fmt::v8::detail::parse_nonnegative_int<wchar_t>": {"offset": "0x1A720"}, "fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>::write_null_pointer": {"offset": "0x14830"}, "fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>::write_null_pointer": {"offset": "0x27BF0"}, "fmt::v8::detail::snprintf_float<double>": {"offset": "0x335D0"}, "fmt::v8::detail::snprintf_float<long double>": {"offset": "0x33850"}, "fmt::v8::detail::thousands_sep_impl<char>": {"offset": "0x33AE0"}, "fmt::v8::detail::thousands_sep_impl<wchar_t>": {"offset": "0x33C50"}, "fmt::v8::detail::thousands_sep_result<char>::~thousands_sep_result<char>": {"offset": "0x10A40"}, "fmt::v8::detail::thousands_sep_result<wchar_t>::~thousands_sep_result<wchar_t>": {"offset": "0x10A40"}, "fmt::v8::detail::to_unsigned<int>": {"offset": "0x9C30"}, "fmt::v8::detail::utf8_decode": {"offset": "0x14670"}, "fmt::v8::detail::vprintf<char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xB230"}, "fmt::v8::detail::vprintf<wchar_t,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1C640"}, "fmt::v8::detail::write<char,fmt::v8::appender,double,0>": {"offset": "0xC230"}, "fmt::v8::detail::write<char,fmt::v8::appender,float,0>": {"offset": "0xBDE0"}, "fmt::v8::detail::write<char,fmt::v8::appender,long double,0>": {"offset": "0xC670"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned __int64,0>": {"offset": "0x348E0"}, "fmt::v8::detail::write<char,fmt::v8::appender,unsigned int,0>": {"offset": "0x347C0"}, "fmt::v8::detail::write<char,fmt::v8::appender>": {"offset": "0xBD10"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1D3A0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,double,0>": {"offset": "0x1D8C0"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,float,0>": {"offset": "0x1D470"}, "fmt::v8::detail::write<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,long double,0>": {"offset": "0x1DD00"}, "fmt::v8::detail::write_bytes<1,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1E140"}, "fmt::v8::detail::write_bytes<2,wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1E280"}, "fmt::v8::detail::write_char<char,fmt::v8::appender>": {"offset": "0xCAB0"}, "fmt::v8::detail::write_char<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1E3C0"}, "fmt::v8::detail::write_exponent<char,fmt::v8::appender>": {"offset": "0xCAF0"}, "fmt::v8::detail::write_exponent<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> > >": {"offset": "0x1E4B0"}, "fmt::v8::detail::write_float<fmt::v8::appender,fmt::v8::detail::big_decimal_fp,char>": {"offset": "0xCC80"}, "fmt::v8::detail::write_float<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,fmt::v8::detail::big_decimal_fp,wchar_t>": {"offset": "0x1E660"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned __int64>": {"offset": "0xD640"}, "fmt::v8::detail::write_int<char,fmt::v8::appender,unsigned int>": {"offset": "0xD050"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x1F190"}, "fmt::v8::detail::write_int<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned int>": {"offset": "0x1EB70"}, "fmt::v8::detail::write_int_data<char>::write_int_data<char>": {"offset": "0x105D0"}, "fmt::v8::detail::write_int_data<wchar_t>::write_int_data<wchar_t>": {"offset": "0x105D0"}, "fmt::v8::detail::write_int_localized<fmt::v8::appender,unsigned __int64,char>": {"offset": "0xDD70"}, "fmt::v8::detail::write_int_localized<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64,wchar_t>": {"offset": "0x1F7B0"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_051aec1ce4ed0271119623a7e83ac6a5> &>": {"offset": "0xE190"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_22cb150d85fca5a3ac6a96f517638e39> &>": {"offset": "0xE360"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_69652fb90da4a77ba8cf5fef5ea19cdd> >": {"offset": "0xE500"}, "fmt::v8::detail::write_padded<1,fmt::v8::appender,char,<lambda_fbd162939e03e0c955beb05a33c24f5e> &>": {"offset": "0xE500"}, "fmt::v8::detail::write_padded<1,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_2d1811023212e86d2b251f7d81404f23> &>": {"offset": "0x1FC40"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_02a1634acea1c2868026d163185cc0d5> &>": {"offset": "0xE690"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06b2adfe852a46c8d65fe26cc7c8ca5d> &>": {"offset": "0xE8B0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_06f64a48a5b4f184684d74740837f1c7> &>": {"offset": "0xEA30"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_2d30ec7f416bbcee3bc8a87df0c22c03> >": {"offset": "0x101C0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_33ba7a720adff5c90e1946d777cb5230> &>": {"offset": "0xEBC0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_4ea56d52f8212d84933fa76e5a159b3f> &>": {"offset": "0xEDE0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_514469ad4ff2f796c2a70ef6994dff64> &>": {"offset": "0xF080"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_596cc5dacf733e82dcb56ca6ee54b81f> &>": {"offset": "0xF2A0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_686459aaae64d4cbdde6763d637485ac> &>": {"offset": "0xF420"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_694eb9c30a49e7dae164c077f09b5a7f> &>": {"offset": "0xF640"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_8cce229a1891be08a4c6e75623ca3fd6> &>": {"offset": "0xF860"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_b9c069f91a736dafbdb5ba8344e9f4c8> &>": {"offset": "0xF9E0"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_cb4d4b26b782de201d2a0edc777aea57> &>": {"offset": "0xFC00"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_de0fdbc03b7ebc9cd9291e836878da40> &>": {"offset": "0xFD80"}, "fmt::v8::detail::write_padded<2,fmt::v8::appender,char,<lambda_f811a0db88237a09377d34dce98c0f31> &>": {"offset": "0xFFA0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1cbc5ef94e0da3c5d5dd40b631e0f419> &>": {"offset": "0x1FDC0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_1e1b2d68aea4f6de5c63992fd3822582> &>": {"offset": "0x1FF60"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_33daaad348c2531f94f8e1b1abe8ee99> &>": {"offset": "0x20100"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_52a8b5dd947b5cef974142c59166c05a> &>": {"offset": "0x20290"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_763961065246f6495121c1e2aec344a8> &>": {"offset": "0x20430"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_b202e369ba43dfec7f2889d27268a32c> &>": {"offset": "0x205D0"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_d4b206abad762a6e1fb6da24cad44b9d> &>": {"offset": "0x20770"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_e2af9d0326dec73bc6415570a60a8ec4> &>": {"offset": "0x20920"}, "fmt::v8::detail::write_padded<2,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t,<lambda_faccbf2d03fcb51dc39f5a676348a8a1> &>": {"offset": "0x20AC0"}, "fmt::v8::detail::write_ptr<char,fmt::v8::appender,unsigned __int64>": {"offset": "0x10350"}, "fmt::v8::detail::write_ptr<wchar_t,std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,unsigned __int64>": {"offset": "0x20C60"}, "fmt::v8::detail::write_significand<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>": {"offset": "0x20E50"}, "fmt::v8::format_error::format_error": {"offset": "0x107D0"}, "fmt::v8::format_error::~format_error": {"offset": "0x10B70"}, "fmt::v8::sprintf<char [43],std::basic_string_view<char,std::char_traits<char> >,char const *,char>": {"offset": "0x379D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xA0A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<__int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B7D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x9E80"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<long,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B5A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x9D50"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<short,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B490"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x9C60"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<signed char,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B360"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xA0A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<unsigned __int64,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B7D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<fmt::v8::appender,char> >,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0x9F70"}, "fmt::v8::visit_format_arg<fmt::v8::detail::arg_converter<void,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B6A0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<fmt::v8::appender,char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xA1D0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_arg_formatter<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1B910"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xAD30"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_precision_handler,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1C3C0"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<char>,fmt::v8::basic_printf_context<fmt::v8::appender,char> >": {"offset": "0xA760"}, "fmt::v8::visit_format_arg<fmt::v8::detail::printf_width_handler<wchar_t>,fmt::v8::basic_printf_context<std::back_insert_iterator<fmt::v8::detail::buffer<wchar_t> >,wchar_t> >": {"offset": "0x1BE60"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<char>,char>": {"offset": "0x1D020"}, "fmt::v8::vsprintf<fmt::v8::basic_string_view<wchar_t>,wchar_t>": {"offset": "0x1D140"}, "fmt::v8::vsprintf<std::basic_string_view<char,std::char_traits<char> >,char>": {"offset": "0x38270"}, "fprintf": {"offset": "0x148F0"}, "fwEvent<>::ConnectInternal": {"offset": "0x25C00"}, "fwPlatformString::fwPlatformString": {"offset": "0x21D40"}, "fwPlatformString::~fwPlatformString": {"offset": "0x10B90"}, "fwRefContainer<PathFilteringDevice>::~fwRefContainer<PathFilteringDevice>": {"offset": "0x22480"}, "fwRefContainer<vfs::Device>::~fwRefContainer<vfs::Device>": {"offset": "0x22480"}, "fwRefCountable::AddRef": {"offset": "0x39280"}, "fwRefCountable::Release": {"offset": "0x39290"}, "fwRefCountable::~fwRefCountable": {"offset": "0x39270"}, "fx::client::GetPureLevel": {"offset": "0x26500"}, "hde64_disasm": {"offset": "0x3B720"}, "hook::AllocateFunctionStub": {"offset": "0x39890"}, "hook::TransformPattern": {"offset": "0x3A220"}, "hook::details::StubInitFunction::Run": {"offset": "0x28DA0"}, "hook::get_pattern<char,34>": {"offset": "0x27CC0"}, "hook::get_pattern<int,32>": {"offset": "0x27CC0"}, "hook::get_pattern<unsigned int,45>": {"offset": "0x27CC0"}, "hook::get_pattern<void,18>": {"offset": "0x27CC0"}, "hook::get_pattern<void,21>": {"offset": "0x27CC0"}, "hook::get_pattern<void,24>": {"offset": "0x27CC0"}, "hook::get_pattern<void,29>": {"offset": "0x27CC0"}, "hook::get_pattern<void,33>": {"offset": "0x27CC0"}, "hook::get_pattern<void,35>": {"offset": "0x27CC0"}, "hook::get_pattern<void,42>": {"offset": "0x27CC0"}, "hook::get_pattern<void,45>": {"offset": "0x27CC0"}, "hook::pattern::EnsureMatches": {"offset": "0x39BC0"}, "hook::pattern::Initialize": {"offset": "0x39F20"}, "hook::pattern::~pattern": {"offset": "0x27EA0"}, "launch::GetLaunchModeKey": {"offset": "0x26460"}, "launch::IsSDKGuest": {"offset": "0x26730"}, "ossl_ctype_check": {"offset": "0x3C5B0"}, "ossl_strtouint64": {"offset": "0x3C500"}, "ossl_tolower": {"offset": "0x3C5D0"}, "rage::`dynamic initializer for '_popFolder''": {"offset": "0x71F0"}, "rage::`dynamic initializer for '_pushFolder''": {"offset": "0x7230"}, "rage::`dynamic initializer for 'fiDeviceRelative__mount''": {"offset": "0x7740"}, "rage::`dynamic initializer for 'fiDeviceRelative__setPath''": {"offset": "0x7780"}, "rage::`dynamic initializer for 'fiDevice__GetDevice''": {"offset": "0x7480"}, "rage::`dynamic initializer for 'fiDevice__MountGlobal''": {"offset": "0x74C0"}, "rage::`dynamic initializer for 'fiDevice__Unmount''": {"offset": "0x7500"}, "rage::`dynamic initializer for 'fiDevice__Unmount_device''": {"offset": "0x7540"}, "rage::`dynamic initializer for 'fiPackfile__closeArchive''": {"offset": "0x77C0"}, "rage::`dynamic initializer for 'fiPackfile__mount''": {"offset": "0x7840"}, "rage::`dynamic initializer for 'fiPackfile__openArchive''": {"offset": "0x7880"}, "rage::`dynamic initializer for 'hookFunction''": {"offset": "0x78C0"}, "rage::fiAssetManager::GetInstance": {"offset": "0x2DEF0"}, "rage::fiAssetManager::PopFolder": {"offset": "0x2DF00"}, "rage::fiAssetManager::PushFolder": {"offset": "0x2DF10"}, "rage::fiCustomDevice::CloseBulk": {"offset": "0x2E160"}, "rage::fiCustomDevice::CreateLocal": {"offset": "0x2E170"}, "rage::fiCustomDevice::FindClose": {"offset": "0x2E180"}, "rage::fiCustomDevice::FindFirst": {"offset": "0x2E190"}, "rage::fiCustomDevice::FindNext": {"offset": "0x2E1A0"}, "rage::fiCustomDevice::GetCollection": {"offset": "0x2E1B0"}, "rage::fiCustomDevice::GetCollectionId": {"offset": "0x2E1C0"}, "rage::fiCustomDevice::GetFileAttributesW": {"offset": "0x2E1D0"}, "rage::fiCustomDevice::GetFileLength": {"offset": "0x2E1E0"}, "rage::fiCustomDevice::GetFileLengthLong": {"offset": "0x2E1F0"}, "rage::fiCustomDevice::GetFileLengthUInt64": {"offset": "0x2E200"}, "rage::fiCustomDevice::GetResourceVersion": {"offset": "0x2E210"}, "rage::fiCustomDevice::GetUnkDevice": {"offset": "0x2E220"}, "rage::fiCustomDevice::IsCollection": {"offset": "0x2E230"}, "rage::fiCustomDevice::OpenBulk": {"offset": "0x2E240"}, "rage::fiCustomDevice::OpenBulkWrap": {"offset": "0x2E240"}, "rage::fiCustomDevice::ReadBulk": {"offset": "0x2E250"}, "rage::fiCustomDevice::ReadFull": {"offset": "0x2E260"}, "rage::fiCustomDevice::RemoveFile": {"offset": "0x15350"}, "rage::fiCustomDevice::RenameFile": {"offset": "0x14A90"}, "rage::fiCustomDevice::SetFileAttributesW": {"offset": "0x2E270"}, "rage::fiCustomDevice::Truncate": {"offset": "0x15350"}, "rage::fiCustomDevice::WriteBulk": {"offset": "0x2E280"}, "rage::fiCustomDevice::WriteFull": {"offset": "0x2E290"}, "rage::fiCustomDevice::fiCustomDevice": {"offset": "0x2C090"}, "rage::fiCustomDevice::m_40": {"offset": "0x2E2A0"}, "rage::fiCustomDevice::m_addedIn1290": {"offset": "0x2E2B0"}, "rage::fiCustomDevice::m_ax": {"offset": "0x2E2C0"}, "rage::fiCustomDevice::m_xx": {"offset": "0x2E2D0"}, "rage::fiCustomDevice::m_xy": {"offset": "0x2E2E0"}, "rage::fiCustomDevice::m_xz": {"offset": "0x2E2F0"}, "rage::fiCustomDevice::m_yy": {"offset": "0x2E300"}, "rage::fiCustomDevice::m_yz": {"offset": "0x2E310"}, "rage::fiCustomDevice::m_zx": {"offset": "0x2E320"}, "rage::fiCustomDevice::~fiCustomDevice": {"offset": "0x2DF50"}, "rage::fiDevice::GetDevice": {"offset": "0x2E750"}, "rage::fiDevice::MountGlobal": {"offset": "0x2E760"}, "rage::fiDevice::Unmount": {"offset": "0x2E780"}, "rage::fiDevice::fiDevice": {"offset": "0x21B90"}, "rage::fiDevice::~fiDevice": {"offset": "0x10920"}, "rage::fiDeviceImplemented::Close": {"offset": "0x2FB90"}, "rage::fiDeviceImplemented::CloseBulk": {"offset": "0x2FC10"}, "rage::fiDeviceImplemented::Create": {"offset": "0x2FC90"}, "rage::fiDeviceImplemented::CreateLocal": {"offset": "0x2FD90"}, "rage::fiDeviceImplemented::FindClose": {"offset": "0x2FE10"}, "rage::fiDeviceImplemented::FindFirst": {"offset": "0x2FE90"}, "rage::fiDeviceImplemented::FindNext": {"offset": "0x2FF10"}, "rage::fiDeviceImplemented::GetCollection": {"offset": "0x2FF90"}, "rage::fiDeviceImplemented::GetCollectionId": {"offset": "0x30010"}, "rage::fiDeviceImplemented::GetFileAttributesW": {"offset": "0x30090"}, "rage::fiDeviceImplemented::GetFileLength": {"offset": "0x30110"}, "rage::fiDeviceImplemented::GetFileLengthLong": {"offset": "0x30190"}, "rage::fiDeviceImplemented::GetFileLengthUInt64": {"offset": "0x30210"}, "rage::fiDeviceImplemented::GetFileTime": {"offset": "0x30290"}, "rage::fiDeviceImplemented::GetName": {"offset": "0x30310"}, "rage::fiDeviceImplemented::IsCollection": {"offset": "0x30390"}, "rage::fiDeviceImplemented::Open": {"offset": "0x30410"}, "rage::fiDeviceImplemented::OpenBulk": {"offset": "0x30490"}, "rage::fiDeviceImplemented::Read": {"offset": "0x30510"}, "rage::fiDeviceImplemented::ReadBulk": {"offset": "0x30590"}, "rage::fiDeviceImplemented::RemoveFile": {"offset": "0x30690"}, "rage::fiDeviceImplemented::RenameFile": {"offset": "0x30710"}, "rage::fiDeviceImplemented::Seek": {"offset": "0x30790"}, "rage::fiDeviceImplemented::SetFileAttributesW": {"offset": "0x30810"}, "rage::fiDeviceImplemented::SetFileTime": {"offset": "0x30890"}, "rage::fiDeviceImplemented::Truncate": {"offset": "0x30910"}, "rage::fiDeviceImplemented::Write": {"offset": "0x30990"}, "rage::fiDeviceImplemented::WriteBulk": {"offset": "0x30A10"}, "rage::fiDeviceImplemented::fiDeviceImplemented": {"offset": "0x21B90"}, "rage::fiDeviceImplemented::m_40": {"offset": "0x30A90"}, "rage::fiDeviceImplemented::m_addedIn1290": {"offset": "0x30B10"}, "rage::fiDeviceImplemented::m_ax": {"offset": "0x30B90"}, "rage::fiDeviceImplemented::m_xx": {"offset": "0x10920"}, "rage::fiDeviceImplemented::m_xy": {"offset": "0x30C10"}, "rage::fiDeviceImplemented::m_xz": {"offset": "0x30C90"}, "rage::fiDeviceImplemented::m_yx": {"offset": "0x30D10"}, "rage::fiDeviceImplemented::m_yy": {"offset": "0x30D90"}, "rage::fiDeviceImplemented::m_yz": {"offset": "0x30E10"}, "rage::fiDeviceImplemented::m_zx": {"offset": "0x30E90"}, "rage::fiDeviceImplemented::~fiDeviceImplemented": {"offset": "0x2DF50"}, "rage::fiDeviceRelative::Mount": {"offset": "0x2FB40"}, "rage::fiDeviceRelative::SetPath": {"offset": "0x267E0"}, "rage::fiDeviceRelative::fiDeviceRelative": {"offset": "0x21BA0"}, "rage::fiDeviceRelative::~fiDeviceRelative": {"offset": "0x227F0"}, "rage::fiEncryptingDevice::AllocKeyState": {"offset": "0x2FAC0"}, "rage::fiEncryptingDevice::FreeKeyState": {"offset": "0x2FB10"}, "rage::fiEncryptingDevice::fiEncryptingDevice": {"offset": "0x21C20"}, "rage::fiEncryptingDevice::~fiEncryptingDevice": {"offset": "0x227F0"}, "rage::fiPackfile::ClosePackfile": {"offset": "0x2FB00"}, "rage::fiPackfile::Mount": {"offset": "0x2FB50"}, "rage::fiPackfile::OpenPackfile": {"offset": "0x267D0"}, "rage::fiPackfile::fiPackfile": {"offset": "0x21CB0"}, "rage::fiPackfile::~fiPackfile": {"offset": "0x227F0"}, "rapidjson::GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>::~GenericDocument<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>,rapidjson::CrtAllocator>": {"offset": "0x10850"}, "rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>::~GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>": {"offset": "0x108F0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::Accept<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0> >": {"offset": "0x8160"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::AddMember": {"offset": "0x119C0"}, "rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >::~GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator> >": {"offset": "0x10920"}, "rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>::Malloc": {"offset": "0x12B10"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Prefix": {"offset": "0x12BD0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::String": {"offset": "0x12E40"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::WriteString": {"offset": "0x132E0"}, "rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::~Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>": {"offset": "0x10930"}, "rapidjson::internal::DigitGen": {"offset": "0x11C70"}, "rapidjson::internal::Grisu2": {"offset": "0x12720"}, "rapidjson::internal::Prettify": {"offset": "0x12C80"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Push<rapidjson::Writer<rapidjson::GenericStringBuffer<rapidjson::UTF8<char>,rapidjson::CrtAllocator>,rapidjson::UTF8<char>,rapidjson::UTF8<char>,rapidjson::CrtAllocator,0>::Level>": {"offset": "0x8BF0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::Reserve<char>": {"offset": "0x8CC0"}, "rapidjson::internal::Stack<rapidjson::CrtAllocator>::~Stack<rapidjson::CrtAllocator>": {"offset": "0x108F0"}, "rapidjson::internal::WriteExponent": {"offset": "0x13250"}, "rapidjson::internal::u32toa": {"offset": "0x13DB0"}, "rapidjson::internal::u64toa": {"offset": "0x14020"}, "se_handler": {"offset": "0x60E0"}, "sha256_block_data_order": {"offset": "0x1500"}, "sha256_block_data_order_avx": {"offset": "0x3D00"}, "sha256_block_data_order_avx2": {"offset": "0x4C80"}, "sha256_block_data_order_shaext": {"offset": "0x2A00"}, "sha256_block_data_order_ssse3": {"offset": "0x2DC0"}, "shaext_handler": {"offset": "0x6230"}, "someFunc": {"offset": "0x28090"}, "std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x22010"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> >,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> >,void *> > >": {"offset": "0x22030"}, "std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x10960"}, "std::_Codecvt_do_length<std::codecvt_utf8_utf16<wchar_t,1114111,0>,char,_Mbstatet>": {"offset": "0x17550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[10],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[11],std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[11],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[12],std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[12],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[14],std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[14],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[15],fwRefContainer<PathFilteringDevice> >": {"offset": "0x19870"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[17],fwRefContainer<PathFilteringDevice> >": {"offset": "0x19870"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[18],fwRefContainer<PathFilteringDevice> >": {"offset": "0x19870"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[6],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[6],std::basic_string<char,std::char_traits<char>,std::allocator<char> >,bool>": {"offset": "0x196E0"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[9],std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>": {"offset": "0x19550"}, "std::_Default_allocator_traits<std::allocator<RelativeRedirection> >::construct<RelativeRedirection,char const (&)[9],std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x19550"}, "std::_Destroy_range<std::allocator<RelativeRedirection> >": {"offset": "0x178D0"}, "std::_Destroy_range<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2BA00"}, "std::_Facet_Register": {"offset": "0x3C898"}, "std::_Func_class<bool>::~_Func_class<bool>": {"offset": "0x22050"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Copy": {"offset": "0x2B9A0"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Delete_this": {"offset": "0x16B90"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Do_call": {"offset": "0x2B9C0"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Get": {"offset": "0x16B80"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Move": {"offset": "0x2B9A0"}, "std::_Func_impl_no_alloc<<lambda_8f598eed4e1adc328d330335872b9c5a>,bool>::_Target_type": {"offset": "0x2B9E0"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Copy": {"offset": "0x16B30"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Delete_this": {"offset": "0x16B90"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Do_call": {"offset": "0x16B50"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Get": {"offset": "0x16B80"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Move": {"offset": "0x16B30"}, "std::_Func_impl_no_alloc<<lambda_d0420b91619b4445d99f1de24b8b8b9e>,bool>::_Target_type": {"offset": "0x16B70"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Copy": {"offset": "0x2E790"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Delete_this": {"offset": "0x16B90"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Do_call": {"offset": "0x2E7B0"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Get": {"offset": "0x16B80"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Move": {"offset": "0x2E790"}, "std::_Func_impl_no_alloc<<lambda_fe9e7b1f3d27d69bed0c7763c3bd9fa1>,bool>::_Target_type": {"offset": "0x2E7D0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Find_last<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >": {"offset": "0x18180"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Forced_rehash": {"offset": "0x269F0"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::_Insert_range_unchecked<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const *,std::basic_string<char,std::char_traits<char>,std::allocator<char> > const *>": {"offset": "0x18690"}, "std::_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >::~_Hash<std::_Uset_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,0> >": {"offset": "0x22080"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::_Assign_grow": {"offset": "0x267F0"}, "std::_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >::~_Hash_vec<std::allocator<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> > >": {"offset": "0x22100"}, "std::_Insert_string<char,std::char_traits<char>,unsigned __int64>": {"offset": "0x189B0"}, "std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *>::_Free_non_head<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x18320"}, "std::_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >::~_List_node_emplace_op2<std::allocator<std::_List_node<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,void *> > >": {"offset": "0x22160"}, "std::_Maklocstr<char>": {"offset": "0x14940"}, "std::_Maklocstr<wchar_t>": {"offset": "0x31300"}, "std::_String_val<std::_Simple_types<char> >::_Xran": {"offset": "0x26EE0"}, "std::_String_val<std::_Simple_types<wchar_t> >::_Xran": {"offset": "0x26EE0"}, "std::_Throw_bad_array_new_length": {"offset": "0x13680"}, "std::_Throw_bad_cast": {"offset": "0x26E50"}, "std::_Throw_range_error": {"offset": "0x26E70"}, "std::_Throw_tree_length_error": {"offset": "0x136A0"}, "std::_Tidy_guard<std::numpunct<char> >::~_Tidy_guard<std::numpunct<char> >": {"offset": "0x34B30"}, "std::_Tidy_guard<std::numpunct<wchar_t> >::~_Tidy_guard<std::numpunct<wchar_t> >": {"offset": "0x34B30"}, "std::_Traits_find<std::char_traits<char> >": {"offset": "0x2BC60"}, "std::_Tree<std::_Tmap_traits<boost::exception_detail::type_info_,boost::shared_ptr<boost::exception_detail::error_info_base>,std::less<boost::exception_detail::type_info_>,std::allocator<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> > >,0> >::_Find_lower_bound<boost::exception_detail::type_info_>": {"offset": "0x18250"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_hint<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x8E90"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::_Find_lower_bound<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >": {"offset": "0x9110"}, "std::_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >::~_Tree<std::_Tmap_traits<std::pair<std::basic_string_view<char,std::char_traits<char> >,int>,xbr::GameBuildUniquifier,std::less<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> >,std::allocator<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> >,0> >": {"offset": "0x10980"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> >,void *> > >": {"offset": "0x180D0"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<boost::exception_detail::type_info_ const ,boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_Insert_node": {"offset": "0x13420"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier>,void *> > >": {"offset": "0x8E30"}, "std::_Tree_val<std::_Tree_simple_types<std::pair<std::pair<std::basic_string_view<char,std::char_traits<char> >,int> const ,xbr::GameBuildUniquifier> > >::_Insert_node": {"offset": "0x13420"}, "std::_Uninitialized_backout_al<std::allocator<RelativeRedirection> >::~_Uninitialized_backout_al<std::allocator<RelativeRedirection> >": {"offset": "0x22260"}, "std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x2C0C0"}, "std::_Uninitialized_copy<RelativeRedirection *,RelativeRedirection *,std::allocator<RelativeRedirection> >": {"offset": "0x19290"}, "std::_Uninitialized_move<RelativeRedirection *,std::allocator<RelativeRedirection> >": {"offset": "0x19350"}, "std::_Xlen_string": {"offset": "0x136C0"}, "std::allocator<RelativeRedirection>::allocate": {"offset": "0x26F00"}, "std::allocator<RelativeRedirection>::deallocate": {"offset": "0x27100"}, "std::allocator<char>::allocate": {"offset": "0x136E0"}, "std::allocator<char>::deallocate": {"offset": "0x270C0"}, "std::allocator<hook::pattern_match>::allocate": {"offset": "0x3A460"}, "std::allocator<hook::pattern_match>::deallocate": {"offset": "0x3A4D0"}, "std::allocator<unsigned __int64>::deallocate": {"offset": "0x3A4D0"}, "std::allocator<unsigned char>::deallocate": {"offset": "0x270C0"}, "std::allocator<wchar_t>::allocate": {"offset": "0x13740"}, "std::allocator<wchar_t>::deallocate": {"offset": "0x27140"}, "std::bad_alloc::bad_alloc": {"offset": "0x3DC1C"}, "std::bad_alloc::~bad_alloc": {"offset": "0x10B70"}, "std::bad_array_new_length::bad_array_new_length": {"offset": "0x10760"}, "std::bad_array_new_length::~bad_array_new_length": {"offset": "0x10B70"}, "std::bad_cast::bad_cast": {"offset": "0x21AD0"}, "std::bad_cast::~bad_cast": {"offset": "0x10B70"}, "std::basic_ostream<char,std::char_traits<char> >::_Sentry_base::~_Sentry_base": {"offset": "0x22730"}, "std::basic_ostream<char,std::char_traits<char> >::sentry::~sentry": {"offset": "0x22830"}, "std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >::str": {"offset": "0x27AE0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>": {"offset": "0x8D70"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_319d5e083f45f90dcdce5dce53cbb275>,char>": {"offset": "0x18C20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_55b0f89b53a0342680e40398a0d39887>,unsigned __int64,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x2BA90"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>": {"offset": "0x18D90"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_9013ee9e23efe4882b67eff5b0ecf103> >": {"offset": "0x371A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_b986da8d428e4af07c64af60eec09b61>,unsigned __int64,unsigned __int64,char>": {"offset": "0x18F20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_f3a66ab6a0570788f31503db83886f49>,unsigned __int64,char const *,unsigned __int64>": {"offset": "0x190E0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate": {"offset": "0x10A40"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append": {"offset": "0x26F70"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign": {"offset": "0x13930"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x213A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert": {"offset": "0x279B0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::insert<std::_Deque_iterator<std::_Deque_val<std::_Deque_simple_types<char> > >,0>": {"offset": "0x1A2A0"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::replace": {"offset": "0x2DC20"}, "std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x10A40"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>": {"offset": "0x17800"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>": {"offset": "0x28EE0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate": {"offset": "0x10AA0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append": {"offset": "0x137B0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign": {"offset": "0x2B360"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x214D0"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::insert": {"offset": "0x2B490"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::push_back": {"offset": "0x2B620"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::reserve": {"offset": "0x39090"}, "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >": {"offset": "0x10AA0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::overflow": {"offset": "0x15F50"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::pbackfail": {"offset": "0x160C0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekoff": {"offset": "0x16220"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::seekpos": {"offset": "0x163A0"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::underflow": {"offset": "0x16150"}, "std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >::~basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >": {"offset": "0x222D0"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_always_noconv": {"offset": "0x15350"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_encoding": {"offset": "0x14A90"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_in": {"offset": "0x14EF0"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_length": {"offset": "0x15340"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_max_length": {"offset": "0x15360"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_out": {"offset": "0x15170"}, "std::codecvt_utf8_utf16<wchar_t,1114111,0>::do_unshift": {"offset": "0x15330"}, "std::deque<char,std::allocator<char> >::_Emplace_back_internal<char const &>": {"offset": "0x179D0"}, "std::deque<char,std::allocator<char> >::_Growmap": {"offset": "0x26C70"}, "std::deque<char,std::allocator<char> >::_Insert_range<1,char const *,char const *>": {"offset": "0x183C0"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<0>::~_Restore_old_size_guard<0>": {"offset": "0x22220"}, "std::deque<char,std::allocator<char> >::_Restore_old_size_guard<1>::~_Restore_old_size_guard<1>": {"offset": "0x221F0"}, "std::deque<char,std::allocator<char> >::_Xlen": {"offset": "0x26EA0"}, "std::deque<char,std::allocator<char> >::~deque<char,std::allocator<char> >": {"offset": "0x223A0"}, "std::exception::exception": {"offset": "0x10790"}, "std::exception::what": {"offset": "0x14810"}, "std::exception::~exception": {"offset": "0x10B70"}, "std::filesystem::_Find_root_name_end": {"offset": "0x2B210"}, "std::filesystem::_Range_compare": {"offset": "0x2B300"}, "std::filesystem::path::is_absolute": {"offset": "0x2B5A0"}, "std::filesystem::path::~path": {"offset": "0x10B90"}, "std::fill<std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> *,std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::_Iterator_base0> >": {"offset": "0x19AD0"}, "std::function<bool __cdecl(void)>::~function<bool __cdecl(void)>": {"offset": "0x22050"}, "std::list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~list<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >": {"offset": "0x224C0"}, "std::locale::~locale": {"offset": "0x22800"}, "std::numpunct<char>::do_decimal_point": {"offset": "0x354B0"}, "std::numpunct<char>::do_falsename": {"offset": "0x354D0"}, "std::numpunct<char>::do_grouping": {"offset": "0x35550"}, "std::numpunct<char>::do_thousands_sep": {"offset": "0x35590"}, "std::numpunct<char>::do_truename": {"offset": "0x355B0"}, "std::numpunct<char>::numpunct<char>": {"offset": "0x34980"}, "std::numpunct<wchar_t>::_Getcat": {"offset": "0x34D50"}, "std::numpunct<wchar_t>::do_decimal_point": {"offset": "0x354C0"}, "std::numpunct<wchar_t>::do_falsename": {"offset": "0x35510"}, "std::numpunct<wchar_t>::do_grouping": {"offset": "0x35550"}, "std::numpunct<wchar_t>::do_thousands_sep": {"offset": "0x355A0"}, "std::numpunct<wchar_t>::do_truename": {"offset": "0x355F0"}, "std::pair<boost::exception_detail::type_info_,boost::shared_ptr<boost::exception_detail::error_info_base> >::~pair<boost::exception_detail::type_info_,boost::shared_ptr<boost::exception_detail::error_info_base> >": {"offset": "0x224F0"}, "std::range_error::range_error": {"offset": "0x21FA0"}, "std::range_error::~range_error": {"offset": "0x10B70"}, "std::rotate<std::_Deque_unchecked_iterator<std::_Deque_val<std::_Deque_simple_types<char> > > >": {"offset": "0x1A810"}, "std::runtime_error::runtime_error": {"offset": "0x10810"}, "std::shared_ptr<rage::fiDevice>::~shared_ptr<rage::fiDevice>": {"offset": "0x2C0D0"}, "std::unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >::~unique_ptr<fwEvent<>::callback,std::default_delete<fwEvent<>::callback> >": {"offset": "0x225D0"}, "std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >::~unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> >": {"offset": "0x225E0"}, "std::use_facet<std::ctype<char> >": {"offset": "0x1B270"}, "std::use_facet<std::numpunct<char> >": {"offset": "0x34560"}, "std::use_facet<std::numpunct<wchar_t> >": {"offset": "0x346D0"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::from_bytes": {"offset": "0x27180"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >": {"offset": "0x218C0"}, "std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >::~wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,0>,wchar_t,std::allocator<wchar_t>,std::allocator<char> >": {"offset": "0x22690"}, "utf8::exception::exception": {"offset": "0x383C0"}, "utf8::internal::append<std::back_insert_iterator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,char>": {"offset": "0x37440"}, "utf8::internal::validate_next<std::_String_view_iterator<std::char_traits<char> > >": {"offset": "0x37D70"}, "utf8::invalid_code_point::invalid_code_point": {"offset": "0x38450"}, "utf8::invalid_code_point::what": {"offset": "0x39240"}, "utf8::invalid_code_point::~invalid_code_point": {"offset": "0x10B70"}, "utf8::invalid_utf8::invalid_utf8": {"offset": "0x384C0"}, "utf8::invalid_utf8::what": {"offset": "0x39250"}, "utf8::invalid_utf8::~invalid_utf8": {"offset": "0x10B70"}, "utf8::not_enough_room::not_enough_room": {"offset": "0x38520"}, "utf8::not_enough_room::what": {"offset": "0x39260"}, "utf8::not_enough_room::~not_enough_room": {"offset": "0x10B70"}, "va_impl<char,fmt::v8::basic_format_args<fmt::v8::basic_printf_context<fmt::v8::appender,char> > >": {"offset": "0x37B50"}, "vva": {"offset": "0x39220"}, "xbr::GetGameBuild": {"offset": "0x26380"}, "xbr::GetReplaceExecutable": {"offset": "0x2D1A0"}, "xbr::GetReplaceExecutableInit": {"offset": "0x392C0"}, "xbr::GetRequestedGameBuildInit": {"offset": "0x394E0"}, "xbr::`dynamic initializer for 'kGameBuildUniquifiers''": {"offset": "0x7C90"}}}