#include "MemoryUtils.h"
#include <Psapi.h>
#include <iostream>
#include <sstream>
#include <iomanip>

bool MemoryUtils::IsMemoryReadable(uintptr_t address, size_t size) {
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    if (mbi.State != MEM_COMMIT) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) != 0;
}

bool MemoryUtils::IsMemoryWritable(uintptr_t address, size_t size) {
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    if (mbi.State != MEM_COMMIT) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_READWRITE | PAGE_EXECUTE_READWRITE)) != 0;
}

bool MemoryUtils::IsMemoryExecutable(uintptr_t address, size_t size) {
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    if (mbi.State != MEM_COMMIT) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) != 0;
}

bool MemoryUtils::ReadMemoryBytes(uintptr_t address, void* buffer, size_t size) {
    if (!IsMemoryReadable(address, size) || !buffer) {
        return false;
    }
    
    __try {
        memcpy(buffer, reinterpret_cast<void*>(address), size);
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}

std::vector<uint8_t> MemoryUtils::ReadMemoryBytes(uintptr_t address, size_t size) {
    std::vector<uint8_t> result;
    
    if (!IsMemoryReadable(address, size)) {
        return result;
    }
    
    result.resize(size);
    if (ReadMemoryBytes(address, result.data(), size)) {
        return result;
    }
    
    result.clear();
    return result;
}

bool MemoryUtils::WriteMemoryBytes(uintptr_t address, const void* buffer, size_t size) {
    if (!IsMemoryWritable(address, size) || !buffer) {
        return false;
    }
    
    __try {
        memcpy(reinterpret_cast<void*>(address), buffer, size);
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}

HMODULE MemoryUtils::GetModuleByName(const std::string& moduleName) {
    return GetModuleHandleA(moduleName.c_str());
}

uintptr_t MemoryUtils::GetModuleBase(HMODULE hModule) {
    if (!hModule) {
        return 0;
    }
    
    MODULEINFO modInfo;
    if (GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
        return reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
    }
    
    return 0;
}

size_t MemoryUtils::GetModuleSize(HMODULE hModule) {
    if (!hModule) {
        return 0;
    }
    
    MODULEINFO modInfo;
    if (GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
        return modInfo.SizeOfImage;
    }
    
    return 0;
}

std::string MemoryUtils::GetModulePath(HMODULE hModule) {
    char path[MAX_PATH];
    if (GetModuleFileNameA(hModule, path, MAX_PATH)) {
        return std::string(path);
    }
    return "";
}

bool MemoryUtils::IsValidAddress(uintptr_t address) {
    return IsMemoryReadable(address, 1);
}

uintptr_t MemoryUtils::ResolveRelativeAddress(uintptr_t instructionAddress, int offset) {
    if (!IsMemoryReadable(instructionAddress + offset, sizeof(int32_t))) {
        return 0;
    }
    
    int32_t relativeOffset = 0;
    if (!ReadMemory(instructionAddress + offset, relativeOffset)) {
        return 0;
    }
    
    return instructionAddress + offset + sizeof(int32_t) + relativeOffset;
}

std::string MemoryUtils::FormatAddress(uintptr_t address) {
    std::stringstream ss;
    ss << "0x" << std::hex << std::uppercase << address;
    return ss.str();
}

DWORD MemoryUtils::GetCurrentProcessId() {
    return ::GetCurrentProcessId();
}

std::string MemoryUtils::GetProcessName() {
    char path[MAX_PATH];
    if (GetModuleFileNameA(nullptr, path, MAX_PATH)) {
        std::string fullPath(path);
        size_t lastSlash = fullPath.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            return fullPath.substr(lastSlash + 1);
        }
        return fullPath;
    }
    return "";
}

std::vector<HMODULE> MemoryUtils::GetLoadedModules() {
    std::vector<HMODULE> modules;
    HMODULE hMods[1024];
    DWORD cbNeeded;
    
    if (EnumProcessModules(GetCurrentProcess(), hMods, sizeof(hMods), &cbNeeded)) {
        DWORD moduleCount = cbNeeded / sizeof(HMODULE);
        modules.reserve(moduleCount);
        
        for (DWORD i = 0; i < moduleCount; ++i) {
            modules.push_back(hMods[i]);
        }
    }
    
    return modules;
}
